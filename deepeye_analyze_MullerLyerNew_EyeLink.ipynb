{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# import DeepEye analysis functions\n", "from deepeye_analysis_package.preprocessing import getFixationLatency, handle_carryover_fixations_and_merge, addAOI\n", "from deepeye_analysis_package.plotting import plot2d"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create dataframe with 1 saccade per trial, filter saccades, normalize to the right and plot"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Get relevant saccades (one per trial)\n", "def getSaccades(df, fn='', plot=False):  \n", "    \n", "    \n", "    ### Get timestamp of when target was presented and add it to the dataframe ###\n", "\n", "    # 1) get the first time sample when the target is presented\n", "    sampTime_df = df.drop_duplicates(subset=['trialNr'],  keep='first', ignore_index=True)\n", "\n", "    # 2) extract the columns needed\n", "    sampTime_df = sampTime_df[['trialNr', 'sampTime']]\n", "\n", "    # 3) rename the columns so they would be added\n", "    sampTime_df.columns = ['trialNr', 'targSampTime']\n", "\n", "    # 4) Get only fixations start and end events ###\n", "    fix_events_df = df[df['FixStartEnd'].isin(['fix_start', 'fix_end'])]\n", "\n", "    # 5) Merge the target time into the fix_events_df (one time per trial)\n", "    fix_events_df = pd.merge(fix_events_df, sampTime_df, on=\"trialNr\")\n", "\n", "    ### Convert to cm ###\n", "    # Convert coordinates to cm\n", "    scale_px_per_cm = fix_events_df['resX'].unique()[0] / fix_events_df.scrW_cm.unique()[0]\n", "    fix_events_df['scale_px_per_cm'] = scale_px_per_cm\n", "\n", "    fix_events_df['user_pred_px_x_cm'] = fix_events_df.user_pred_px_x/scale_px_per_cm\n", "    fix_events_df['user_pred_px_y_cm'] = fix_events_df.user_pred_px_y/scale_px_per_cm\n", "\n", "    fix_events_df['FixXPos_cm'] = fix_events_df.FixXPos/scale_px_per_cm\n", "    fix_events_df['FixYPos_cm'] = fix_events_df.FixYPos/scale_px_per_cm\n", "    fix_events_df['targetX_cm'] = fix_events_df.targetX/scale_px_per_cm\n", "    fix_events_df['fixationStimY_cm'] = fix_events_df.fixationStimY/scale_px_per_cm\n", "    fix_events_df['fixationStimX_cm'] = fix_events_df.fixationStimX/scale_px_per_cm\n", "    fix_events_df['DistFromPrevFix_cm'] = fix_events_df.DistFromPrevFix/scale_px_per_cm\n", "    \n", "    # Specify criteria for saccades\n", "    # Need to add checking that there is only one sample per trial\n", "    line_length_px = np.abs(fix_events_df.fixationStimX.unique()[0] - fix_events_df.targetX.unique()[0])\n", "    line_length_cm = line_length_px/scale_px_per_cm\n", "    fix_events_df['line_length_cm'] = line_length_cm\n", "\n", "    SACC_SIZE_CM = line_length_cm * 0.5\n", "    SACC_DIST2TART_CM = line_length_cm * 0.5\n", "    SACC_DIST2FIX_CM = line_length_cm * 0.5\n", "\n", "    ### Filter the saccades (should end up with max one saccade per trial) ###\n", "\n", "    # 1) select only rows where saccade is large\n", "    summary_df = fix_events_df[fix_events_df.DistFromPrevFix_cm > SACC_SIZE_CM]\n", "\n", "    # 2) select only rows where the distance between saccade endpoint and target is small\n", "    summary_df['DistEndpoint2Targ_cm'] = summary_df.apply(lambda row: (np.linalg.norm(np.array([row.FixXPos, row.FixYPos]) - np.array([row.targetX, row.fixationStimY]))/scale_px_per_cm), axis=1)\n", "    summary_df = summary_df[summary_df.DistEndpoint2Targ_cm < SACC_DIST2TART_CM]\n", "\n", "    # 3) select only rows where the distance between saccade startpoint and fixation point is small\n", "    summary_df['DistStartpoint2FixPoint_cm'] = summary_df.apply(lambda row: (np.linalg.norm(np.array([row.PrevFixXPos, row.PrevFixYPos]) - np.array([row.fixationStimX, row.fixationStimY]))/scale_px_per_cm), axis=1)\n", "    summary_df = summary_df[summary_df.DistStartpoint2FixPoint_cm < SACC_DIST2FIX_CM]\n", "\n", "    # 4) select based on Saccade Latency \n", "    # Note: Saccade Latency can be negative if the fixation end was not present on the current trial preceding the fixation start\n", "    summary_df['SaccLat'] = summary_df.PrevFixSampTime - summary_df.targSampTime\n", "    # Filter out the trials based on the saccade latency\n", "    summary_df = summary_df[(summary_df.SaccLat > 80) & (summary_df.SaccLat < 600)]\n", "    \n", "    ### Normalize the endpoints to the target on the right ###\n", "    summary_df['NormFixXPos'] = summary_df.apply(\n", "        lambda row: row.resX - row.FixXPos if row.target == 'left' else row.FixXPos,\n", "        axis=1\n", "    )\n", "\n", "    ### Normalize the target position to the target on the right ###\n", "    summary_df['NormtargetX'] = summary_df.apply(\n", "        lambda row: row.resX - row.targetX if row.target == 'left' else row.targetX,\n", "        axis=1\n", "    )\n", "\n", "    ### Normalize the fixation point to the target on the right ###\n", "    summary_df['NormfixationStimX'] = summary_df.apply(\n", "        lambda row: row.resX - row.fixationStimX if row.target == 'left' else row.fixationStimX,\n", "        axis=1\n", "    )\n", "\n", "    # Calculate the mean saccade endpoint for the two conditions\n", "    out = summary_df[summary_df.condition == 'arrowHeadsOutward']\n", "    inw = summary_df[summary_df.condition == 'arrowHeadsInward']\n", "\n", "    # Print summary per person\n", "    print(f'Mean Saccade Latency: {summary_df.SaccLat.mean()}')\n", "    print(f'Number of trials: {summary_df.shape[0]}')\n", "    outX = out.NormFixXPos.mean()\n", "    inwX = inw.NormFixXPos.mean()\n", "    print(f'Out: {outX}')\n", "    print(f'Inw: {inwX}')\n", "\n", "    if np.isnan(outX) or np.isnan(inwX):\n", "        print(f'Warning: at least one of the conditions has no saccades')\n", "\n", "    if not (np.isnan(outX) or np.isnan(inwX)):\n", "       \n", "        if plot == True:\n", "            ### Plot the saccades endpoints ###\n", "            plt.figure()\n", "            plt.title(f'{fn}') \n", "            plt.xlim(0, out.resX.unique()[0])\n", "            plt.ylim(0, out.resY.unique()[0])\n", "            plt.gca().invert_yaxis()\n", "\n", "\n", "            out_hndl = plt.scatter(out.NormFixXPos, out.FixYPos, c='blue', alpha=0.5, edgecolors='black')        \n", "            inw_hndl = plt.scatter(inw.NormFixXPos, inw.FixYPos, c='orange', alpha=0.5, edgecolors='black') \n", "\n", "            fixp_hndl = plt.scatter(out.NormfixationStimX, out.fixationStimY, c='red')\n", "            targ_hndl = plt.scatter(out.NormtargetX, out.fixationStimY, c='green')\n", "\n", "            plt.legend((out_hndl, inw_hndl, targ_hndl, fixp_hndl), (f'<-> outward arrowheads {np.round(outX,1)}px', f'>-< inward arrowheads {np.round(inwX,1)}px', 'target', 'fixP'), scatterpoints=1)\n", "            plt.xlabel('Horizontal eye position (pixels)')\n", "            plt.ylabel('Vertical eye position (pixels)')       \n", "\n", "            # Plot the histogram of saccade latencies\n", "            plt.figure()\n", "            plt.title(f'{fn}')\n", "            h = plt.hist(summary_df.SaccLat)\n", "            plt.xlabel('Saccade Latency (ms)')\n", "\n", "    return summary_df\n", "   \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Specify the Experiment version and paths for DATA and ANALYSIS\n", "- *MullerLyer_lab* - experiment ran in the lab\n", "- *MullerLyer_online* - experiment ran online via Prolific "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = './CollectedData/complete'\n", "path_to_data = os.path.join(path, 'data')\n", "path_to_analysis = os.path.join(path, 'analysis')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Split the dataset into AccuracyPrecision dataset and Muller-Lyer dataset"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Get all folder names from the data directory\n", "folder_names = [name for name in os.listdir(path_to_data) if os.path.isdir(os.path.join(path_to_data, name))]\n", "\n", "# Process each participant's data\n", "for fn in folder_names:\n", "\n", "    # Check if the file already exists to avoid reprocessing\n", "    if not os.path.exists(os.path.join(path_to_data, fn, f'{fn}_mullerlyer_record.csv') ):\n", "        path_to_file = os.path.join(path_to_data, fn, f'{fn}_record.csv')   \n", "\n", "        # 1. Read all lines\n", "        with open(path_to_file, 'r') as f:\n", "            lines = f.readlines()\n", "\n", "        # 2. Find the first line that contains a unique second‐header token\n", "        split_idx = next(i for i, line in enumerate(lines)\n", "                        if \"fixationStim\" in line)\n", "\n", "        # 3. Slice into two chunks: header1+data1, header2+data2\n", "        hdr1, data1 = lines[0], lines[1:split_idx]\n", "        hdr2, data2 = lines[split_idx], lines[split_idx+1:]\n", "\n", "        # 4. Build mini‐CSVs in memory and parse\n", "        df1 = pd.read_csv(io.StringIO(hdr1 + \"\".join(data1)), sep=',')\n", "        df2 = pd.read_csv(io.StringIO(hdr2 + \"\".join(data2)), sep=',')\n", "\n", "        df1.to_csv(os.path.join(path_to_data, fn, f'{fn}_accprec_record.csv'), index=False)\n", "        df2.to_csv(os.path.join(path_to_data, fn, f'{fn}_mullerlyer_record.csv'), index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Main Part for <PERSON> Lyer dataset"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting fixations for participant 2025_03_27_10_28_10...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\FixationDetection\\I2MC.py:52: FutureWarning: errors='ignore' is deprecated and will raise in a future version. Use to_numeric without passing `errors` and catch exceptions explicitly instead\n", "  origData = origData.apply(pd.to_numeric, errors='ignore')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\n", "Importing and processing: \"C:/Users/<USER>/Dropbox/Appliedwork/CognitiveSolutions/Projects/DeepEye/TechnicalReports/TechnicalReport1/Test_MullerLyer/<PERSON>L<PERSON>_eyelink/complete\\data\\2025_03_27_10_28_10\\2025_03_27_10_28_10_mullerlyer_record.csv\"\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\FixationDetection\\import_funcs.py:124: FutureWarning: errors='ignore' is deprecated and will raise in a future version. Use to_numeric without passing `errors` and catch exceptions explicitly instead\n", "  data = data.apply(pd.to_numeric, errors='ignore')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\tSearching for valid interpolation windows\n", "\tReplace interpolation windows with Steffen interpolation\n", "\t2-Means clustering started for averaged signal\n", "\tDetermining fixations based on clustering weight mean for averaged signal and separate eyes + 2*std\n", "\n", "\n", "I2MC took 5.671844959259033s to finish!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\deepeye_analyze\\lib\\site-packages\\numpy\\_core\\fromnumeric.py:3904: RuntimeWarning: Mean of empty slice.\n", "  return _methods._mean(a, axis=axis, dtype=dtype,\n", "c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\deepeye_analyze\\lib\\site-packages\\numpy\\_core\\_methods.py:147: RuntimeWarning: invalid value encountered in scalar divide\n", "  ret = ret.dtype.type(ret / rcount)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing participant 2025_03_27_10_28_10...\n", "Mean Saccade Latency: 206.54130434781774\n", "Number of trials: 92\n", "Out: 1176.6818464305031\n", "Inw: 1188.8441935823005\n", "Combined data saved to C:/Users/<USER>/Dropbox/Appliedwork/CognitiveSolutions/Projects/DeepEye/TechnicalReports/TechnicalReport1/Test_MullerLyer/MullerLyer_eyelink/complete\\analysis\\allSubjects_ML.csv\n", "Combined summary data saved to C:/Users/<USER>/Dropbox/Appliedwork/CognitiveSolutions/Projects/DeepEye/TechnicalReports/TechnicalReport1/Test_MullerLyer/<PERSON><PERSON><PERSON>_eyelink/complete\\analysis\\allSubjects_ML_summary.csv\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21428\\4239998295.py:53: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  summary_df['DistEndpoint2Targ_cm'] = summary_df.apply(lambda row: (np.linalg.norm(np.array([row.FixXPos, row.FixYPos]) - np.array([row.targetX, row.fixationStimY]))/scale_px_per_cm), axis=1)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deepeye_analysis_package.getFixations import extract_fixations\n", "\n", "# Initialize an empty list to hold the processed dataframes\n", "output_dfs = []\n", "summary_dfs = []\n", "\n", "# Get all folder names from the data directory\n", "folder_names = [name for name in os.listdir(path_to_data) if os.path.isdir(os.path.join(path_to_data, name))]\n", "\n", "# Process each participant's data\n", "for fn in folder_names:\n", "    # Check if _record_extra.csv exists, if not use _record.csv\n", "    path_to_file = os.path.join(path_to_data, fn, f'{fn}_mullerlyer_record_extra.csv')\n", "    if not os.path.exists(path_to_file):\n", "        path_to_file = os.path.join(path_to_data, fn, f'{fn}_mullerlyer_record.csv')\n", "\n", "        print(f'Extracting fixations for participant {fn}...')\n", "\n", "        try:\n", "            df = pd.read_csv(path_to_file, on_bad_lines='skip')\n", "            df = extract_fixations(df, path_to_file)\n", "            df = df.drop_duplicates(subset=['user_pred_px_x', 'user_pred_px_y'], ignore_index=True)  # Ensure unique coordinates (sometimes webcam is stuck)\n", "            \n", "        except FileNotFoundError:\n", "            print(f'File does not exist: {path_to_file}')\n", "            continue    \n", "    else: \n", "        try:\n", "            df = pd.read_csv(path_to_file, on_bad_lines='skip')\n", "            df = df.drop_duplicates(subset=['user_pred_px_x', 'user_pred_px_y'], ignore_index=True)  # Ensure unique coordinates (sometimes webcam is stuck)\n", "\n", "        except FileNotFoundError:\n", "            print(f'File does not exist: {path_to_file}')\n", "            continue\n", "\n", "    print(f'Processing participant {fn}...')\n", "    # Filter data to only include rows where the target was presented\n", "    df1 = df[df['event'] == 'target_on'].copy()\n", "\n", "    # Add subject ID to the dataframe\n", "    df1['deepeye_id'] = fn\n", "\n", "    ### Convert critical columns to numeric values if they are not ###\n", "    df1['FixXPos'] = pd.to_numeric(df1['FixXPos'], errors='coerce')\n", "    df1['FixYPos'] = pd.to_numeric(df1['FixYPos'], errors='coerce')\n", "    df1['targetX'] = pd.to_numeric(df1['targetX'], errors='coerce')\n", "    df1['fixationStimY'] = pd.to_numeric(df1['fixationStimY'], errors='coerce')\n", "    df1['fixationStimX'] = pd.to_numeric(df1['fixationStimX'], errors='coerce')\n", "    df1['resX'] = pd.to_numeric(df1['resX'], errors='coerce')\n", "    df1['resY'] = pd.to_numeric(df1['resY'], errors='coerce')\n", "    df1['scrW_cm'] = pd.to_numeric(df1['scrW_cm'], errors='coerce')\n", "    df1['PrevFixXPos'] = pd.to_numeric(df1['PrevFixXPos'], errors='coerce')\n", "    df1['PrevFixYPos'] = pd.to_numeric(df1['PrevFixYPos'], errors='coerce')\n", "    df1['PrevFixSampTime'] = pd.to_numeric(df1['PrevFixSampTime'], errors='coerce')\n", "    df1['DistFromPrevFix'] = pd.to_numeric(df1['DistFromPrevFix'], errors='coerce')\n", "\n", "    # Get saccade endpoint data and latency\n", "    summary_df = getSaccades(df1, fn, plot=True)  \n", "    summary_dfs.append(summary_df)  \n", "\n", "    # Accumulate the processed dataframe for this participant\n", "    output_dfs.append(df1)\n", "\n", "# Concatenate all participants' data into one DataFrame\n", "if output_dfs:\n", "    output_df = pd.concat(output_dfs, ignore_index=True)\n", "    output_file = os.path.join(path_to_analysis, 'allSubjects_ML.csv')\n", "    output_df.to_csv(output_file, index=False)\n", "    print(f'Combined data saved to {output_file}')\n", "else:\n", "    print('No data was processed.')\n", "\n", "# Concatenate all participants' summary data into one DataFrame\n", "if summary_dfs:\n", "    summary_df = pd.concat(summary_dfs, ignore_index=True)\n", "    summary_file = os.path.join(path_to_analysis, 'allSubjects_ML_summary.csv')\n", "    summary_df.to_csv(summary_file, index=False)\n", "    print(f'Combined summary data saved to {summary_file}')\n", "else:\n", "    print('No summary data was processed.')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter out subjects with too few datapoints in outward or inward condition"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           condition           deepeye_id  counts\n", "0   arrowHeadsInward  2025_03_27_10_28_10      45\n", "1  arrowHeadsOutward  2025_03_27_10_28_10      47\n", "\n", "Excluded subjects based on too few valid trials: []\n"]}], "source": ["# Count the number of data points per 'deepeye_id' and 'condition'\n", "count_df = summary_df.groupby(['condition', 'deepeye_id']).size().reset_index(name='counts')\n", "print(count_df)\n", "\n", "# Set a threshold for the minimum number of data points per condition\n", "min_data_points = 5  # Adjust this threshold as needed\n", "\n", "# Filter out deepeye_ids that don't meet the minimum number of data points\n", "invalid_deepeye_ids = count_df[count_df['counts'] <= min_data_points]['deepeye_id'].unique()\n", "\n", "print(f'\\nExcluded subjects based on too few valid trials: {invalid_deepeye_ids}')\n", "\n", "# Filter the original DataFrame to exclude the invalid deepeye_ids\n", "filtered_summary_df = summary_df[~summary_df['deepeye_id'].isin(invalid_deepeye_ids)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze Illusion Size Per Participant"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["condition           deepeye_id  arrowHeadsInward  arrowHeadsOutward  \\\n", "0          2025_03_27_10_28_10       1188.844194        1176.681846   \n", "\n", "condition  IllusionSize  IllusionSize_percent  \n", "0             12.162347              3.118551  \n", "T-test: t=nan, p=nan\n", "Mean Illusion_%: 3.1185505517430263\n", "Mean line_length_cm: 10.231481481481481\n", "Number of participants: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\deepeye_analyze\\lib\\site-packages\\scipy\\stats\\_stats_py.py:1114: RuntimeWarning: divide by zero encountered in divide\n", "  var *= np.divide(n, n-ddof)  # to avoid error on division by zero\n", "c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\deepeye_analyze\\lib\\site-packages\\scipy\\stats\\_stats_py.py:1114: RuntimeWarning: invalid value encountered in scalar multiply\n", "  var *= np.divide(n, n-ddof)  # to avoid error on division by zero\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Summary statistics for the saccade endpoints\n", "illusSize_df = filtered_summary_df.groupby(['condition', 'deepeye_id', 'pp_id',\n", "                                        'scale_px_per_cm', 'line_length_cm',\n", "                                        'NormtargetX', 'NormfixationStimX',\n", "                                        'fixationStimY'])[['NormFixXPos', 'FixYPos']].mean().reset_index()\n", "# print(illusSize_df)\n", "# Extract constant per participant\n", "df_a = filtered_summary_df.groupby(['deepeye_id', 'scale_px_per_cm',\n", "                                    'line_length_cm', 'NormtargetX',\n", "                                    'NormfixationStimX','fixationStimY'])[['NormFixXPos']].mean().reset_index()\n", "# print(df_a)\n", "a = df_a.scale_px_per_cm\n", "b = df_a.line_length_cm\n", "# c = df_a.<PERSON>targetX\n", "# d = df_a.fixationStimY\n", "# e = df_a.NormfixationStimX\n", "\n", "# Pivot the DataFrame to have conditions as columns\n", "illusSize_df_FixXPos = illusSize_df.pivot(index='deepeye_id', columns='condition', values='NormFixXPos').reset_index()\n", "illusSize_df_FixXPos['IllusionSize'] = illusSize_df_FixXPos.arrowHeadsInward - illusSize_df_FixXPos.arrowHeadsOutward\n", "illusSize_df_FixXPos['IllusionSize_percent'] = illusSize_df_FixXPos['IllusionSize']/a / b * 100.0\n", "\n", "# Drop subjects with NaN values\n", "illusSize_df_FixXPos = illusSize_df_FixXPos.dropna()\n", "print(illusSize_df_FixXPos)\n", "\n", "# Plot the illusion size\n", "plt.figure()\n", "plt.title('Illusion Size')\n", "plt.ylabel('Inward - Outward (% length)')\n", "sns.boxplot(illusSize_df_FixXPos.IllusionSize_percent)\n", "\n", "# T-test\n", "from scipy.stats import ttest_rel\n", "t, p = ttest_rel(illusSize_df_FixXPos.arrowHeadsInward, illusSize_df_FixXPos.arrowHeadsOutward)\n", "print(f'T-test: t={t}, p={p}')\n", "\n", "print(f'Mean Illusion_%: {illusSize_df_FixXPos.IllusionSize_percent.mean()}')\n", "print(f'Mean line_length_cm: {b.mean()}')\n", "print(f'Number of participants: {illusSize_df_FixXPos.shape[0]}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze Saccade Latency per subject"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["condition           deepeye_id  arrowHeadsInward  arrowHeadsOutward  \\\n", "0          2025_03_27_10_28_10        210.428889         202.819149   \n", "\n", "condition  IllusionLatencyDiff  \n", "0                      7.60974  \n", "T-test: t=nan, p=nan\n", "Mean Illusion Latency Inward: 210.42888888887828\n", "Mean Illusion Latency Outward: 202.81914893616403\n", "Mean Illusion Latency Diff: 7.60973995271425\n", "Number of participants: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\deepeye_analyze\\lib\\site-packages\\scipy\\stats\\_stats_py.py:1114: RuntimeWarning: divide by zero encountered in divide\n", "  var *= np.divide(n, n-ddof)  # to avoid error on division by zero\n", "c:\\Users\\<USER>\\Documents\\GitHub\\DeepEye_analyze\\deepeye_analyze\\lib\\site-packages\\scipy\\stats\\_stats_py.py:1114: RuntimeWarning: invalid value encountered in scalar multiply\n", "  var *= np.divide(n, n-ddof)  # to avoid error on division by zero\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAGgCAYAAABWo0bIAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAvgklEQVR4nO3deXQUZb7/8U9nIYQlnSgJArYEgiyC4sKooBBGwQgIAziIgiQYURRGREcdMmwCQhQV0VEYQWgYA6JiDJtHJFxA2SRcUIlXZE9HRUUwadYOpPv3x/zoO32TYFfSoTvl+3VOnUM99VTVt3OO9uc8z9NVFo/H4xEAAIBJhAW7AAAAgEAi3AAAAFMh3AAAAFMh3AAAAFMh3AAAAFMh3AAAAFMh3AAAAFMh3AAAAFMh3AAAAFMh3AC/M88++6wsFotPW2JiooYOHVpt97RYLHr22Wer7foVKe+zAjA/wg1gYgsWLJDFYtH27duDXUqNM2vWLC1YsCDYZQCohIhgFwDA/E6fPq2IiJr1v5tZs2apQYMG1TqiBaB61Kz/2wCokWrXrh3sEgD8jjAtBaCMitaqnJ/mOnTokLdt+/btSklJUYMGDRQdHa1mzZopPT3d57zy1tzs3LlTPXr0UExMjOrVq6fbb79dW7duLfd+mzZt0pNPPqn4+HjVrVtX/fr105EjRyr12ex2u2677TYlJCQoKipKV111lWbPnu3TJzExUV9//bU2bNggi8Uii8Wirl27eo8XFRVp9OjRstlsioqKUosWLfTCCy/I7XZ7+xw6dEgWi0UvvfSS5syZo6SkJEVFRekPf/iD8vLyytS1e/du3XPPPYqPj1d0dLRatWqlsWPHSpLWrVsni8WiDz/8sMx5ixcvlsVi0ZYtWyr19wDMiJEbAJX2888/64477lB8fLzGjBmj2NhYHTp0SNnZ2Rc87+uvv1bnzp0VExOjZ555RpGRkXrzzTfVtWtXbdiwQTfddJNP/8cee0xxcXGaOHGiDh06pJkzZ+ovf/mL3n33XcM1z549W23btlWfPn0UERGhFStWaMSIEXK73Ro5cqQkaebMmXrsscdUr149b8Bo2LChJOnUqVNKTk7W999/r+HDh+uKK67Q5s2blZGRocOHD2vmzJk+91u8eLGOHz+u4cOHy2KxaPr06erfv78OHDigyMhISdJXX32lzp07KzIyUg8//LASExO1f/9+rVixQlOnTlXXrl1ls9m0aNEi9evXz+f6ixYtUlJSkjp27Gj4bwGYlgeAadntdo8kT15enrdt4sSJnv/7n37Tpk09aWlpF+zzn9c7ePCgx+PxeD788MMy1y+PJM/EiRO9+3379vXUqlXLs3//fm/bDz/84Klfv76nS5cuZe7XrVs3j9vt9rY/8cQTnvDwcE9RUdEF71ve5zh16lSZfikpKZ7mzZv7tLVt29aTnJxcpu+UKVM8devW9ezZs8enfcyYMZ7w8HCPw+HweDwez8GDBz2SPJdeeqnn2LFj3n7Lli3zSPKsWLHC29alSxdP/fr1PQUFBT7X/M/PnJGR4YmKivL5zD///LMnIiLC528LwONhWgpApcXGxkqSVq5cqbNnz/p1TmlpqT755BP17dtXzZs397Y3atRIgwYN0saNG+V0On3Oefjhh32myTp37qzS0lIVFBQYrjk6Otr77+LiYv3yyy9KTk7WgQMHVFxc/Jvnv//+++rcubPi4uL0yy+/eLdu3bqptLRUn376qU//gQMHKi4uzqd2STpw4IAk6ciRI/r000+Vnp6uK664wufc//zMqampcrlcWrp0qbft3Xff1blz53T//fcb+AsA5ke4AVBpycnJuvvuuzVp0iQ1aNBAf/rTn2S32+VyuSo858iRIzp16pRatWpV5libNm3kdrtVWFjo0/5/v/TPh4Vff/3VcM2bNm1St27dVLduXcXGxio+Pl5///vfJcmvcLN37159/PHHio+P99m6desm6d9TdUZqPx9y2rVrd8H7tm7dWn/4wx+0aNEib9uiRYt08803q0WLFr9ZN/B7wpobAGVU9OC70tLSMv2WLl2qrVu3asWKFVq9erXS09P18ssva+vWrapXr15A6gkPDy+33ePxGLrO/v37dfvtt6t169aaMWOGbDabatWqpY8++kivvPKKz4LgirjdbnXv3l3PPPNMucdbtmxZLbVL/x69efzxx/Xdd9/J5XJp69atev311w1fBzA7wg2AMs6PLhQVFXmnniRVOA1088036+abb9bUqVO1ePFiDR48WEuWLNGwYcPK9I2Pj1edOnX07bffljm2e/duhYWFyWazBeaD/B8rVqyQy+XS8uXLfUZU1q1bV6ZvRQEvKSlJJ06c8I7UVNX5qbn8/Pzf7HvvvffqySef1DvvvKPTp08rMjJSAwcODEgdgJkwLQWgjKSkJEnyWT9y8uRJLVy40Kffr7/+WmYE4tprr5WkCqemwsPDdccdd2jZsmU+Pyn/6aeftHjxYt16662KiYkJwKco/96S76hJcXGx7HZ7mb5169ZVUVFRmfZ77rlHW7Zs0erVq8scKyoq0rlz5wzVFB8fry5dumj+/PlyOBw+x/7v37ZBgwbq0aOHsrKytGjRIt15551q0KCBofsBvweM3AAo44477tAVV1yhBx98UE8//bTCw8M1f/58xcfH+3wBL1y4ULNmzVK/fv2UlJSk48ePa+7cuYqJiVHPnj0rvP5zzz2nNWvW6NZbb9WIESMUERGhN998Uy6XS9OnT6/Wz1WrVi317t1bw4cP14kTJzR37lwlJCTo8OHDPn1vuOEGzZ49W88995xatGihhIQE3XbbbXr66ae1fPly3XXXXRo6dKhuuOEGnTx5Urt27dLSpUt16NAhw4Hjtdde06233qrrr79eDz/8sJo1a6ZDhw5p1apV+uKLL3z6pqam6s9//rMkacqUKVX6ewBmRbgBUEZkZKQ+/PBDjRgxQuPHj9dll12m0aNHKy4uTg888IC3X3JysrZt26YlS5bop59+ktVq1Y033qhFixapWbNmFV6/bdu2+uyzz5SRkaHMzEy53W7ddNNNysrKKvOMm0Bq1aqVli5dqnHjxumpp57SZZddpkcffVTx8fFlHjw4YcIEFRQUaPr06Tp+/LiSk5N12223qU6dOtqwYYOmTZum999/X//6178UExOjli1batKkSbJarYbrat++vbZu3arx48dr9uzZOnPmjJo2bap77rmnTN/evXsrLi5Obrdbffr0qfTfAjAzi6cyq9oAAEFx7tw5NW7cWL1799a8efOCXQ4QklhzAwA1SE5Ojo4cOaLU1NRglwKELEZuAKAG+Pzzz/XVV19pypQpatCggXbs2BHskoCQxcgNANQAs2fP1qOPPqqEhAT961//CnY5QEhj5AYAAJgKIzcAAMBUCDcAAMBUfnfPuXG73frhhx9Uv379Ch+vDgAAQovH49Hx48fVuHFjhYVdeGzmdxdufvjhh2p7bw0AAKhehYWFuvzyyy/Y53cXburXry/p33+c6np/DQAACCyn0ymbzeb9Hr+Q3124OT8VFRMTQ7gBAKCG8WdJCQuKAQCAqRBuAACAqRBuAACAqRBuAACAqRBuAACAqQQ13JSWlmr8+PFq1qyZoqOjlZSUpClTpui3Xne1fv16XX/99YqKilKLFi20YMGCi1MwAAAIeUH9KfgLL7yg2bNna+HChWrbtq22b9+uBx54QFarVaNGjSr3nIMHD6pXr1565JFHtGjRIq1du1bDhg1To0aNlJKScpE/AQAACDVBfSv4XXfdpYYNG2revHnetrvvvlvR0dHKysoq95y//e1vWrVqlfLz871t9957r4qKivTxxx//5j2dTqesVquKi4t5zg0AADWEke/voE5LderUSWvXrtWePXskSV9++aU2btyoHj16VHjOli1b1K1bN5+2lJQUbdmypdz+LpdLTqfTZwMAAOYV1GmpMWPGyOl0qnXr1goPD1dpaammTp2qwYMHV3jOjz/+qIYNG/q0NWzYUE6nU6dPn1Z0dLTPsczMTE2aNKla6gcAAKEnqCM37733nhYtWqTFixdrx44dWrhwoV566SUtXLgwYPfIyMhQcXGxdyssLAzYtQEAQOgJ6sjN008/rTFjxujee++VJF199dUqKChQZmam0tLSyj3nsssu008//eTT9tNPPykmJqbMqI0kRUVFKSoqKvDFAwCAkBTUkZtTp04pLMy3hPDwcLnd7grP6dixo9auXevTtmbNGnXs2LFaagQAADVLUMNN7969NXXqVK1atUqHDh3Shx9+qBkzZqhfv37ePhkZGUpNTfXuP/LIIzpw4ICeeeYZ7d69W7NmzdJ7772nJ554IhgfAQAAhJigTkv94x//0Pjx4zVixAj9/PPPaty4sYYPH64JEyZ4+xw+fFgOh8O736xZM61atUpPPPGEXn31VV1++eV66623eMYNAACQFOTn3AQDz7kBAKDmqTHPuQEAAAg0wg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADAVwg0AADCVoIabxMREWSyWMtvIkSPL7X/27FlNnjxZSUlJql27ttq3b6+PP/74IlcNAABCWUQwb56Xl6fS0lLvfn5+vrp3764BAwaU23/cuHHKysrS3Llz1bp1a61evVr9+vXT5s2bdd11112ssgEAQAizeDweT7CLOG/06NFauXKl9u7dK4vFUuZ448aNNXbsWJ+RnbvvvlvR0dHKysry6x5Op1NWq1XFxcWKiYkJWO0AAKD6GPn+DurIzX8qKSlRVlaWnnzyyXKDjSS5XC7Vrl3bpy06OlobN26s8Loul0sul8u773Q6A1MwAAAISSGzoDgnJ0dFRUUaOnRohX1SUlI0Y8YM7d27V263W2vWrFF2drYOHz5c4TmZmZmyWq3ezWazVUP1AAAgVITMtFRKSopq1aqlFStWVNjnyJEjeuihh7RixQpZLBYlJSWpW7dumj9/vk6fPl3uOeWN3NhsNqalAACoQYxMS4XEyE1BQYFyc3M1bNiwC/aLj49XTk6OTp48qYKCAu3evVv16tVT8+bNKzwnKipKMTExPhsAADCvkAg3drtdCQkJ6tWrl1/9a9eurSZNmujcuXP64IMP9Kc//amaKwQAADVF0MON2+2W3W5XWlqaIiJ81zenpqYqIyPDu//5558rOztbBw4c0GeffaY777xTbrdbzzzzzMUuGwAAhKig/1oqNzdXDodD6enpZY45HA6Fhf1v/jpz5ozGjRunAwcOqF69eurZs6fefvttxcbGXsSKAQBAKAuZBcUXC8+5AQCg5qlxC4oBAAAChXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMhXADAABMJajhJjExURaLpcw2cuTICs+ZOXOmWrVqpejoaNlsNj3xxBM6c+bMRawaAACEsohg3jwvL0+lpaXe/fz8fHXv3l0DBgwot//ixYs1ZswYzZ8/X506ddKePXs0dOhQWSwWzZgx42KVDQAAQlhQw018fLzP/vPPP6+kpCQlJyeX23/z5s265ZZbNGjQIEn/Hvm577779Pnnn1d4D5fLJZfL5d13Op0BqBwAAISqkFlzU1JSoqysLKWnp8tisZTbp1OnTvrv//5vbdu2TZJ04MABffTRR+rZs2eF183MzJTVavVuNputWuoHAAChweLxeDzBLkKS3nvvPQ0aNEgOh0ONGzeusN9rr72mp556Sh6PR+fOndMjjzyi2bNnV9i/vJEbm82m4uJixcTEBPQzAACA6uF0OmW1Wv36/g6ZkZt58+apR48eFww269ev17Rp0zRr1izt2LFD2dnZWrVqlaZMmVLhOVFRUYqJifHZAACAeQV1zc15BQUFys3NVXZ29gX7jR8/XkOGDNGwYcMkSVdffbVOnjyphx9+WGPHjlVYWMhkNQAAECQhkQbsdrsSEhLUq1evC/Y7depUmQATHh4uSQqR2TUAABBkQR+5cbvdstvtSktLU0SEbzmpqalq0qSJMjMzJUm9e/fWjBkzdN111+mmm27Svn37NH78ePXu3dsbcgAAwO9b0MNNbm6uHA6H0tPTyxxzOBw+IzXjxo2TxWLRuHHj9P333ys+Pl69e/fW1KlTL2bJAAAghIXMr6UuFiOrrQEAQGiokb+WAgAACATCDQAAMBXCDQAAMBXCDQAAMBXCDQAAMBXCDQAAMBXD4aawsFDfffedd3/btm0aPXq05syZE9DCAAAAKsNwuBk0aJDWrVsnSfrxxx/VvXt3bdu2TWPHjtXkyZMDXiAAAIARhsNNfn6+brzxRknSe++9p3bt2mnz5s1atGiRFixYEOj6AAAADDEcbs6ePauoqChJ/351Qp8+fSRJrVu31uHDhwNbHQAAgEGGw03btm31z3/+U5999pnWrFmjO++8U5L0ww8/6NJLLw14gQAAAEYYDjcvvPCC3nzzTXXt2lX33Xef2rdvL0lavny5d7oKAAAgWCr14szS0lI5nU7FxcV52w4dOqQ6deooISEhoAUGGi/OBACg5jHy/R1RmRuEh4f7BBtJSkxMrMylAAAAAspwuDl69KgmTJigdevW6eeff5bb7fY5fuzYsYAVBwAAYJThcDNkyBDt27dPDz74oBo2bCiLxVIddQEAAFSK4XDz2WefaePGjd6FxAAAAKHE8K+lWrdurdOnT1dHLQAAAFVmONzMmjVLY8eO1YYNG3T06FE5nU6fDQAAIJgMT0vFxsbK6XTqtttu82n3eDyyWCwqLS0NWHEAAABGGQ43gwcPVmRkpBYvXsyCYgAAEHIMh5v8/Hzt3LlTrVq1qo56AAAAqsTwmpsOHTqosLCwOmoBAACoMsMjN4899pgef/xxPf3007r66qsVGRnpc/yaa64JWHEAAABGGX63VFhY2cEei8VSYxYU824pAABqnmp9t9TBgwcrXRgAAEB1MxxumjZtWh11AAAABIThBcUAAAChjHADAABMhXADAABMhXADAABMhXADAABMxa9fS8XFxfn9Dqljx45VqSAAAICq8CvczJw50/vvo0eP6rnnnlNKSoo6duwoSdqyZYtWr16t8ePHG7p5YmKiCgoKyrSPGDFCb7zxRpn2rl27asOGDWXae/bsqVWrVhm6NwAAMCfDTyi+++679cc//lF/+ctffNpff/115ebmKicnx+9rHTlyxOeJxvn5+erevbvWrVunrl27lul/7NgxlZSUePePHj2q9u3b66233tLQoUP9uidPKAYAoOYx8v1tONzUq1dPX3zxhVq0aOHTvm/fPl177bU6ceKE8Yr/v9GjR2vlypXau3evX9NgM2fO1IQJE3T48GHVrVu33D4ul0sul8u773Q6ZbPZCDcAANQgRsKN4QXFl156qZYtW1amfdmyZbr00kuNXs6rpKREWVlZSk9P93t9z7x583TvvfdWGGwkKTMzU1ar1bvZbLZK1wgAAEKf4dcvTJo0ScOGDdP69et10003SZI+//xzffzxx5o7d26lC8nJyVFRUZHf00vbtm1Tfn6+5s2bd8F+GRkZevLJJ73750duAACAORmelpL+HWZee+01ffPNN5KkNm3aaNSoUd6wUxkpKSmqVauWVqxY4Vf/4cOHa8uWLfrqq68M3Yc1NwAA1DzV9lbws2fPavjw4Ro/frwWLVpUpSL/U0FBgXJzc5Wdne1X/5MnT2rJkiWaPHlywGoAAADmYGjNTWRkpD744IOAF2G325WQkKBevXr51f/999+Xy+XS/fffH/BaAABAzWZ4QXHfvn0N/dz7t7jdbtntdqWlpSkiwncgKTU1VRkZGWXOmTdvnvr27VulBcwAAMCcDC8ovvLKKzV58mRt2rRJN9xwQ5lfKo0aNcrQ9XJzc+VwOJSenl7mmMPhUFiYb/769ttvtXHjRn3yySdGSwcAAL8DhhcUN2vWrOKLWSw6cOBAlYuqTiwoBgCg5qm2BcWSdPDgwUoXBgAAUN14KzgAADAVwyM3kvTdd99p+fLlcjgcPu96kqQZM2YEpDAAAIDKMBxu1q5dqz59+qh58+bavXu32rVrp0OHDsnj8ej666+vjhoBAAD8ZnhaKiMjQ0899ZR27dql2rVr64MPPlBhYaGSk5M1YMCA6qgRAADAb4bDzTfffKPU1FRJUkREhE6fPq169epp8uTJeuGFFwJeIAAAgBGGw03dunW962waNWqk/fv3e4/98ssvgasMAACgEgyvubn55pu1ceNGtWnTRj179tRf//pX7dq1S9nZ2br55puro0YAAAC/GQ43M2bM0IkTJyRJkyZN0okTJ/Tuu+/qyiuv5JdSAAAg6Aw/obim4wnFAADUPEa+vw2vuZkwYYLWrVunM2fOVLpAAACA6mI43GzZskW9e/dWbGysOnfurHHjxik3N1enT5+ujvoAAAAMMRxu1qxZo6KiIq1du1Y9e/bU9u3b1b9/f8XGxurWW2+tjhoBAAD8VqnXL0REROiWW25RfHy8LrnkEtWvX185OTnavXt3oOsDAAAwxPDIzZw5czRo0CA1adJEnTp10scff6xbb71V27dv15EjR6qjRgAAAL8ZHrl55JFHFB8fr7/+9a8aMWKE6tWrVx11AQAAVIrhkZvs7GwNHjxYS5YsUXx8vDp16qS///3v+uSTT3Tq1KnqqBEAAMBvVXrOTXFxsT777DO9//77eueddxQWFhbyPxHnOTcAANQ8Rr6/K7Wg+OjRo9qwYYPWr1+v9evX6+uvv1ZcXJw6d+5cqYIBAAACxXC4ufrqq/XNN98oLi5OXbp00UMPPaTk5GRdc8011VEfAACAIZVaUJycnKx27dpVRz0AAABVYnhB8dGjR9W8efMy7adPn9bkyZMDUhQAAEBlGV5QHB4ersOHDyshIcGn/ejRo0pISFBpaWlACww0FhQDAFDzVOuLMz0ejywWS5n2L7/8UpdcconRywEAAASU32tu4uLiZLFYZLFY1LJlS5+AU1paqhMnTuiRRx6pliIBAAD85Xe4mTlzpjwej9LT0zVp0iRZrVbvsVq1aikxMVEdO3asliIBAAD85Xe4SUtLkyQ1a9ZMnTp1UmRkZLUVBQAAUFmGfwrerFkzHT58uMLjV1xxRZUKAgAAqArD4SYxMbHcBcXnhfqvpQAAgLkZDjc7d+702T979qx27typGTNmaOrUqQErDAAAoDIMh5v27duXaevQoYMaN26sF198Uf379w9IYQAAAJVRqRdnlqdVq1bKy8sL1OXgh3379ungwYPBLuN379SpU9q/f3+wywBCUlJSkurUqRPsMqB/r5lt0aJFsMu4KAyHG6fT6bPv8Xh0+PBhPfvss7ryyisDVhh+2z/+8Q99+eWXwS4DAFADtG/fXq+++mqwy7goDIeb2NjYMguKPR6PbDablixZYuhaiYmJKigoKNM+YsQIvfHGG+WeU1RUpLFjxyo7O1vHjh1T06ZNNXPmTPXs2dPQvc3gscceY+QmBDByA1SMkZvQ0axZs2CXcNEYDjfr1q3z2Q8LC1N8fLxatGihiAhjl8vLy/P5dVV+fr66d++uAQMGlNu/pKRE3bt3V0JCgpYuXaomTZqooKBAsbGxRj+GKbRo0eJ3M8QIAIC/DIeb5OTkgN08Pj7eZ//5559XUlJShfeYP3++jh07ps2bN3sfIpiYmBiwegAAQM1n+MWZ77//vvr376927dqpXbt26t+/v5YuXVrlQkpKSpSVlaX09PQKn6OzfPlydezYUSNHjlTDhg3Vrl07TZs27YLP1nG5XHI6nT4bAAAwL7/Djdvt1sCBAzVw4ED9z//8j3dK5Ouvv9bAgQN17733yuPxVLqQnJwcFRUVaejQoRX2OXDggJYuXarS0lJ99NFHGj9+vF5++WU999xzFZ6TmZkpq9Xq3Ww2W6VrBAAAoc/i8TORvPLKK3ruuee0cOFC3XXXXT7Hli9frgceeEDjx4/X6NGjK1VISkqKatWqpRUrVlTYp2XLljpz5owOHjyo8PBwSdKMGTP04osvVvhKCJfLJZfL5d13Op2y2WwqLi5WTExMpWoFAAAXl9PplNVq9ev72++RG7vdrhdffLFMsJGkPn36aPr06Zo/f77xaiUVFBQoNzdXw4YNu2C/Ro0aqWXLlt5gI0lt2rTRjz/+qJKSknLPiYqKUkxMjM8GAADMy+9ws3fvXnXr1q3C4926ddPevXsrVYTdbldCQoJ69ep1wX633HKL9u3bJ7fb7W3bs2ePGjVqpFq1alXq3gAAwFz8DjfR0dEqKiqq8LjT6VTt2rUNF+B2u2W325WWllbmp+SpqanKyMjw7j/66KM6duyYHn/8ce3Zs0erVq3StGnTNHLkSMP3BQAA5uR3uOnYsaNmz55d4fE33nhDHTt2NFxAbm6uHA6H0tPTyxxzOBw+a2lsNptWr16tvLw8XXPNNRo1apQef/xxjRkzxvB9AQCAOfm9oHjz5s3q2rWr+vbtq6eeekqtW7eWx+PRN998o5dfflnLli3TunXrdMstt1R3zVViZEESAAAIDUa+v/1+iF+nTp307rvv6uGHH9YHH3zgcywuLk7vvPNOyAcbAABgfn6P3Jx36tQprV692rt4uGXLlrrjjjtqzLtDGLkBAKDmqZaRm/Pq1Kmjfv36Vbo4AACA6mT49QsAAAChjHADAABMhXADAABMhXADAABMpUrhplevXhW+sBIAACAYqhRuPv30U50+fTpQtQAAAFQZ01IAAMBUqhRumjZtqsjIyEDVAgAAUGWGH+L3n/Lz8wNVBwAAQEAwLQUAAEyFcAMAAEyFcAMAAEyFcAMAAEyFcAMAAEzFr19LXXfddbJYLH5dcMeOHVUqCAAAoCr8Cjd9+/b1/vvMmTOaNWuWrrrqKnXs2FGStHXrVn399dcaMWJEtRQJAADgL7/CzcSJE73/HjZsmEaNGqUpU6aU6VNYWBjY6gAAAAyyeDwej5ETrFartm/friuvvNKnfe/everQoYOKi4sDWmCgOZ1OWa1WFRcXKyYmJtjlAAAAPxj5/ja8oDg6OlqbNm0q075p0ybVrl3b6OUAAAACyvDrF0aPHq1HH31UO3bs0I033ihJ+vzzzzV//nyNHz8+4AUCAAAYYTjcjBkzRs2bN9err76qrKwsSVKbNm1kt9t1zz33BLxAAAAAIwyFm3PnzmnatGlKT08nyAAAgJBkaM1NRESEpk+frnPnzlVXPQAAAFVieEHx7bffrg0bNlRHLQAAAFVmeM1Njx49NGbMGO3atUs33HCD6tat63O8T58+ASsOAADAKMPPuQkLq3iwx2KxqLS0tMpFVSeecwMAQM1j5Pvb8MiN2+2udGEAAADVjbeCAwAAUzE8ciNJJ0+e1IYNG+RwOFRSUuJzbNSoUQEpDAAAoDIMh5udO3eqZ8+eOnXqlE6ePKlLLrlEv/zyi+rUqaOEhATCDQAACCrD01JPPPGEevfurV9//VXR0dHaunWrCgoKdMMNN+ill14ydK3ExERZLJYy28iRI8vtv2DBgjJ9eZ8VAAD4T4ZHbr744gu9+eabCgsLU3h4uFwul5o3b67p06crLS1N/fv39/taeXl5Pr+uys/PV/fu3TVgwIAKz4mJidG3337r3bdYLEY/AgAAMDHD4SYyMtL7c/CEhAQ5HA61adNGVqtVhYWFhq4VHx/vs//8888rKSlJycnJFZ5jsVh02WWXGS0bAAD8ThgON9ddd53y8vJ05ZVXKjk5WRMmTNAvv/yit99+W+3atat0ISUlJcrKytKTTz55wdGYEydOqGnTpnK73br++us1bdo0tW3btsL+LpdLLpfLu+90OitdIwAACH2G19xMmzZNjRo1kiRNnTpVcXFxevTRR3XkyBHNmTOn0oXk5OSoqKhIQ4cOrbBPq1atNH/+fC1btkxZWVlyu93q1KmTvvvuuwrPyczMlNVq9W42m63SNQIAgNBn+AnF1SUlJUW1atXSihUr/D7n7NmzatOmje677z5NmTKl3D7ljdzYbDaeUAwAQA1SrU8onj9/vv74xz+qWbNmlS7w/yooKFBubq6ys7MNnRcZGanrrrtO+/btq7BPVFSUoqKiqloiAACoIQxPS2VmZqpFixa64oorNGTIEL311lsXDBf+sNvtSkhIUK9evQydV1paql27dnmnyQAAAAyHm71798rhcCgzM1N16tTRSy+9pFatWunyyy/X/fffb7gAt9stu92utLQ0RUT4DiSlpqYqIyPDuz958mR98sknOnDggHbs2KH7779fBQUFGjZsmOH7AgAAc6rUu6WaNGmiwYMH65VXXtGrr76qIUOG6KefftKSJUsMXys3N1cOh0Pp6elljjkcDh0+fNi7/+uvv+qhhx5SmzZt1LNnTzmdTm3evFlXXXVVZT4GAAAwIcMLij/55BOtX79e69ev186dO9WmTRslJyera9eu6tKli+Li4qqr1oAwsiAJAACEhmpdUHznnXcqPj5ef/3rX/XRRx8pNja2snUCAAAEnOFpqRkzZuiWW27R9OnT1bZtWw0aNEhz5szRnj17qqM+AAAAQ6r0nJtdu3Zpw4YN+q//+i+tXLlSCQkJF3ygXihgWgoAgJqnWqelJMnj8Wjnzp1av3691q1bp40bN8rtdpd5VxQAAMDFZjjc9O7dW5s2bZLT6VT79u3VtWtXPfTQQ+rSpQvrbwAAQNAZDjetW7fW8OHD1blzZ1mt1uqoCQAAoNIMh5sXX3yxOuoAAAAIiEqtuVm7dq3Wrl2rn3/+WW632+fY/PnzA1IYAABAZRgON5MmTdLkyZPVoUMHNWrUSBaLpTrqAgAAqBTD4eaf//ynFixYoCFDhlRHPQAAAFVi+CF+JSUl6tSpU3XUAgAAUGWGw82wYcO0ePHi6qgFAACgygxPS505c0Zz5sxRbm6urrnmGkVGRvocnzFjRsCKAwAAMMpwuPnqq6907bXXSpLy8/N9jrG4GAAABJvhcLNu3brqqAMAACAgDK+5AQAACGV+j9z079/fr37Z2dmVLgYAAKCq/A43vEcKAADUBH6HG7vdXp11AAAABARrbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkENdwkJibKYrGU2UaOHPmb5y5ZskQWi0V9+/at/kIBAECNERHMm+fl5am0tNS7n5+fr+7du2vAgAEXPO/QoUN66qmn1Llz5+ouEQAA1DBBHbmJj4/XZZdd5t1WrlyppKQkJScnV3hOaWmpBg8erEmTJql58+YXsVoAAFAThMyam5KSEmVlZSk9PV0Wi6XCfpMnT1ZCQoIefPBBv67rcrnkdDp9NgAAYF4hE25ycnJUVFSkoUOHVthn48aNmjdvnubOnev3dTMzM2W1Wr2bzWYLQLUAACBUhUy4mTdvnnr06KHGjRuXe/z48eMaMmSI5s6dqwYNGvh93YyMDBUXF3u3wsLCQJUMAABCUFAXFJ9XUFCg3NxcZWdnV9hn//79OnTokHr37u1tc7vdkqSIiAh9++23SkpKKnNeVFSUoqKiAl80AAAISSERbux2uxISEtSrV68K+7Ru3Vq7du3yaRs3bpyOHz+uV199lekmAAAgKQTCjdvtlt1uV1pamiIifMtJTU1VkyZNlJmZqdq1a6tdu3Y+x2NjYyWpTDsAAPj9Cnq4yc3NlcPhUHp6epljDodDYWEhsywIAADUABaPx+MJdhEXk9PplNVqVXFxsWJiYoJdDgAA8IOR72+GRQAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkQbgAAgKkENdwkJibKYrGU2UaOHFlu/+zsbHXo0EGxsbGqW7eurr32Wr399tsXuWoAABDKIoJ587y8PJWWlnr38/Pz1b17dw0YMKDc/pdcconGjh2r1q1bq1atWlq5cqUeeOABJSQkKCUl5WKVDQAAQpjF4/F4gl3EeaNHj9bKlSu1d+9eWSwWv865/vrr1atXL02ZMsWv/k6nU1arVcXFxYqJialKuQAA4CIx8v0dMmtuSkpKlJWVpfT0dL+Cjcfj0dq1a/Xtt9+qS5cuFfZzuVxyOp0+GwAAMK+gTkv9p5ycHBUVFWno0KEX7FdcXKwmTZrI5XIpPDxcs2bNUvfu3Svsn5mZqUmTJgW4WgAAEKpCZloqJSVFtWrV0ooVKy7Yz+1268CBAzpx4oTWrl2rKVOmKCcnR127di23v8vlksvl8u47nU7ZbDampQAAqEGMTEuFxMhNQUGBcnNzlZ2d/Zt9w8LC1KJFC0nStddeq2+++UaZmZkVhpuoqChFRUUFslwAABDCQmLNjd1uV0JCgnr16mX4XLfb7TMyAwAAft+CPnLjdrtlt9uVlpamiAjfclJTU9WkSRNlZmZK+vf6mQ4dOigpKUkul0sfffSR3n77bc2ePTsYpQMAgBAU9HCTm5srh8Oh9PT0MsccDofCwv53cOnkyZMaMWKEvvvuO0VHR6t169bKysrSwIEDL2bJAAAghIXMguKLhefcAABQ89TI59wAAAAEAuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYCuEGAACYSlDDTWJioiwWS5lt5MiR5fafO3euOnfurLi4OMXFxalbt27atm3bRa4aAACEsqCGm7y8PB0+fNi7rVmzRpI0YMCAcvuvX79e9913n9atW6ctW7bIZrPpjjvu0Pfff38xywYAACHM4vF4PMEu4rzRo0dr5cqV2rt3rywWy2/2Ly0tVVxcnF5//XWlpqaW28flcsnlcnn3nU6nbDabiouLFRMTE7DaAQBA9XE6nbJarX59f4fMmpuSkhJlZWUpPT3dr2AjSadOndLZs2d1ySWXVNgnMzNTVqvVu9lstkCVDAAAQlDIhJucnBwVFRVp6NChfp/zt7/9TY0bN1a3bt0q7JORkaHi4mLvVlhYGIBqAQBAqIoIdgHnzZs3Tz169FDjxo396v/8889ryZIlWr9+vWrXrl1hv6ioKEVFRQWqTAAAEOJCItwUFBQoNzdX2dnZfvV/6aWX9Pzzzys3N1fXXHONoXudX2LkdDoN1wkAAILj/Pe2P0uFQyLc2O12JSQkqFevXr/Zd/r06Zo6dapWr16tDh06GL7X8ePHJYm1NwAA1EDHjx+X1Wq9YJ+ghxu32y273a60tDRFRPiWk5qaqiZNmigzM1OS9MILL2jChAlavHixEhMT9eOPP0qS6tWrp3r16vl1v8aNG6uwsFD169f3e+EygJrh/K8hCwsL+TUkYDIej0fHjx/3a/lK0MNNbm6uHA6H0tPTyxxzOBwKC/vfNc+zZ89WSUmJ/vznP/v0mzhxop599lm/7hcWFqbLL7+8SjUDCG0xMTGEG8CEfmvE5ryQes4NAFSFkedgADCvkPkpOAAAQCAQbgCYRlRUlCZOnMjjH4DfOaalAACAqTByAwAATIVwAwAATIVwAwAATIVwAwAATIVwAwAATIVwAwAATIVwAwAATIVwAwAATOX/AaL3uXVtyVr/AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Summary statistics for the saccade latency\n", "illusLatency_df = filtered_summary_df.groupby(['condition', 'deepeye_id'])[['SaccLat']].mean().reset_index()\n", "\n", "# Pivot the DataFrame to have conditions as columns\n", "illusLatency_df = illusLatency_df.pivot(index='deepeye_id', columns='condition', values='SaccLat').reset_index()\n", "illusLatency_df['IllusionLatencyDiff'] = illusLatency_df.arrowHeadsInward - illusLatency_df.arrowHeadsOutward\n", "print(illusLatency_df)\n", "\n", "# Plot the illusion latency\n", "plt.figure()\n", "plt.title('Illusion latency')\n", "plt.ylabel('Inward - Outward ms')\n", "sns.boxplot(illusLatency_df.IllusionLatencyDiff)\n", "\n", "# T-test\n", "from scipy.stats import ttest_rel\n", "t, p = ttest_rel(illusLatency_df.arrowHeadsInward, illusLatency_df.arrowHeadsOutward)\n", "print(f'T-test: t={t}, p={p}')\n", "print(f'Mean Illusion Latency Inward: {illusLatency_df.arrowHeadsInward.mean()}')\n", "print(f'Mean Illusion Latency Outward: {illusLatency_df.arrowHeadsOutward.mean()}')\n", "print(f'Mean Illusion Latency Diff: {illusLatency_df.IllusionLatencyDiff.mean()}')\n", "print(f'Number of participants: {illusLatency_df.shape[0]}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Saccade Latency For all subjects together, for outward and inward arrows"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean outward latency: 202.81914893616403\n", "Mean inward latency: 210.42888888887828\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot histograms with normalization\n", "plt.figure(figsize=(10, 6))\n", "sns.set_theme(style=\"whitegrid\")\n", "\n", "plt.title('Saccade Latency', fontsize=16, fontweight='bold')\n", "\n", "outward = filtered_summary_df.SaccLat[summary_df.condition == 'arrowHeadsOutward']\n", "inward = filtered_summary_df.SaccLat[summary_df.condition == 'arrowHeadsInward']\n", "\n", "print(f'Mean outward latency: {outward.mean()}')\n", "print(f'Mean inward latency: {inward.mean()}')\n", "\n", "plt.hist(outward,  alpha=0.6, bins=10, label='Outward Arrowheads', density=True, color='skyblue', edgecolor='black')\n", "plt.hist(inward,  alpha=0.6, bins=10, label='Inward Arrowheads', density=True, color='salmon', edgecolor='black')\n", "\n", "plt.legend(title='Condition', fontsize=12)\n", "plt.xlabel('Saccade Latency (ms)', fontsize=14)\n", "plt.ylabel('Frequency (Normalized)', fontsize=14)\n", "plt.xticks(fontsize=12)\n", "plt.yticks(fontsize=12)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "deepeye_analyze", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}