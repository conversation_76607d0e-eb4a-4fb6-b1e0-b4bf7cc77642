import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm # Was in webcam notebook
import matplotlib.patches as patches # Was in webcam notebook, en gebruikt voor custom plot
import os
import astropy.convolution as krn # Was in webcam notebook, maar makeHeat wordt hier niet gebruikt voor checkerboard
import scipy.stats as stats      # Was in webcam notebook, m<PERSON><PERSON><PERSON> later nodig voor statistische tests
import seaborn as sns
import statsmodels.api as sm     # Was in webcam notebook, m<PERSON><PERSON><PERSON> later nodig
from statsmodels.stats.anova import AnovaRM # Was in webcam notebook, m<PERSON><PERSON><PERSON> later nodig

# De import 'from deepeye_analysis_package.plotting import makeHeat' wordt hier niet direct gebruikt,
# omdat de checkerboard heatmaps gebaseerd zijn op de sns.heatmap logica uit de .md file.
# Als andere specifieke plotfuncties uit je package nodig zijn, moeten die hier geïmporteerd worden
# of hun definities moeten in dit notebook worden opgenomen.

import openpyxl 
import xlrd     

def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):
    """Converts pixel values to centimeters, identical to webcam analysis logic."""
    pixels_numeric = pd.to_numeric(pixels, errors='coerce')
    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:
        return np.nan
    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm

# compute_rms zoals gedefinieerd in de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb snippet)
def compute_rms(samples):
    """Computes Root Mean Square of sequential differences in samples.
       Logica gebaseerd op de compute_rms uit de webcam analyse.
       Input 'samples' wordt verwacht als een numpy array of lijst van (x,y) tuples/lijsten.
    """
    samples_arr = np.array(samples)
    if samples_arr.ndim != 2 or samples_arr.shape[1] != 2:
        # Probeer te hervormen als het een platte lijst is van x,y,x,y...
        if samples_arr.ndim == 1 and len(samples_arr) % 2 == 0 and len(samples_arr) >= 4:
            samples_arr = samples_arr.reshape(-1, 2)
        else:
            # print(f"compute_rms: Ongeldige input shape {samples_arr.shape}. Verwacht (N,2).")
            return np.nan
            
    # Verwijder rijen waar een van de coördinaten NaN is, voor np.diff
    samples_cleaned_for_diff = samples_arr[~np.isnan(samples_arr).any(axis=1)]

    if samples_cleaned_for_diff.shape[0] < 2: 
        return np.nan
        
    deltas = np.diff(samples_cleaned_for_diff, axis=0)
    # deltas zelf zouden geen NaNs mogen bevatten als input clean was
    distances_sq = np.sum(deltas**2, axis=1)
    
    if distances_sq.size == 0:
        return np.nan
        
    rms = np.sqrt(np.mean(distances_sq))
    return rms

def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):
    """
    Calculates median offset, precision (mean Euclidean distance to median gaze),
    and RMS error for a given target dot. Consistent with webcam analysis logic.
    """
    # Deze variabele werd gebruikt in de webcam notebook voor de precisie metriek
    precision_col_name = 'eucl_dist_gaze_to_median_cm' 

    if not gaze_points_for_dot_cm:
        return {
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            precision_col_name: np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': 0
        }

    gaze_array_cm = np.array(gaze_points_for_dot_cm)
    gaze_array_cm_cleaned = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]
    
    if gaze_array_cm_cleaned.shape[0] == 0:
         return {
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            precision_col_name: np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': len(gaze_points_for_dot_cm) 
        }

    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])
    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])
    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)

    target_coords_cm_arr = np.array(target_coords_cm)
    if np.isnan(target_coords_cm_arr).any() or np.isnan(median_gaze_coords_cm).any():
        offset_cm = np.nan
    else:
        offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - target_coords_cm_arr)

    if np.isnan(median_gaze_coords_cm).any():
        distances_to_median_cm = [] 
    else:
        distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]
    
    # Precisie metriek (gemiddelde Euclidische afstand tot mediaan gaze)
    precision_val = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan
    
    rms_cm = compute_rms(gaze_array_cm_cleaned)

    return {
        'dot_id': dot_id,
        'median_offset_cm': offset_cm,
        precision_col_name: precision_val,
        'rms_error_cm': rms_cm,
        'num_fixations': len(gaze_points_for_dot_cm)
    }

# plot_matrix functie, aangepast om de checkerboard stijl uit de webcam .md te repliceren
# De naam is gewijzigd om verwarring met een mogelijke 'plot_matrix' uit een package te voorkomen
def plot_checkerboard_heatmap_webcam_style(df_metrics_per_dot, measurement_col_name, title_str, positions_to_fill_list, cmap_str="YlGnBu"):
    """Plots a 5x5 checkerboard heatmap for the given measurement, styled like the webcam notebook."""
    
    # Bepaal de cbar label op basis van measurement_col_name
    if 'median_offset_cm' in measurement_col_name:
        cbar_label_text = 'Offset (cm)'
    elif 'eucl_dist_gaze_to_median_cm' in measurement_col_name:
        cbar_label_text = 'SD (cm)' # Overeenkomstig met webcam plot titel 'SD (cm) Across Positions'
    elif 'rms_error_cm' in measurement_col_name:
        cbar_label_text = 'RMS error (cm)' # Overeenkomstig met webcam plot titel 'RMS error (cm) Across Positions'
    else:
        cbar_label_text = measurement_col_name 
    
    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan))
    # Sorteer df_metrics_per_dot op 'dot_id' om te matchen met de impliciete volgorde van positions_to_fill_list
    # Dit gaat ervan uit dat dot_id 0..12 overeenkomt met de 13 posities
    df_sorted = df_metrics_per_dot.sort_values(by='dot_id').reset_index(drop=True)

    for idx, (row_idx, col_idx) in enumerate(positions_to_fill_list):
        # Check of de huidige dot_id (idx) bestaat in de gesorteerde data
        if idx < len(df_sorted):
            # De webcam MD snippet gebruikte .iloc[col, row] wat (y,x) is voor heatmap, maar (col_idx, row_idx) voor DataFrame.iloc
            # Standaard is heatmap(data[row, col]), dus dot_matrix.iloc[row_idx, col_idx] is correct.
            value = df_sorted.loc[idx, measurement_col_name]
            dot_matrix.iloc[row_idx, col_idx] = value
            
    plt.figure(figsize=(8, 8)) # Grootte zoals in webcam markdown
    heatmap_ax = sns.heatmap(dot_matrix, annot=True, fmt=".2f", cmap=cmap_str, 
                             square=True, annot_kws={"size": 12}, linewidths=0.5,
                             linecolor='gray', cbar_kws={'label': cbar_label_text})
    
    plt.title(title_str, fontsize=16, weight='bold', pad=12)
    plt.xlabel('Column', fontsize=14, labelpad=10)
    plt.ylabel('Row', fontsize=14, labelpad=10)
    heatmap_ax.set_xticklabels(range(1, 6), fontsize=12)
    heatmap_ax.set_yticklabels(range(1, 6), fontsize=12, rotation=0)
    if heatmap_ax.collections: # Check if colorbar exists
        heatmap_ax.collections[0].colorbar.ax.tick_params(labelsize=12)
    plt.tight_layout()
    plt.show()

# plot_metric_per_dot_custom_style, aangepast om de stijl van image_73b5a0.png (webcam) te repliceren
def plot_metric_per_dot_webcam_style(data_df, metric_col, y_label, title_str, dot_id_order):
    """Genereert een boxplot per dot_id met individuele rode punten en alternerende achtergrondkleur."""
    
    plt.style.use('seaborn-v0_8-whitegrid') 
    fig, ax = plt.subplots(figsize=(14, 7)) # Consistent met eerdere figsize
    
    plot_data = data_df[data_df['dot_id'].isin(dot_id_order)]
    
    # Alternerende achtergrondkleuren
    for i, dot_val in enumerate(dot_id_order): # i is hier de index in de dot_id_order lijst
        if i % 2 == 1: 
            ax.axvspan(i - 0.5, i + 0.5, facecolor='whitesmoke', alpha=1, zorder=0) 
            
    # Boxplot - stijl aangepast aan image_73b5a0.png
    sns.boxplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order,
                color='white', width=0.7, showfliers=False, 
                medianprops={'color': 'black', 'linewidth': 2},
                boxprops={'edgecolor': 'black', 'linewidth': 1.5, 'facecolor':'white'},
                whiskerprops={'color': 'black', 'linewidth': 1.5},
                capprops={'color': 'black', 'linewidth': 1.5},
                ax=ax, zorder=2) # zorder om boxplot boven axvspan te krijgen
    
    # Individuele datapunten (rood)
    sns.stripplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order, 
                  color='red', jitter=0.15, alpha=0.8, size=6, ax=ax, zorder=1) # zorder om stripplot achter boxplot whiskers te krijgen indien nodig
    
    ax.set_title(title_str, fontsize=16, weight='bold', pad=15)
    ax.set_xlabel('Dot ID', fontsize=14, labelpad=10)
    ax.set_ylabel(y_label, fontsize=14, labelpad=10)
    
    ax.set_xticks(ticks=range(len(dot_id_order)))
    ax.set_xticklabels(dot_id_order, fontsize=12)
    ax.tick_params(axis='y', labelsize=12)
    
    ax.grid(True, axis='y', linestyle=':', color='lightgray', zorder=-2) # Grid achter alles
    ax.set_facecolor('white') 
    fig.patch.set_facecolor('white') 
    
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    plt.tight_layout()
    plt.show()

PHYSICAL_SCREEN_WIDTH_CM = 34.4
PHYSICAL_SCREEN_HEIGHT_CM = 19.3
EYELINK_SCREEN_WIDTH_PX = 1920 
EYELINK_SCREEN_HEIGHT_PX = 1080 

print(f"EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm")
print(f"EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm")
print(f"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px")

base_data_path = './CollectedData/eyelink_data/' 
print(f"Base data path for EyeLink data: {base_data_path}")

try:
    folder_names = [
        name for name in os.listdir(base_data_path)
        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')
    ]
    if not folder_names:
        print(f"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'.")
    else:
        print(f"Gevonden EyeLink participant mappen: {sorted(folder_names)}")
except FileNotFoundError:
    print(f"FOUT: Basis data pad niet gevonden: '{base_data_path}'.")
    folder_names = []

# Checkerboard posities voor 13 punten in een 5x5 grid (0-indexed)
# Deze mapping MOET overeenkomen met hoe het in de webcam analyse werd gedaan.
positions_to_fill_13_dots = [
    (0,0), (0,2), (0,4),  
    (1,1), (1,3),          
    (2,0), (2,2), (2,4),  
    (3,1), (3,3),          
    (4,0), (4,2), (4,4)   
]

all_participants_dot_summaries_list = []

if not folder_names:
    print("Geen participant mappen om te verwerken. Script stopt.")
else:
    for participant_id in sorted(folder_names): 
        print(f"\n--- Processing Participant: {participant_id} ---")
        
        file_path = os.path.join(base_data_path, participant_id, "Output", f"{participant_id}.xls")
        print(f"Pogen data te laden van: {file_path}")

        df_eyelink_raw = None
        loaded_successfully = False
        try:
            print(f"  Poging 1: Laden als TSV... {file_path}")
            df_eyelink_raw = pd.read_csv(file_path, sep='\t', low_memory=False)
            print(f"    Succesvol geladen als TSV. Vorm: {df_eyelink_raw.shape}")
            loaded_successfully = True
        except Exception:
            try:
                print(f"  Poging 2: Laden als CSV... {file_path}")
                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)
                print(f"    Succesvol geladen als CSV. Vorm: {df_eyelink_raw.shape}")
                loaded_successfully = True
            except Exception:
                try:
                    print(f"  Poging 3: Laden als Excel (.xls) met xlrd... {file_path}")
                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')
                    print(f"    Succesvol geladen als Excel (.xls) met xlrd. Vorm: {df_eyelink_raw.shape}")
                    loaded_successfully = True
                except Exception:
                    try:
                        print(f"  Poging 4: Laden als Excel (.xlsx style) met openpyxl... {file_path}")
                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')
                        print(f"    Succesvol geladen als Excel (.xlsx style) met openpyxl. Vorm: {df_eyelink_raw.shape}")
                        loaded_successfully = True
                    except Exception as e_final:
                        print(f"  KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} ({e_final}). Sla over.")
                        continue 

        if not loaded_successfully or df_eyelink_raw is None:
            continue

        if 'arrowLength' not in df_eyelink_raw.columns:
            print(f"FOUT: Kolom 'arrowLength' niet gevonden voor {participant_id}. Sla over.")
            continue
        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)
        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith("UNDEFINED")].copy()

        if df_accprec.empty:
            print(f"Geen accuracy/precision data gevonden voor {participant_id}. Sla over.")
            continue
        
        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']
        missing_cols = [col for col in required_cols if col not in df_accprec.columns]
        if missing_cols:
            print(f"FOUT: Vereiste kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.")
            continue
        for col in required_cols:
            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')
        df_accprec.dropna(subset=required_cols, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na numerieke conversie voor {participant_id}. Sla over.")
            continue

        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']
        df_accprec.dropna(subset=cm_cols_to_check, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na cm-conversie voor {participant_id}. Sla over.")
            continue

        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)
        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)
        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]
        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])
        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)
        unique_targets_cm['dot_id'] = unique_targets_cm.index 
        num_unique_dots_found = len(unique_targets_cm)
        print(f"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots_found}")
        if num_unique_dots_found == 0:
            print(f"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.")
            continue
        if num_unique_dots_found != 13:
             print(f"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities, gevonden {num_unique_dots_found}.")
        
        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], 
                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')
        
        current_participant_dots_summary_list = []
        # Gebruik dot_ids 0 tot max(12, num_unique_dots_found-1) voor de lus om te zorgen dat we niet out of bounds gaan
        # maar ook niet meer dots proberen te verwerken dan er zijn voor checkerboard mapping.
        max_dot_id_to_process = min(num_unique_dots_found -1, len(positions_to_fill_13_dots) - 1)

        for dot_id_val in range(max_dot_id_to_process + 1):
            group = df_accprec[df_accprec['dot_id'] == dot_id_val]
            if group.empty: # Als een participant een specifieke dot_id mist
                 # Voeg een NaN rij toe zodat de structuur behouden blijft voor aggregatie
                current_participant_dots_summary_list.append({
                    'dot_id': dot_id_val, 'median_offset_cm': np.nan, 
                    'eucl_dist_gaze_to_median_cm': np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0
                })
                continue
            
            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]
            if target_info.empty: 
                print(f"  WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val} voor pp {participant_id} (onverwacht).")
                current_participant_dots_summary_list.append({
                    'dot_id': dot_id_val, 'median_offset_cm': np.nan, 
                    'eucl_dist_gaze_to_median_cm': np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0
                })
                continue
            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])
            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))
            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)
            current_participant_dots_summary_list.append(summary)
        
        summary_df_per_dot_current_pp = pd.DataFrame(current_participant_dots_summary_list)
        
        if not summary_df_per_dot_current_pp.empty:
            summary_df_per_dot_current_pp['participant_id'] = participant_id 
            all_participants_dot_summaries_list.append(summary_df_per_dot_current_pp)
            print(f"Statistieken per doelpunt berekend voor {participant_id}.")
        else:
            print(f"Kon geen (valide) statistieken per doelpunt berekenen voor {participant_id}.")

print("\nAlle participanten verwerkt.")

if not all_participants_dot_summaries_list:
    print("Geen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.")
else:
    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries_list, ignore_index=True)
    print(f"\nGecombineerde per-dot data van alle participanten. Totaal aantal observaties (dots x PPs): {len(final_dot_summaries_all_pps_df)}")

    # --- A. Gemiddelde Checkerboard Heatmaps (over alle participanten) ---
    # Titels en stijl identiek aan de webcam output_10_X.png voorbeelden
    print("\nGenereren van gemiddelde checkerboard heatmaps over alle participanten...")
    if not final_dot_summaries_all_pps_df.empty:
        final_dot_summaries_all_pps_df['dot_id'] = pd.to_numeric(final_dot_summaries_all_pps_df['dot_id'], errors='coerce')
        final_dot_summaries_all_pps_df.dropna(subset=['dot_id'], inplace=True)
        if not final_dot_summaries_all_pps_df.empty:
             final_dot_summaries_all_pps_df['dot_id'] = final_dot_summaries_all_pps_df['dot_id'].astype(int)
        
        averaged_dot_metrics = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()

        if not averaged_dot_metrics.empty:
            plot_df_avg_checkerboard = pd.DataFrame({'dot_id': range(len(positions_to_fill_13_dots))})
            plot_df_avg_checkerboard = pd.merge(plot_df_avg_checkerboard, averaged_dot_metrics, on='dot_id', how='left')
            
            if len(averaged_dot_metrics) < len(positions_to_fill_13_dots):
                 print(f"  WAARSCHUWING: Gemiddelde data voor checkerboard heatmap beschikbaar voor {len(averaged_dot_metrics)} van de {len(positions_to_fill_13_dots)} verwachte unieke dot posities.")
            
            # Definieer de exacte titels zoals in de webcam analyse
            heatmap_titles = {
                'median_offset_cm': 'Offset (cm) Across Positions',
                'eucl_dist_gaze_to_median_cm': 'SD (cm) Across Positions',
                'rms_error_cm': 'RMS error (cm) Across Positions' # Let op klein lettergebruik 'error'
            }
            plot_checkerboard_heatmap_webcam_style(plot_df_avg_checkerboard, measurement='median_offset_cm', title_str=heatmap_titles['median_offset_cm'] + ' (EyeLink - Gem. Alle PPs)', positions_to_fill_list=positions_to_fill_13_dots)
            plot_checkerboard_heatmap_webcam_style(plot_df_avg_checkerboard, measurement='eucl_dist_gaze_to_median_cm', title_str=heatmap_titles['eucl_dist_gaze_to_median_cm'] + ' (EyeLink - Gem. Alle PPs)', positions_to_fill_list=positions_to_fill_13_dots)
            plot_checkerboard_heatmap_webcam_style(plot_df_avg_checkerboard, measurement='rms_error_cm', title_str=heatmap_titles['rms_error_cm'] + ' (EyeLink - Gem. Alle PPs)', positions_to_fill_list=positions_to_fill_13_dots)
        else:
            print("Geen data om gemiddelde checkerboard heatmaps te plotten na groeperen.")
    else:
        print("Geen gecombineerde data beschikbaar voor checkerboard heatmaps.")

    # --- B. Overige Gecombineerde Plots (identiek aan webcam output_14_X en output_16_X) ---
    print("\nGenereren van gecombineerde beschrijvende statistieken en andere plots...")
    if not final_dot_summaries_all_pps_df.empty:
        # B.1. Tabel met Beschrijvende Statistieken
        desc_stats_data_all = {
            'Metric': ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)'],
            'Mean': [
                final_dot_summaries_all_pps_df['median_offset_cm'].mean(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].mean(),
                final_dot_summaries_all_pps_df['rms_error_cm'].mean()
            ],
            'Std Dev': [
                final_dot_summaries_all_pps_df['median_offset_cm'].std(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].std(),
                final_dot_summaries_all_pps_df['rms_error_cm'].std()
            ],
            'Min': [
                final_dot_summaries_all_pps_df['median_offset_cm'].min(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].min(),
                final_dot_summaries_all_pps_df['rms_error_cm'].min()
            ],
            'Median': [
                final_dot_summaries_all_pps_df['median_offset_cm'].median(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].median(),
                final_dot_summaries_all_pps_df['rms_error_cm'].median()
            ],
            'Max': [
                final_dot_summaries_all_pps_df['median_offset_cm'].max(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].max(),
                final_dot_summaries_all_pps_df['rms_error_cm'].max()
            ]
        }
        overall_desc_stats_df = pd.DataFrame(desc_stats_data_all)
        print(f"\nDescriptive Statistics for Offset, Precision (SD), and RMS Error (EyeLink - All PPs):")
        print(overall_desc_stats_df.to_string(index=False))

        # B.2. Staafdiagram van Gemiddelde Prestaties
        plt.figure(figsize=(8, 6))
        barplot_data_overall = overall_desc_stats_df.set_index('Metric')
        metric_order = ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)']
        barplot_data_overall = barplot_data_overall.reindex(metric_order)
        sns.barplot(x=barplot_data_overall.index, y='Mean', data=barplot_data_overall, palette=['#1f77b4', '#ff7f0e', '#2ca02c'])
        plt.title('Average System Performance Metrics (EyeLink - All PPs)', fontsize=16)
        plt.ylabel('Value (cm)', fontsize=12)
        plt.xlabel('') 
        plt.xticks(ticks=range(len(barplot_data_overall.index)), labels=barplot_data_overall.index, rotation=0, ha='center', fontsize=10)
        plt.tight_layout()
        plt.show()

        # B.3. Overall Boxplot van Distributies (Offset, SD, RMS)
        metrics_for_overall_boxplot = {
            'median_offset_cm': ('Offset (cm)', 'skyblue', 'Distribution of Offset Values Across Calibration Points'),
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'lightcoral', 'Distribution of Precision (SD) Values Across Calibration Points'),
            'rms_error_cm': ('RMS Error (cm)', 'lightgreen', 'Distribution of RMS Error Values Across Calibration Points')
        }
        for metric_col, (ylabel_text, color, plot_title_text_base) in metrics_for_overall_boxplot.items():
            plt.figure(figsize=(5, 6)) 
            sns.boxplot(y=final_dot_summaries_all_pps_df[metric_col], color=color, width=0.5)
            plt.title(plot_title_text_base + ' (EyeLink - All PPs)', fontsize=14)
            plt.ylabel(ylabel_text, fontsize=12)
            plt.xlabel(f'{len(positions_to_fill_13_dots)} Calibration Points', fontsize=10) # X-label zoals in webcam voorbeeld plot output_14_3.png
            plt.xticks([]) 
            plt.tight_layout()
            plt.show()

        # B.4. Scatterplot van Offset vs. Precisie (SD) (overall)
        plt.figure(figsize=(8, 6))
        sns.scatterplot(x=final_dot_summaries_all_pps_df['median_offset_cm'], y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], alpha=0.5)
        plt.title('Accuracy (Offset) vs. Precision (SD) per Point (EyeLink - All PPs)', fontsize=16)
        plt.xlabel('Offset (cm)', fontsize=12)
        plt.ylabel('Precision (SD, cm)', fontsize=12)
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        # --- B.5. Plots: Metriek per dot across participants (Custom Stijl) ---
        print("\nGenereren van plots: metriek per doelpunt over participanten (custom stijl)...")
        # Titels moeten overeenkomen met webcam analyse (bijv. "Distribution of Offset (cm) per Dot ID")
        metrics_to_plot_per_dot = {
            'median_offset_cm': ('Offset (cm)', 'Distribution of Offset (cm) per Dot ID'), 
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'Distribution of Precision (SD, cm) per Dot ID'),
            'rms_error_cm': ('RMS Error (cm)', 'Distribution of RMS Error (cm) per Dot ID')
        }
        
        dot_id_order_for_x_axis = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())
        # Filter op dot_ids die we verwachten (0-12 voor 13 punten)
        dot_id_order_for_x_axis = [did for did in dot_id_order_for_x_axis if did < len(positions_to_fill_13_dots)]
        plot_data_for_dots = final_dot_summaries_all_pps_df[final_dot_summaries_all_pps_df['dot_id'].isin(dot_id_order_for_x_axis)]

        if not plot_data_for_dots.empty and dot_id_order_for_x_axis:
            for metric_col, (ylabel_text, plot_title_base) in metrics_to_plot_per_dot.items():
                plot_metric_per_dot_webcam_style(plot_data_for_dots, 
                                                 metric_col=metric_col, 
                                                 y_label=ylabel_text, 
                                                 title_str=plot_title_base, # De functie voegt (EyeLink - All PPs) niet meer toe
                                                 dot_id_order=dot_id_order_for_x_axis)
        else:
            print("Geen data beschikbaar voor 'per dot across participants' plots.")
    else:
        print("Geen gecombineerde data beschikbaar voor het genereren van plots.")
