{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# EyeLink Accuracy/Precision Analysis (Batch Processing - EXACTE Replicatie Webcam Analyse)\n", "\n", "**Doel:** Dit notebook repliceert de accuracy/precision analyse zoals uitgevoerd in `deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb` (webcam data), maar past deze toe op de EyeLink data.\n", "Helper functies (`compute_rms`, `pixels_to_cm`, `calculate_summary_stats`, `plot_matrix` voor heatmaps) zijn direct overgenomen uit het genoemde webcam notebook.\n", "Voor overige plots (tabel, staafdiagram, boxplots, scatterplot), die niet expliciet in het verstrekte webcam notebook gedefinieerd waren, worden functies gebruikt die de stijl van de voorbeeldafbeeldingen repliceren.\n", "\n", "**Input:** `.xls` bestanden per participant uit de EyeLink.\n", "**Output (geaggregeerd over alle participanten):** <PERSON><PERSON><PERSON><PERSON> set plots als de webcam analyse."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries\n", "Overgenomen uit `deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb` waar relevant, a<PERSON>evuld met `os`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib import cm \n", "import matplotlib.patches as patches \n", "import os\n", "import astropy.convolution as krn # Geïmporteerd in webcam notebook\n", "import scipy.stats as stats      # Geïmporteerd in webcam notebook\n", "import seaborn as sns\n", "import statsmodels.api as sm     # Geïmporteerd in webcam notebook\n", "from statsmodels.stats.anova import AnovaRM # Geïmporteerd in webcam notebook\n", "\n", "# from deepeye_analysis_package.plotting import makeHeat # Geïmporteerd in webcam notebook, maar niet gebruikt voor deze specifieke heatmaps\n", "\n", "import openpyxl \n", "import xlrd     "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions (Exact overgenomen of direct gebaseerd op de Webcam Analyse Notebook)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# compute_rms zoals gedefinieerd in de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb, cel 2)\n", "def compute_rms(samples):\n", "    samples_arr = np.array(samples)\n", "    if samples_arr.ndim != 2 or samples_arr.shape[1] != 2:\n", "        if samples_arr.ndim == 1 and len(samples_arr) % 2 == 0 and len(samples_arr) >= 4:\n", "            samples_arr = samples_arr.reshape(-1, 2)\n", "        else:\n", "            return np.nan\n", "            \n", "    samples_cleaned_for_diff = samples_arr[~np.isnan(samples_arr).any(axis=1)]\n", "\n", "    if samples_cleaned_for_diff.shape[0] < 2: \n", "        return np.nan\n", "        \n", "    deltas = np.diff(samples_cleaned_for_diff, axis=0)\n", "    if deltas.shape[0] == 0: \n", "        return np.nan\n", "        \n", "    distances_sq = np.sum(deltas**2, axis=1)\n", "    distances_sq_cleaned = distances_sq[~np.isnan(distances_sq)]\n", "    if distances_sq_cleaned.size == 0:\n", "        return np.nan\n", "        \n", "    rms = np.sqrt(np.mean(distances_sq_cleaned))\n", "    return rms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pixels_to_cm zoals gedefinieerd in de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb, cel 7)\n", "# De originele functie had 'screen_width_px' en 'physical_screen_width_cm' als parameters in de definitie.\n", "# De aanroep in de commentaren van die cel liet zien dat voor y-coördinaten height parameters werden gebruikt.\n", "# Deze functie is aangepast om generiek te zijn voor een dimensie.\n", "def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):\n", "    \"\"\"Converts pixel values to centimeters for a given dimension.\"\"\"\n", "    pixels_numeric = pd.to_numeric(pixels, errors='coerce')\n", "    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:\n", "        return np.nan\n", "    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# calculate_summary_stats zoals gedefinieerd in de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb, cel 8)\n", "# Input df_dot verwacht kolommen 'gaze_x_cm' en 'gaze_y_cm'\n", "def calculate_summary_stats(dot_id, df_dot, target_coords_cm):\n", "    \"\"\"\n", "    Calculates median offset, precision (mean Euclidean distance to median gaze),\n", "    and RMS error for a given target dot. From webcam notebook.\n", "    Returns a Pandas Series.\n", "    \"\"\"\n", "    precision_col_name = 'eucl_dist_gaze_to_median_cm'\n", "\n", "    if 'gaze_x_cm' not in df_dot.columns or 'gaze_y_cm' not in df_dot.columns:\n", "        return pd.Series({\n", "            'dot_id': dot_id, 'median_offset_cm': np.nan,\n", "            precision_col_name: np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0\n", "        })\n", "        \n", "    df_dot_cleaned = df_dot[['gaze_x_cm', 'gaze_y_cm']].dropna()\n", "    gaze_points_for_dot = list(zip(df_dot_cleaned['gaze_x_cm'], df_dot_cleaned['gaze_y_cm']))\n", "\n", "    if not gaze_points_for_dot:\n", "        return pd.Series({\n", "            'dot_id': dot_id, 'median_offset_cm': np.nan,\n", "            precision_col_name: np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0\n", "        })\n", "\n", "    gaze_array_cm_cleaned = np.array(gaze_points_for_dot)\n", "    \n", "    if gaze_array_cm_cleaned.shape[0] == 0:\n", "         return pd.Series({\n", "            'dot_id': dot_id, 'median_offset_cm': np.nan,\n", "            precision_col_name: np.nan, 'rms_error_cm': np.nan, \n", "            'num_fixations': len(df_dot) # Geb<PERSON>ik len(df_dot) voor origineel aantal fixaties voor deze dot\n", "        })\n", "\n", "    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])\n", "    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])\n", "    median_gaze_coords_cm = np.array([median_gaze_x_cm, median_gaze_y_cm])\n", "\n", "    target_coords_cm_arr = np.array(target_coords_cm)\n", "    if np.isnan(target_coords_cm_arr).any() or np.isnan(median_gaze_coords_cm).any():\n", "        offset_cm = np.nan\n", "    else:\n", "        offset_cm = np.linalg.norm(median_gaze_coords_cm - target_coords_cm_arr)\n", "\n", "    if np.isnan(median_gaze_coords_cm).any():\n", "        distances_to_median_cm = [] \n", "    else:\n", "        distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]\n", "    \n", "    precision_val = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan\n", "    rms_cm = compute_rms(gaze_array_cm_cleaned) \n", "\n", "    return pd.Series({\n", "        'dot_id': dot_id,\n", "        'median_offset_cm': offset_cm,\n", "        precision_col_name: precision_val,\n", "        'rms_error_cm': rms_cm,\n", "        'num_fixations': len(df_dot)\n", "    })"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# plot_matrix functie, exact overgenomen uit de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb, cell 10)\n", "def plot_matrix(average_per_dot, measurement, title, cmap_str=\"YlGnBu\"):\n", "    \"\"\"Plots a 5x5 checkerboard heatmap using the exact logic from the webcam notebook.\n", "       'positions_to_fill' is defined INSIDE this function as per the original notebook.\n", "    \"\"\"\n", "    # Definieer de checkerboard posities zoals in de webcam notebook\n", "    positions_to_fill = [\n", "        (0,0), (0,2), (0,4),  \n", "        (1,1), (1,3),          \n", "        (2,0), (2,2), (2,4),  \n", "        (3,1), (3,3),          \n", "        (4,0), (4,2), (4,4)   \n", "    ]\n", "    \n", "    # Bepaal cbar label (de originele webcam notebook gebruikte 'measurement' direct)\n", "    if measurement == 'median_offset_cm':\n", "        cbar_label_text = 'Offset (cm)' \n", "    elif measurement == 'eucl_dist_gaze_to_median_cm':\n", "        cbar_label_text = 'SD (cm)' \n", "    elif measurement == 'rms_error_cm':\n", "        cbar_label_text = 'RMS error (cm)'\n", "    else:\n", "        cbar_label_text = measurement\n", "\n", "    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan))\n", "    \n", "    # average_per_dot is de DataFrame met metrieken per dot_id (0-12 voor 13 punten)\n", "    # Zorg dat het gesorteerd is op dot_id voor correcte mapping met de impliciete volgorde van positions_to_fill\n", "    # De originele webcam notebook deed: average_per_dot[measurement].values[idx]\n", "    # Dit impliceert dat average_per_dot al correct gesorteerd en geïndexeerd (0..12) was.\n", "    df_sorted_for_plot = average_per_dot.sort_values(by='dot_id').reset_index(drop=True)\n", "\n", "    for idx, (row, col) in enumerate(positions_to_fill): # Origineel: (row, col)\n", "        if idx < len(df_sorted_for_plot):\n", "            # De originele webcam notebook had: dot_matrix.iloc[col, row] = average_per_dot[measurement].values[idx]\n", "            # Dit is de letterlijke replicatie van die indexering.\n", "            # (row, col) uit positions_to_fill zijn hier (visual_row, visual_col)\n", "            # iloc[col, row] betekent iloc[visual_col, visual_row]\n", "            # Dit zal de heatmap vullen alsof de matrix getransponeerd is t.o.v. de (row,col) tuples.\n", "            # Als de 'positions_to_fill' bedoeld zijn als (row_index, col_index) voor de matrix,\n", "            # dan zou het dot_matrix.iloc[row, col] moeten zijn. Ik volg nu de letterlijke code.\n", "            value = df_sorted_for_plot.loc[df_sorted_for_plot['dot_id'] == idx, measurement].values\n", "            if len(value) > 0:\n", "                 dot_matrix.iloc[col, row] = value[0] # Exacte indexering uit webcam notebook\n", "            else:\n", "                 dot_matrix.iloc[col, row] = np.nan # Als een dot_id ontbreekt in df_sorted_for_plot\n", "\n", "    plt.figure(figsize=(8, 8))\n", "    heatmap_ax = sns.heatmap(dot_matrix, annot=True, fmt=\".2f\", cmap=cmap_str,\n", "                             square=True, annot_kws={\"size\": 12}, linewidths=0.5,\n", "                             linecolor='gray', cbar_kws={'label': cbar_label_text})\n", "    \n", "    plt.title(title, fontsize=16, weight='bold', pad=12)\n", "    plt.xlabel('Column', fontsize=14, labelpad=10)\n", "    plt.ylabel('Row', fontsize=14, labelpad=10)\n", "    heatmap_ax.set_xticklabels(range(1, 6), fontsize=12)\n", "    heatmap_ax.set_yticklabels(range(1, 6), fontsize=12, rotation=0)\n", "    if heatmap_ax.collections: \n", "        heatmap_ax.collections[0].colorbar.ax.tick_params(labelsize=12)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# plot_metric_per_dot_webcam_style, om de stijl van image_73b5a0.png (webcam) te repliceren\n", "# Deze functie was NIET expliciet in de webcam .ipynb, dus dit is een replicatie van de stijl.\n", "def plot_metric_per_dot_webcam_style(data_df, metric_col, y_label, title_str, dot_id_order):\n", "    \"\"\"Genereert een boxplot per dot_id met individuele rode punten en alternerende achtergrondkleur.\"\"\"\n", "    \n", "    plt.style.use('seaborn-v0_8-whitegrid') \n", "    fig, ax = plt.subplots(figsize=(14, 7)) \n", "    \n", "    # Zorg dat data_df alleen de dot_ids bevat die in dot_id_order staan voor correct plotten\n", "    plot_data = data_df[data_df['dot_id'].isin(dot_id_order)]\n", "    \n", "    # Maak een mapping van dot_id naar numerieke index voor de x-as posities\n", "    # Dit is nodig omdat axvspan en boxplot/stripplot numerieke posities verwachten voor categorieën.\n", "    dot_id_map = {dot_id: i for i, dot_id in enumerate(dot_id_order)}\n", "    # plot_data['x_pos'] = plot_data['dot_id'].map(dot_id_map)\n", "\n", "    for i, _ in enumerate(dot_id_order):\n", "        if i % 2 == 1: \n", "            ax.axvspan(i - 0.5, i + 0.5, facecolor='whitesmoke', alpha=1, zorder=0) \n", "            \n", "    # Gebruik de 'order' parameter om de volgorde van boxen te specificeren\n", "    sns.boxplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order,\n", "                color='white', width=0.7, showfliers=False, \n", "                medianprops={'color': 'black', 'linewidth': 2},\n", "                boxprops={'edgecolor': 'black', 'linewidth': 1.5, 'facecolor':'white'},\n", "                whiskerprops={'color': 'black', 'linewidth': 1.5},\n", "                capprops={'color': 'black', 'linewidth': 1.5},\n", "                ax=ax, zorder=2)\n", "    \n", "    sns.stripplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order, \n", "                  color='red', jitter=0.15, alpha=0.8, size=6, ax=ax, zorder=1)\n", "    \n", "    ax.set_title(title_str + ' (EyeLink - All PPs)', fontsize=16, weight='bold', pad=15)\n", "    ax.set_xlabel('Dot ID', fontsize=14, labelpad=10)\n", "    ax.set_ylabel(y_label, fontsize=14, labelpad=10)\n", "    \n", "    ax.set_xticks(ticks=range(len(dot_id_order)))\n", "    ax.set_xticklabels(dot_id_order, fontsize=12) # Labels zijn de daadwerkelijke dot_id waarden\n", "    ax.tick_params(axis='y', labelsize=12)\n", "    \n", "    ax.grid(True, axis='y', linestyle=':', color='lightgray', zorder=-2) \n", "    ax.set_facecolor('white') \n", "    fig.patch.set_facecolor('white') \n", "    \n", "    ax.spines['top'].set_visible(False)\n", "    ax.spines['right'].set_visible(False)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Main Analysis: EyeLink Data Processing\n", "Data laden en voorbewerken van EyeLink data, da<PERSON><PERSON> analyses en plots identiek aan webcam notebook."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1. Define Base Path and Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scherm parameters, overgenomen uit webcam notebook (cell 7)\n", "screen_params = {\n", "    'physical_width_cm': 34.4, \n", "    'physical_height_cm': 19.3,\n", "    'width_px': 1920,  \n", "    'height_px': 1080 \n", "}\n", "physical_screen_width_cm = screen_params['physical_width_cm']\n", "physical_screen_height_cm = screen_params['physical_height_cm']\n", "screen_width_px = screen_params['width_px']\n", "screen_height_px = screen_params['height_px']\n", "\n", "print(f\"EyeLink physical screen width: {physical_screen_width_cm} cm\")\n", "print(f\"EyeLink physical screen height: {physical_screen_height_cm} cm\")\n", "print(f\"EyeLink screen resolution: {screen_width_px}x{screen_height_px} px\")\n", "\n", "base_data_path = './CollectedData/eyelink_data/' \n", "print(f\"Base data path for EyeLink data: {base_data_path}\")\n", "\n", "try:\n", "    folder_names = [\n", "        name for name in os.listdir(base_data_path)\n", "        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')\n", "    ]\n", "    if not folder_names:\n", "        print(f\"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'.\")\n", "    else:\n", "        print(f\"Gevonden EyeLink participant mappen: {sorted(folder_names)}\")\n", "except FileNotFoundError:\n", "    print(f\"FOUT: Basis data pad niet gevonden: '{base_data_path}'.\")\n", "    folder_names = []\n", "\n", "# positions_to_fill zoals gedefinieerd in de webcam notebook (cell 10) (daar heette het 'positions_to_fill')\n", "# Deze globale variabele wordt gebruikt door de gekopieerde plot_matrix functie.\n", "positions_to_fill = [\n", "    (0,0), (0,2), (0,4),  \n", "    (1,1), (1,3),          \n", "    (2,0), (2,2), (2,4),  \n", "    (3,1), (3,3),          \n", "    (4,0), (4,2), (4,4)   \n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2. Loop Through Participants: Data Loading and Metric Calculation (EyeLink Data)\n", "De output van deze cel is een lijst `all_participants_dot_summaries_list` \n", "die per participant een DataFrame bevat met de metrieken per dot."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_participants_dot_summaries_list = []\n", "\n", "if not folder_names:\n", "    print(\"Geen participant mappen om te verwerken. Script stopt.\")\n", "else:\n", "    for participant_id in sorted(folder_names): \n", "        print(f\"\\n--- Processing Participant: {participant_id} ---\")\n", "        \n", "        file_path = os.path.join(base_data_path, participant_id, \"Output\", f\"{participant_id}.xls\")\n", "        print(f\"Pogen data te laden van: {file_path}\")\n", "\n", "        df_eyelink_raw = None\n", "        loaded_successfully = False\n", "        try:\n", "            print(f\"  Poging 1: Laden als TSV... {file_path}\")\n", "            df_eyelink_raw = pd.read_csv(file_path, sep='\\t', low_memory=False)\n", "            print(f\"    Succesvol geladen als TSV. Vorm: {df_eyelink_raw.shape}\")\n", "            loaded_successfully = True\n", "        except Exception:\n", "            try:\n", "                print(f\"  Poging 2: Laden als CSV... {file_path}\")\n", "                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)\n", "                print(f\"    Succesvol geladen als CSV. Vorm: {df_eyelink_raw.shape}\")\n", "                loaded_successfully = True\n", "            except Exception:\n", "                try:\n", "                    print(f\"  Poging 3: <PERSON>den als Excel (.xls) met xlrd... {file_path}\")\n", "                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')\n", "                    print(f\"    Succesvol geladen als Excel (.xls) met xlrd. Vorm: {df_eyelink_raw.shape}\")\n", "                    loaded_successfully = True\n", "                except Exception:\n", "                    try:\n", "                        print(f\"  Poging 4: <PERSON>den als Excel (.xlsx style) met openpyxl... {file_path}\")\n", "                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')\n", "                        print(f\"    Succesvol geladen als Excel (.xlsx style) met openpyxl. Vorm: {df_eyelink_raw.shape}\")\n", "                        loaded_successfully = True\n", "                    except Exception as e_final:\n", "                        print(f\"  KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} ({e_final}). Sla over.\")\n", "                        continue \n", "\n", "        if not loaded_successfully or df_eyelink_raw is None:\n", "            continue\n", "\n", "        if 'arrowLength' not in df_eyelink_raw.columns:\n", "            print(f\"FOUT: <PERSON><PERSON><PERSON> 'arrowLength' niet gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)\n", "        df_participant_preproc = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith(\"UNDEFINED\")].copy()\n", "\n", "        if df_participant_preproc.empty:\n", "            print(f\"Geen accuracy/precision data gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        # EyeLink kolommen voor gaze en target (in pixels)\n", "        eyelink_gaze_x_col = 'CURRENT_FIX_X'\n", "        eyelink_gaze_y_col = 'CURRENT_FIX_Y'\n", "        eyelink_target_x_col = 'accprecX'\n", "        eyelink_target_y_col = 'accprecY'\n", "\n", "        required_pixel_cols = [eyelink_gaze_x_col, eyelink_gaze_y_col, eyelink_target_x_col, eyelink_target_y_col]\n", "        missing_cols = [col for col in required_pixel_cols if col not in df_participant_preproc.columns]\n", "        if missing_cols:\n", "            print(f\"FOUT: Vereiste pixelkolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        for col in required_pixel_cols:\n", "            df_participant_preproc[col] = pd.to_numeric(df_participant_preproc[col], errors='coerce')\n", "        df_participant_preproc.dropna(subset=required_pixel_cols, inplace=True)\n", "        if df_participant_preproc.empty:\n", "            print(f\"Geen valide data na numerieke conversie van pixelkolommen voor {participant_id}. Sla over.\")\n", "            continue\n", "\n", "        # Pixel naar CM conversie -> resulteert in 'gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm'\n", "        df_participant_preproc['gaze_x_cm'] = pixels_to_cm(df_participant_preproc[eyelink_gaze_x_col], screen_width_px, physical_screen_width_cm)\n", "        df_participant_preproc['gaze_y_cm'] = pixels_to_cm(df_participant_preproc[eyelink_gaze_y_col], screen_height_px, physical_screen_height_cm)\n", "        df_participant_preproc['target_x_cm'] = pixels_to_cm(df_participant_preproc[eyelink_target_x_col], screen_width_px, physical_screen_width_cm)\n", "        df_participant_preproc['target_y_cm'] = pixels_to_cm(df_participant_preproc[eyelink_target_y_col], screen_height_px, physical_screen_height_cm)\n", "        \n", "        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']\n", "        df_participant_preproc.dropna(subset=cm_cols_to_check, inplace=True)\n", "        if df_participant_preproc.empty:\n", "            print(f\"Geen valide data na cm-conversie voor {participant_id}. Sla over.\")\n", "            continue\n", "\n", "        # Identificeer unieke target dots en wijs dot_id toe (0 tot N-1)\n", "        df_participant_preproc['target_x_cm_rounded'] = df_participant_preproc['target_x_cm'].round(3)\n", "        df_participant_preproc['target_y_cm_rounded'] = df_participant_preproc['target_y_cm'].round(3)\n", "        unique_targets_participant = df_participant_preproc[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]\n", "        unique_targets_participant = unique_targets_participant.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])\n", "        unique_targets_participant = unique_targets_participant.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)\n", "        unique_targets_participant['dot_id'] = unique_targets_participant.index \n", "        num_unique_dots_found = len(unique_targets_participant)\n", "        print(f\"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots_found}\")\n", "        if num_unique_dots_found == 0:\n", "            print(f\"FOUT: Geen unieke doelposities gevonden na cm-conversie voor {participant_id}. Sla over.\")\n", "            continue\n", "        if num_unique_dots_found != 13:\n", "             print(f\"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities, gevonden {num_unique_dots_found}.\")\n", "        \n", "        df_participant_preproc_merged = pd.merge(df_participant_preproc, unique_targets_participant[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], \n", "                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')\n", "        \n", "        results_current_pp_list = []\n", "        for dot_id_val in range(len(positions_to_fill)):\n", "            df_dot = df_participant_preproc_merged[df_participant_preproc_merged['dot_id'] == dot_id_val]\n", "            target_info_current = unique_targets_participant[unique_targets_participant['dot_id'] == dot_id_val]\n", "\n", "            if target_info_current.empty: \n", "                # print(f\"  Geen target info voor verwachte dot_id {dot_id_val} voor participant {participant_id}.\")\n", "                summary = pd.Series({\n", "                    'dot_id': dot_id_val, 'median_offset_cm': np.nan,\n", "                    'eucl_dist_gaze_to_median_cm': np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0\n", "                })\n", "            else:\n", "                target_coords_cm_actual = (target_info_current['target_x_cm'].iloc[0], target_info_current['target_y_cm'].iloc[0])\n", "                # Als df_dot leeg is (geen fixaties voor deze dot_id), zal calculate_summary_stats NaNs returnen.\n", "                summary = calculate_summary_stats(dot_id_val, df_dot, target_coords_cm_actual)\n", "            results_current_pp_list.append(summary)\n", "        \n", "        if results_current_pp_list:\n", "            summary_df_per_dot_current_pp = pd.DataFrame(results_current_pp_list)\n", "            if not summary_df_per_dot_current_pp.empty:\n", "                summary_df_per_dot_current_pp['participant_id'] = participant_id \n", "                all_participants_dot_summaries_list.append(summary_df_per_dot_current_pp)\n", "                print(f\"Statistieken per doelpunt berekend voor {participant_id}.\")\n", "            else:\n", "                print(f\"DataFrame met statistieken per doelpunt is leeg voor {participant_id}.\")\n", "        else:\n", "            print(f\"Kon geen statistieken per doelpunt genereren voor {participant_id}.\")\n", "\n", "print(\"\\nAlle participanten verwerkt.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3. <PERSON><PERSON><PERSON><PERSON><PERSON> van Resultaten en Genereren van Plots (Identiek aan Webcam Analyse Stijl)\n", "Alle plots hieronder zijn gebaseerd op de geaggregeerde data van alle succesvol verwerkte participanten \n", "en gebruiken de plotstijl en -logica van `deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb` (voor heatmaps) \n", "of replice<PERSON> de stijl van de door de gebruiker aangeleverde voorbeeldafbeeldingen (voor overige plots)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not all_participants_dot_summaries_list:\n", "    print(\"Geen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.\")\n", "else:\n", "    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries_list, ignore_index=True)\n", "    print(f\"\\nGecombineerde per-dot data van alle participanten. Totaal aantal rijen: {len(final_dot_summaries_all_pps_df)}\")\n", "\n", "    # --- <PERSON><PERSON>erboard Heatmaps (over alle participanten) ---\n", "    # Gebruik de 'plot_matrix' functie zoals gedefinieerd in de webcam notebook.\n", "    print(\"\\nGenereren van gemiddelde checkerboard heatmaps over alle participanten...\")\n", "    if not final_dot_summaries_all_pps_df.empty:\n", "        final_dot_summaries_all_pps_df['dot_id'] = pd.to_numeric(final_dot_summaries_all_pps_df['dot_id'], errors='coerce')\n", "        final_dot_summaries_all_pps_df.dropna(subset=['dot_id'], inplace=True)\n", "        if not final_dot_summaries_all_pps_df.empty:\n", "             final_dot_summaries_all_pps_df['dot_id'] = final_dot_summaries_all_pps_df['dot_id'].astype(int)\n", "        \n", "        # 'average_per_dot' is de naam die de plot_matrix functie in de webcam notebook verwachtte\n", "        average_per_dot = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()\n", "\n", "        if not average_per_dot.empty:\n", "            expected_dot_ids_df = pd.DataFrame({'dot_id': range(len(positions_to_fill))})\n", "            average_per_dot_for_plot = pd.merge(expected_dot_ids_df, average_per_dot, on='dot_id', how='left')\n", "            \n", "            if len(average_per_dot) < len(positions_to_fill):\n", "                 print(f\"  WAARSCHUWING: Gemiddelde data voor checkerboard heatmap beschikbaar voor {len(average_per_dot)} van de {len(positions_to_fill)} verwachte unieke dot posities.\")\n", "            \n", "            # Titels exact zoals in de webcam notebook, met toevoeging voor EyeLink\n", "            plot_matrix(average_per_dot_for_plot, measurement='median_offset_cm', title='Offset (cm) Across Positions (EyeLink - Gem. Alle PPs)')\n", "            plot_matrix(average_per_dot_for_plot, measurement='eucl_dist_gaze_to_median_cm', title='SD (cm) Across Positions (EyeLink - Gem. Alle PPs)')\n", "            plot_matrix(average_per_dot_for_plot, measurement='rms_error_cm', title='RMS error (cm) Across Positions (EyeLink - Gem. Alle PPs)')\n", "        else:\n", "            print(\"Geen data om gemiddelde checkerboard heatmaps te plotten na g<PERSON>en.\")\n", "    else:\n", "        print(\"Geen gecombineerde data beschikbaar voor checkerboard heatmaps.\")\n", "\n", "    # --- <PERSON><PERSON> <PERSON> Gecombineerde Plots (stijl identiek aan webcam output_14_X en output_16_X) ---\n", "    print(\"\\nGenereren van gecombineerde beschrijvende statistieken en andere plots...\")\n", "    if not final_dot_summaries_all_pps_df.empty:\n", "        # B.1. <PERSON><PERSON> met Beschrijvende Statistieken\n", "        desc_stats_data_all = {\n", "            'Metric': ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)'],\n", "            'Mean': [\n", "                final_dot_summaries_all_pps_df['median_offset_cm'].mean(),\n", "                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].mean(),\n", "                final_dot_summaries_all_pps_df['rms_error_cm'].mean()\n", "            ],\n", "            'Std Dev': [\n", "                final_dot_summaries_all_pps_df['median_offset_cm'].std(),\n", "                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].std(),\n", "                final_dot_summaries_all_pps_df['rms_error_cm'].std()\n", "            ],\n", "            'Min': [\n", "                final_dot_summaries_all_pps_df['median_offset_cm'].min(),\n", "                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].min(),\n", "                final_dot_summaries_all_pps_df['rms_error_cm'].min()\n", "            ],\n", "            'Median': [\n", "                final_dot_summaries_all_pps_df['median_offset_cm'].median(),\n", "                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].median(),\n", "                final_dot_summaries_all_pps_df['rms_error_cm'].median()\n", "            ],\n", "            'Max': [\n", "                final_dot_summaries_all_pps_df['median_offset_cm'].max(),\n", "                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].max(),\n", "                final_dot_summaries_all_pps_df['rms_error_cm'].max()\n", "            ]\n", "        }\n", "        overall_desc_stats_df = pd.DataFrame(desc_stats_data_all)\n", "        print(f\"\\nDescriptive Statistics for Offset, Precision (SD), and RMS Error (EyeLink - All PPs):\")\n", "        print(overall_desc_stats_df.to_string(index=False))\n", "\n", "        # B.2. Staafdiagram van Gemiddelde Prestaties\n", "        plt.figure(figsize=(8, 6))\n", "        barplot_data_overall = overall_desc_stats_df.set_index('Metric')\n", "        metric_order = ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)']\n", "        barplot_data_overall = barplot_data_overall.reindex(metric_order)\n", "        sns.barplot(x=barplot_data_overall.index, y='Mean', data=barplot_data_overall, palette=['#1f77b4', '#ff7f0e', '#2ca02c'])\n", "        plt.title('Average System Performance Metrics (EyeLink - All PPs)', fontsize=16)\n", "        plt.ylabel('Value (cm)', fontsize=12)\n", "        plt.xlabel('') \n", "        plt.xticks(ticks=range(len(barplot_data_overall.index)), labels=barplot_data_overall.index, rotation=0, ha='center', fontsize=10)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # B.3. Overall Boxplot van Distributies (Offset, SD, RMS)\n", "        metrics_for_overall_boxplot = {\n", "            'median_offset_cm': ('Offset (cm)', 'skyblue', 'Distribution of Offset Values Across Calibration Points'),\n", "            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'lightcoral', 'Distribution of Precision (SD) Values Across Calibration Points'),\n", "            'rms_error_cm': ('RMS Error (cm)', 'lightgreen', 'Distribution of RMS Error Values Across Calibration Points')\n", "        }\n", "        for metric_col, (ylabel_text, color, plot_title_text_base) in metrics_for_overall_boxplot.items():\n", "            plt.figure(figsize=(5, 6)) \n", "            sns.boxplot(y=final_dot_summaries_all_pps_df[metric_col], color=color, width=0.5)\n", "            plt.title(plot_title_text_base + ' (EyeLink - All PPs)', fontsize=14)\n", "            plt.ylabel(ylabel_text, fontsize=12)\n", "            plt.xlabel(f'{len(positions_to_fill)} Calibration Points', fontsize=10) \n", "            plt.xticks([]) \n", "            plt.tight_layout()\n", "            plt.show()\n", "\n", "        # B.4. <PERSON><PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON> (SD) (overall)\n", "        plt.figure(figsize=(8, 6))\n", "        sns.scatterplot(x=final_dot_summaries_all_pps_df['median_offset_cm'], y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], alpha=0.5)\n", "        plt.title('Accuracy (Offset) vs. Precision (SD) per Point (EyeLink - All PPs)', fontsize=16)\n", "        plt.xlabel('Offset (cm)', fontsize=12)\n", "        plt.ylabel('Precision (SD, cm)', fontsize=12)\n", "        plt.grid(True)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # --- B.5. Plots: Metriek per dot across participants (Custom Stijl) ---\n", "        print(\"\\nGenereren van plots: metriek per doelpunt over participanten (custom stijl)...\")\n", "        metrics_titles_per_dot = {\n", "            'median_offset_cm': ('Offset (cm)', 'Distribution of Offset Values per Dot ID'), \n", "            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'Distribution of Precision (SD) Values per Dot ID'),\n", "            'rms_error_cm': ('RMS Error (cm)', 'Distribution of RMS Error Values per Dot ID')\n", "        }\n", "        \n", "        unique_dot_ids_in_final_summary = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())\n", "        dot_id_order_for_plot = [did for did in unique_dot_ids_in_final_summary if did < len(positions_to_fill)]\n", "\n", "        plot_data_for_dots = final_dot_summaries_all_pps_df[final_dot_summaries_all_pps_df['dot_id'].isin(dot_id_order_for_plot)]\n", "\n", "        if not plot_data_for_dots.empty and dot_id_order_for_plot:\n", "            for metric_col, (ylabel_text, plot_title_base) in metrics_titles_per_dot.items():\n", "                plot_metric_per_dot_webcam_style(plot_data_for_dots, \n", "                                                 metric_col=metric_col, \n", "                                                 y_label=ylabel_text, \n", "                                                 title_str=plot_title_base, \n", "                                                 dot_id_order=dot_id_order_for_plot)\n", "        else:\n", "            print(\"Geen data beschikbaar voor 'per dot across participants' plots.\")\n", "    else:\n", "        print(\"Geen gecombineerde data beschikbaar voor het genereren van plots.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}