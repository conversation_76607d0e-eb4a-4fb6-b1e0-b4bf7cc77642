import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
# Libraries die mogelijk in de oorspronkelijke notebook werden gebruikt:
# from matplotlib import cm 
# import matplotlib.patches as patches
# import astropy.convolution as krn # Als dit niet gebruikt wordt, kan het weg
# import scipy.stats as stats      # Als dit niet gebruikt wordt, kan het weg
# import statsmodels.api as sm     # Als dit niet gebruikt wordt, kan het weg
# from statsmodels.stats.anova import AnovaRM # Als dit niet gebruikt wordt, kan het weg

# Voor het lezen van Excel bestanden
import openpyxl # Voor .xlsx
import xlrd     # Voor .xls (al wordt engine expliciet gespecificeerd)

def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):
    """Converts pixel values to centimeters, identical to webcam analysis logic."""
    pixels_numeric = pd.to_numeric(pixels, errors='coerce')
    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:
        return np.nan
    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm

def compute_rms(samples_cm):
    """Computes Root Mean Square of sequential differences in samples.
       Logica gebaseerd op de compute_rms uit de webcam analyse.
    """
    if not isinstance(samples_cm, np.ndarray):
        samples_cm = np.array(samples_cm)
    
    # Zorg dat lege of NaN-only arrays geen errors geven
    if samples_cm.size == 0 or np.all(np.isnan(samples_cm)):
        return np.nan
    
    # Verwijder rijen waar een van de coördinaten NaN is, voor np.diff
    samples_cm_cleaned_for_diff = samples_cm[~np.isnan(samples_cm).any(axis=1)]

    if samples_cm_cleaned_for_diff.shape[0] < 2: 
        return np.nan
        
    deltas = np.diff(samples_cm_cleaned_for_diff, axis=0)
    if deltas.shape[0] == 0: 
        return np.nan
        
    # np.linalg.norm kan niet met NaNs, dus we kwadrateren en sommeren handmatig
    distances_sq = np.sum(deltas**2, axis=1)
    # Verwijder NaNs die kunnen ontstaan als een delta component NaN was (zou niet moeten na cleaning)
    distances_sq_cleaned = distances_sq[~np.isnan(distances_sq)]
    if distances_sq_cleaned.size == 0:
        return np.nan
        
    rms = np.sqrt(np.mean(distances_sq_cleaned))
    return rms

def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):
    """
    Calculates median offset, precision (mean Euclidean distance to median gaze),
    and RMS error for a given target dot. Consistent with webcam analysis logic.
    """
    if not gaze_points_for_dot_cm:
        return {
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            'eucl_dist_gaze_to_median_cm': np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': 0
        }

    gaze_array_cm = np.array(gaze_points_for_dot_cm)
    gaze_array_cm_cleaned = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]
    
    if gaze_array_cm_cleaned.shape[0] == 0:
         return {
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            'eucl_dist_gaze_to_median_cm': np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': len(gaze_points_for_dot_cm) 
        }

    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])
    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])
    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)

    # Zorg ervoor dat target_coords_cm een numpy array is voor de berekening
    target_coords_cm_arr = np.array(target_coords_cm)
    if np.isnan(target_coords_cm_arr).any() or np.isnan(median_gaze_coords_cm).any():
        offset_cm = np.nan
    else:
        offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - target_coords_cm_arr)

    if np.isnan(median_gaze_coords_cm).any():
        distances_to_median_cm = [] # Kan niet berekenen als mediaan NaN is
    else:
        distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]
    
    precision_metric_cm = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan
    
    rms_cm = compute_rms(gaze_array_cm_cleaned)

    return {
        'dot_id': dot_id,
        'median_offset_cm': offset_cm,
        'eucl_dist_gaze_to_median_cm': precision_metric_cm,
        'rms_error_cm': rms_cm,
        'num_fixations': len(gaze_points_for_dot_cm)
    }

def plot_checkerboard_heatmap_from_markdown(df_aggregated_per_dot, measurement, title, positions_to_fill):
    """Plots a 5x5 checkerboard heatmap based on the logic from deepeye_analyze_MullerLyer_EyeLink_AccPrec13.md."""
    
    # Bepaal de cbar label op basis van measurement, voor consistentie
    if 'Offset' in title:
        cbar_label_text = 'Offset (cm)'
    elif 'SD' in title:
        cbar_label_text = 'SD (cm)'
    elif 'RMS' in title:
        cbar_label_text = 'RMS error (cm)'
    else:
        cbar_label_text = measurement # Fallback
    
    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan))
    
    # Sorteer de input dataframe op 'dot_id' om te matchen met de impliciete volgorde van positions_to_fill
    # Dit gaat ervan uit dat dot_id 0..12 overeenkomt met de 13 posities
    df_sorted = df_aggregated_per_dot.sort_values(by='dot_id').reset_index(drop=True)

    for idx, (row_idx, col_idx) in enumerate(positions_to_fill):
        if idx < len(df_sorted):
            # Haal de waarde op voor de huidige dot_id (idx na sorteren) en de gespecificeerde 'measurement' kolom
            value = df_sorted.loc[idx, measurement]
            dot_matrix.iloc[row_idx, col_idx] = value # In webcam MD was het iloc[col, row], maar iloc[row_idx, col_idx] is standaard

    plt.figure(figsize=(8, 8)) # Grootte zoals in de webcam analyse
    heatmap_ax = sns.heatmap(dot_matrix, annot=True, fmt=".2f", cmap="YlGnBu", # cmap van webcam voorbeeld
                             square=True, annot_kws={"size": 12}, linewidths=0.5,
                             linecolor='gray', cbar_kws={'label': cbar_label_text})
    
    plt.title(title, fontsize=16, weight='bold', pad=12)
    plt.xlabel('Column', fontsize=14, labelpad=10)
    plt.ylabel('Row', fontsize=14, labelpad=10)
    heatmap_ax.set_xticklabels(range(1, 6), fontsize=12) # Labels 1-5 voor assen
    heatmap_ax.set_yticklabels(range(1, 6), fontsize=12, rotation=0)
    if heatmap_ax.collections: # Zorg ervoor dat er een colorbar is om aan te passen
        heatmap_ax.collections[0].colorbar.ax.tick_params(labelsize=12)
    plt.tight_layout()
    plt.show()

# Scherm parameters, consistent met eerdere analyses
PHYSICAL_SCREEN_WIDTH_CM = 34.4
PHYSICAL_SCREEN_HEIGHT_CM = 19.3
EYELINK_SCREEN_WIDTH_PX = 1920 # Identiek aan webcam resolutie in dit voorbeeld
EYELINK_SCREEN_HEIGHT_PX = 1080 # Identiek aan webcam resolutie in dit voorbeeld

print(f"EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm")
print(f"EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm")
print(f"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px")

# Basispad voor EyeLink data
base_data_path = './CollectedData/eyelink_data/' 
print(f"Base data path for EyeLink data: {base_data_path}")

try:
    folder_names = [
        name for name in os.listdir(base_data_path)
        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')
    ]
    if not folder_names:
        print(f"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'.")
    else:
        print(f"Gevonden EyeLink participant mappen: {sorted(folder_names)}")
except FileNotFoundError:
    print(f"FOUT: Basis data pad niet gevonden: '{base_data_path}'.")
    folder_names = []

# Checkerboard posities voor 13 punten in een 5x5 grid (0-indexed)
# Deze mapping MOET overeenkomen met hoe het in de webcam analyse werd gedaan.
positions_to_fill_13_dots = [
    (0,0), (0,2), (0,4),  
    (1,1), (1,3),          
    (2,0), (2,2), (2,4),  
    (3,1), (3,3),          
    (4,0), (4,2), (4,4)   
]

all_participants_dot_summaries_list = []

if not folder_names:
    print("Geen participant mappen om te verwerken. Script stopt.")
else:
    for participant_id in sorted(folder_names): 
        print(f"\n--- Processing Participant: {participant_id} ---")
        
        file_path = os.path.join(base_data_path, participant_id, "Output", f"{participant_id}.xls")
        print(f"Pogen data te laden van: {file_path}")

        df_eyelink_raw = None
        loaded_successfully = False
        try:
            print(f"  Poging 1: Laden als TSV... {file_path}")
            df_eyelink_raw = pd.read_csv(file_path, sep='\t', low_memory=False)
            print(f"    Succesvol geladen als TSV. Vorm: {df_eyelink_raw.shape}")
            loaded_successfully = True
        except Exception:
            try:
                print(f"  Poging 2: Laden als CSV... {file_path}")
                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)
                print(f"    Succesvol geladen als CSV. Vorm: {df_eyelink_raw.shape}")
                loaded_successfully = True
            except Exception:
                try:
                    print(f"  Poging 3: Laden als Excel (.xls) met xlrd... {file_path}")
                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')
                    print(f"    Succesvol geladen als Excel (.xls) met xlrd. Vorm: {df_eyelink_raw.shape}")
                    loaded_successfully = True
                except Exception:
                    try:
                        print(f"  Poging 4: Laden als Excel (.xlsx style) met openpyxl... {file_path}")
                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')
                        print(f"    Succesvol geladen als Excel (.xlsx style) met openpyxl. Vorm: {df_eyelink_raw.shape}")
                        loaded_successfully = True
                    except Exception as e_final:
                        print(f"  KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} ({e_final}). Sla over.")
                        continue 

        if not loaded_successfully or df_eyelink_raw is None:
            continue

        # Filteren op 'arrowLength' voor accuracy/precision data
        if 'arrowLength' not in df_eyelink_raw.columns:
            print(f"FOUT: Kolom 'arrowLength' niet gevonden voor {participant_id}. Sla over.")
            continue
        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)
        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith("UNDEFINED")].copy()

        if df_accprec.empty:
            print(f"Geen accuracy/precision data gevonden voor {participant_id}. Sla over.")
            continue
        
        # Check en converteer vereiste coördinaatkolommen
        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']
        missing_cols = [col for col in required_cols if col not in df_accprec.columns]
        if missing_cols:
            print(f"FOUT: Vereiste kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.")
            continue
        for col in required_cols:
            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')
        df_accprec.dropna(subset=required_cols, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na numerieke conversie voor {participant_id}. Sla over.")
            continue

        # Pixel naar CM conversie
        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']
        df_accprec.dropna(subset=cm_cols_to_check, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na cm-conversie voor {participant_id}. Sla over.")
            continue

        # Identificeer unieke target dots en wijs dot_id toe (0 tot N-1)
        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)
        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)
        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]
        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])
        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)
        unique_targets_cm['dot_id'] = unique_targets_cm.index 
        num_unique_dots_found = len(unique_targets_cm)
        print(f"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots_found}")
        if num_unique_dots_found == 0:
            print(f"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.")
            continue
        if num_unique_dots_found != 13:
             print(f"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities, gevonden {num_unique_dots_found}.")
        
        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], 
                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')
        
        # Bereken statistieken per dot
        current_participant_dots_summary_list = []
        for dot_id_val, group in df_accprec.groupby('dot_id'):
            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]
            if target_info.empty: continue
            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])
            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))
            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)
            current_participant_dots_summary_list.append(summary)
        
        summary_df_per_dot_current_pp = pd.DataFrame(current_participant_dots_summary_list)
        
        if not summary_df_per_dot_current_pp.empty and not summary_df_per_dot_current_pp[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].isnull().all().all():
            summary_df_per_dot_current_pp['participant_id'] = participant_id 
            all_participants_dot_summaries_list.append(summary_df_per_dot_current_pp)
            print(f"Statistieken per doelpunt berekend voor {participant_id}.")
        else:
            print(f"Kon geen (valide) statistieken per doelpunt berekenen voor {participant_id}.")

print("\nAlle participanten verwerkt.")

if not all_participants_dot_summaries_list:
    print("Geen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.")
else:
    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries_list, ignore_index=True)
    print(f"\nGecombineerde per-dot data van alle participanten. Totaal aantal observaties (dots x PPs): {len(final_dot_summaries_all_pps_df)}")

    # --- A. Gemiddelde Checkerboard Heatmaps (over alle participanten) ---
    # Titels en stijl identiek aan de webcam output_10_X.png voorbeelden
    print("\nGenereren van gemiddelde checkerboard heatmaps over alle participanten...")
    if not final_dot_summaries_all_pps_df.empty:
        final_dot_summaries_all_pps_df['dot_id'] = pd.to_numeric(final_dot_summaries_all_pps_df['dot_id'], errors='coerce')
        final_dot_summaries_all_pps_df.dropna(subset=['dot_id'], inplace=True)
        if not final_dot_summaries_all_pps_df.empty:
             final_dot_summaries_all_pps_df['dot_id'] = final_dot_summaries_all_pps_df['dot_id'].astype(int)
        
        # Bereken gemiddelde per dot_id over alle participanten
        averaged_dot_metrics = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()

        if not averaged_dot_metrics.empty:
            # Zorg voor een DataFrame met alle 13 dot_ids voor het plotten, vul ontbrekende met NaN
            plot_df_avg_checkerboard = pd.DataFrame({'dot_id': range(len(positions_to_fill_13_dots))})
            plot_df_avg_checkerboard = pd.merge(plot_df_avg_checkerboard, averaged_dot_metrics, on='dot_id', how='left')
            
            if len(averaged_dot_metrics) < len(positions_to_fill_13_dots):
                 print(f"  WAARSCHUWING: Gemiddelde data voor checkerboard heatmap beschikbaar voor {len(averaged_dot_metrics)} van de {len(positions_to_fill_13_dots)} verwachte unieke dot posities.")
            
            # Plot de drie checkerboard heatmaps
            plot_checkerboard_heatmap_from_markdown(plot_df_avg_checkerboard, measurement='median_offset_cm', title='Offset (cm) Across Positions', positions_to_fill=positions_to_fill_13_dots)
            plot_checkerboard_heatmap_from_markdown(plot_df_avg_checkerboard, measurement='eucl_dist_gaze_to_median_cm', title='SD (cm) Across Positions', positions_to_fill=positions_to_fill_13_dots)
            plot_checkerboard_heatmap_from_markdown(plot_df_avg_checkerboard, measurement='rms_error_cm', title='RMS error (cm) Across Positions', positions_to_fill=positions_to_fill_13_dots) # Let op: titel webcam was 'RMS error', niet 'RMS Error'
        else:
            print("Geen data om gemiddelde checkerboard heatmaps te plotten na groeperen.")
    else:
        print("Geen gecombineerde data beschikbaar voor checkerboard heatmaps.")

    # --- B. Overige Gecombineerde Plots (identiek aan webcam output_14_X en output_16_X) ---
    print("\nGenereren van gecombineerde beschrijvende statistieken en andere plots...")
    if not final_dot_summaries_all_pps_df.empty:
        # B.1. Tabel met Beschrijvende Statistieken (over alle dots, alle participanten)
        # Titel: "Descriptive Statistics for Offset, Precision (SD), and RMS Error"
        desc_stats_data_all = {
            'Metric': ['Offset (cm)', 'Precisie (SD, cm)', 'RMS Error (cm)'],
            'Mean': [
                final_dot_summaries_all_pps_df['median_offset_cm'].mean(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].mean(),
                final_dot_summaries_all_pps_df['rms_error_cm'].mean()
            ],
            'Std Dev': [
                final_dot_summaries_all_pps_df['median_offset_cm'].std(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].std(),
                final_dot_summaries_all_pps_df['rms_error_cm'].std()
            ],
            'Min': [
                final_dot_summaries_all_pps_df['median_offset_cm'].min(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].min(),
                final_dot_summaries_all_pps_df['rms_error_cm'].min()
            ],
            'Median': [
                final_dot_summaries_all_pps_df['median_offset_cm'].median(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].median(),
                final_dot_summaries_all_pps_df['rms_error_cm'].median()
            ],
            'Max': [
                final_dot_summaries_all_pps_df['median_offset_cm'].max(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].max(),
                final_dot_summaries_all_pps_df['rms_error_cm'].max()
            ]
        }
        overall_desc_stats_df = pd.DataFrame(desc_stats_data_all)
        print(f"\nDescriptive Statistics for Offset, Precision (SD), and RMS Error (EyeLink - All PPs, All Dots):")
        print(overall_desc_stats_df.to_string(index=False))

        # B.2. Staafdiagram van Gemiddelde Prestaties (overall)
        # Titel: "Average System Performance Metrics"
        plt.figure(figsize=(8, 6))
        barplot_data_overall = overall_desc_stats_df.set_index('Metric')
        # Zorg voor de juiste volgorde en labels voor x-as ticks
        metric_order = ['Offset (cm)', 'Precisie (SD, cm)', 'RMS Error (cm)']
        barplot_data_overall = barplot_data_overall.reindex(metric_order)
        sns.barplot(x=barplot_data_overall.index, y='Mean', data=barplot_data_overall, palette=['#1f77b4', '#ff7f0e', '#2ca02c'])
        plt.title('Average System Performance Metrics', fontsize=16)
        plt.ylabel('Value (cm)', fontsize=12)
        plt.xlabel('') # Geen expliciet x-as label, de metrieken zijn de labels
        plt.xticks(ticks=range(len(barplot_data_overall.index)), labels=barplot_data_overall.index, rotation=0, ha='center', fontsize=10)
        plt.tight_layout()
        plt.show()

        # B.3. Overall Boxplot van Distributies (Offset, SD, RMS)
        # Titels: "Distribution of [Metric] Values Across Calibration Points"
        metrics_for_overall_boxplot = {
            'median_offset_cm': ('Offset (cm)', 'skyblue', 'Distribution of Offset Values Across Calibration Points'),
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'lightcoral', 'Distribution of Precision (SD) Values Across Calibration Points'),
            'rms_error_cm': ('RMS Error (cm)', 'lightgreen', 'Distribution of RMS Error Values Across Calibration Points')
        }
        for metric_col, (ylabel_text, color, plot_title_text) in metrics_for_overall_boxplot.items():
            plt.figure(figsize=(6, 7))
            sns.boxplot(y=final_dot_summaries_all_pps_df[metric_col], color=color)
            plt.title(plot_title_text + ' (EyeLink - All PPs)', fontsize=14)
            plt.ylabel(ylabel_text, fontsize=12)
            # Het x-label van de webcam plots was "13 Calibration Points", 
            # hier tonen we het totaal aantal metingen voor de distributie.
            plt.xlabel(f'{len(positions_to_fill_13_dots)} Calibration Point Types\n(Data Pooled from All Participants & Dots)', fontsize=10)
            plt.xticks([]) 
            plt.tight_layout()
            plt.show()

        # B.4. Scatterplot van Offset vs. Precisie (SD) (overall)
        # Titel: "Accuracy (Offset) vs. Precision (SD) per Point"
        plt.figure(figsize=(8, 6))
        sns.scatterplot(x=final_dot_summaries_all_pps_df['median_offset_cm'], y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], alpha=0.5)
        plt.title('Accuracy (Offset) vs. Precision (SD) per Point (EyeLink - All PPs)', fontsize=16)
        plt.xlabel('Offset (cm)', fontsize=12)
        plt.ylabel('Precision (SD, cm)', fontsize=12)
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        # --- B.5. Nieuwe Plots: Metriek per dot across participants ---
        print("\nGenereren van plots: metriek per doelpunt over participanten...")
        metrics_to_plot_per_dot = {
            'median_offset_cm': 'Offset (cm)',
            'eucl_dist_gaze_to_median_cm': 'Precision (SD, cm)',
            'rms_error_cm': 'RMS Error (cm)'
        }
        
        actual_dot_ids_present = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())
        # We willen plotten voor dot_ids 0 tot max_expected_dot_id (0-12 voor 13 punten)
        # en alleen degenen die daadwerkelijk data hebben.
        expected_dot_ids_for_plot = [dot_id for dot_id in range(len(positions_to_fill_13_dots)) if dot_id in actual_dot_ids_present]
        plot_data_for_dots = final_dot_summaries_all_pps_df[final_dot_summaries_all_pps_df['dot_id'].isin(expected_dot_ids_for_plot)]

        if not plot_data_for_dots.empty:
            for metric_col, metric_label in metrics_to_plot_per_dot.items():
                plt.figure(figsize=(12, 7))
                # Sorteer op dot_id voor consistente volgorde van boxplots
                # En gebruik 'order' parameter om de x-as volgorde te specificeren
                sns.boxplot(x='dot_id', y=metric_col, data=plot_data_for_dots, color='lightgray', showfliers=False, order=expected_dot_ids_for_plot)
                sns.stripplot(x='dot_id', y=metric_col, data=plot_data_for_dots, jitter=True, alpha=0.7, size=5, order=expected_dot_ids_for_plot)
                plt.title(f'EyeLink (All PPs): {metric_label} per Dot Across Participants', fontsize=16)
                plt.xlabel(f'Dot ID (0-{len(expected_dot_ids_for_plot)-1}, corresponding to {len(expected_dot_ids_for_plot)} reference points)', fontsize=12)
                plt.ylabel(metric_label, fontsize=12)
                if expected_dot_ids_for_plot:
                     plt.xticks(ticks=range(len(expected_dot_ids_for_plot)), labels=expected_dot_ids_for_plot, rotation=0, ha='center', fontsize=10)
                plt.grid(axis='y', linestyle='--')
                plt.tight_layout()
                plt.show()
        else:
            print("Geen data beschikbaar voor 'per dot across participants' plots.")
    else:
        print("Geen gecombineerde data beschikbaar voor het genereren van plots.")
