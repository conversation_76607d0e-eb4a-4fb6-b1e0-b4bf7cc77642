{"cells": [{"cell_type": "markdown", "id": "3c9d6fde", "metadata": {}, "source": ["### Import libraries"]}, {"cell_type": "code", "execution_count": 30, "id": "5f62cbd8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib import cm\n", "import matplotlib.patches as patches\n", "import os\n", "import astropy.convolution as krn\n", "import scipy.stats as stats\n", "import seaborn as sns\n", "import statsmodels.api as sm\n", "from statsmodels.stats.anova import AnovaRM\n", "\n", "from deepeye_analysis_package.plotting import makeHeat"]}, {"cell_type": "code", "execution_count": 31, "id": "ee40faf0", "metadata": {}, "outputs": [], "source": ["def compute_rms(samples):\n", "    # Assuming `samples` is a list of tuples, each containing (x, y) coordinates\n", "    deltas = np.diff(samples, axis=0)\n", "    distances = np.linalg.norm(deltas, axis=1)\n", "    rms = np.sqrt(np.mean(distances**2))\n", "    return rms"]}, {"cell_type": "markdown", "id": "291816c6", "metadata": {}, "source": ["### Preprocess files and make a dataframe\n", "\n", "test_all file contains the results of a validation session. If calibration had to be repeated under the same participant name, then the results of the new validation are appended. Therefore, the test_all file may contain several validation datasets, each with each own header row. \n", "\n", "\n", "1. get_last_dataset()\n", "    Extract the last dataset from the test_all file based on the header row. Participants had 3 attempts to get calibration <3 cm. Keep only the successful calibration dataset\n", "2. scale_dot_postion()\n", "    Scale pixels to standard dimensions. Calibration dots were presented as % display size in px. Convert pixels to cm. Assign each dot a unique label\n", "\n"]}, {"cell_type": "markdown", "id": "78bf10fb", "metadata": {}, "source": ["### Specify the Experiment version and paths\n", "- *MullerLyer_lab* - experiment ran in the lab\n", "- *MullerLyer_online* - experiment ran online via Prolific "]}, {"cell_type": "code", "execution_count": null, "id": "f2d1e64e", "metadata": {}, "outputs": [], "source": ["# Path to data folders\n", "path_to_folders = './CollectedData/complete/data'\n", "\n", "\n", "# Get all folder names\n", "folder_names = os.listdir(path_to_folders)\n", "\n", "# List to accumulate datasets across subjects\n", "pp_list = []\n", "\n", "# Define target resolution, since datasets may have different screen resolutions\n", "TARG_RES = True\n", "if TARG_RES:\n", "    target_resX, target_resY = 1920, 1080\n", "else:\n", "    target_resX, target_resY = None, None\n", "\n", "for fn in folder_names:\n", "    path = os.path.join(path_to_folders, fn, f\"{fn}_accprec_record.csv\")\n", "\n", "    # read everything in as strings (so the header‐rows don't blow up type inference)\n", "    df = pd.read_csv(path, sep=',', dtype=str)\n", "\n", "    # drop any row where frameNr is literally the string \"frameNr\"\n", "    df = df[df['frameNr'] != 'frameNr']\n", "\n", "    # now you can coerce types if you like\n", "    df['frameNr'] = df['frameNr'].astype(int)\n", "    df['sampTime'] = df['sampTime'].astype(float)\n", "    df['accprecX'] = df['accprecX'].astype(float)\n", "    df['accprecY'] = df['accprecY'].astype(float)\n", "    df['user_pred_px_x'] = df['user_pred_px_x'].astype(float)\n", "    df['user_pred_px_y'] = df['user_pred_px_y'].astype(float)\n", "    df['resX'] = df['resX'].astype(float)\n", "    df['resY'] = df['resY'].astype(float)\n", "    df['scrW_cm'] = df['scrW_cm'].astype(float)        \n", "   \n", "    df = df.drop_duplicates(subset=['user_pred_px_x', 'user_pred_px_y'], ignore_index=True)  # Ensure unique coordinates (sometimes webcam is stuck)\n", "    \n", "    df['scale_cm_in_px'] = df['scrW_cm'] / df['resX']  # Scaling factor for converting px to cm\n", "\n", "    # Identify unique dot positions\n", "    _, indices = np.unique(df[['accprecX', 'accprecY']], axis=0, return_inverse=True)\n", "    df['unique_dot'] = indices\n", "\n", "    # Identify unique dot positions\n", "    unique_dots = df[['accprecX', 'accprecY']].drop_duplicates()\n", "\n", "    # Filter out participants if they have fewer unique dots than the number in 'numCalibDots'\n", "    required_dots = 13\n", "    if len(unique_dots) < required_dots:\n", "        print(f\"Skipping participant {fn} with fewer unique dots ({len(unique_dots)}) than required ({required_dots})\")\n", "        continue  # Skip this participant if they have fewer unique dots\n", "\n", "    # Append the cleaned dataset to the list\n", "    pp_list.append(df)\n", "\n", "# Concatenate all subject data into a single DataFrame\n", "all_df = pd.concat(pp_list, ignore_index=True)"]}, {"cell_type": "markdown", "id": "8b20472a", "metadata": {}, "source": ["## Plot heatmap across all subjects"]}, {"cell_type": "code", "execution_count": 33, "id": "e38beee1", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlsAAAGDCAYAAAAcQNWuAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOydd5gURfrHPzOb2QRLWHJcMogEQQxkQQ4VFU89A4remT0Vs+IBnhHjTz0xA2c4IyZQAUGCAiqCApKDkmFJy+6yefr3x24NNTXVPT2zMxv7+zz9dHd1dXV1d71V3zd0tcswDAMHDhw4cODAgQMHEYG7sivgwIEDBw4cOHBQk+GQLQcOHDhw4MCBgwjCIVsOHDhw4MCBAwcRhEO2HDhw4MCBAwcOIgiHbDlw4MCBAwcOHEQQDtly4MCBAwcOHDiIIByy5cCBAwcOHDhwEEE4ZMuBAwcOHDhw4CCCcMiWAwcOHDhw4MBBBBFd2RVw4KAicPXVVzNjxgy2b99O69atAfjjjz9o06YNV111FdOnT/fmHTRoEIsWLaKq/lxBdy9VAZ999hkXXHABP/zwA6eddlq5ytK9g4ULFzJ48GAmTpzIpEmTylnbyoNZuwsGchuu6di4cSNdu3blhRde4KabbgrqXI/HQ2FhYYRq5qC2IyYmhqioKFt5HbLloMrgl19+4eWXX2bx4sXs2bMHj8dD06ZNOe200xg7dixnnXVWZVexQjB9+nTGjRvHtGnTuPrqqyu7OrZQVFTEPffcw4gRIyyJ1jXXXMO0adNIS0tjz549xMXFRaxOkyZNYvLkyT5pderUoV27dlx44YXcfffdJCYmRuTatYkMWcHlcjFw4EAWLlwYchkdO3bkb3/7G5MnT+bKK68kOTnZ1nmFhYVs374dj8cT8rUdOAiEunXr0rhxY1wul2U+h2w5qHR4PB7uuusunnvuOaKjoxkyZAjnnXceMTExbNu2jdmzZ/POO+/w8MMP89BDD4Xtus2aNWP9+vWkpqaGrcyKwOOPP859991Hs2bNKrsqXrz99tts3ryZV155xTRPdnY2H374IS6Xi8OHD/PZZ59xySWXRLxuY8aMoVu3bgDs3buXL774gsmTJ/Pll1+ybNkyYmNjI14HGeFod/Pnzw9jjao+7rnnHt555x1eeOEFHnzwwYD5DcNg7969REVF0aJFC9xuJ2LGQXhhGAbHjx/nwIEDADRp0sQyv0O2HFQ6JkyYwHPPPcfJJ5/Mxx9/TLt27XyO5+Xl8dJLL3Ho0KGwXjcmJoZOnTqFtcyKQJMmTQIKdkVj6tSptGjRgsGDB5vm+eCDD8jNzWX8+PE8//zzvPnmmxVCti666CIuvfRS7/7TTz9N3759WblyJe+9916FWw/D0e5UGanp6N69OyeddBKvv/46999/f0DyVFxczPHjx2natCl16tSpoFo6qG1ISEgA4MCBAzRq1MjSpejQfQeVii1btjBlyhTq16/PN998ox1EEhISuPvuu31cQps2beKee+6hV69e1K9fn/j4eDp06MB9991HTk6OrWv/8ccfuFwu08E2Pz+f++67j5YtWxIfH0/nzp158cUX/WK5pk+fjsvlYvr06Xz55ZecfvrpJCcne11JhYWFvPjii4wYMYIWLVoQFxdHo0aNuPDCC1m1apVPWVdffTXjxo0DYNy4cbhcLu8i53G5XFoX1bRp0+jXrx9JSUkkJSXRr18/bVzQwoULcblcTJo0iRUrVnDWWWeRnJxMamoqF1xwQVDur7Vr17JixQrGjBljaUp/8803iY6O5p577mHw4MHMnz+fP//80/Z1woXk5GTvO//5558B+O6777jmmmvo2LGj99n16dOH1157TVuGy+Vi0KBB7N69m7Fjx9K4cWPcbre3Lfz555/8+eefPu9PxJlZtbvs7GwmT57MSSedRJ06dUhNTaVnz5489NBDFBUVefO1bt3aL15v0qRJuFwuFi5cyJtvvkn37t2Jj4+nWbNm3HHHHWRnZ/td76233mL06NG0bt2a+Ph40tLSGDFiBN99951f3mDajMgLsGjRIp/nINqjx+PhjTfeoG/fvqSlpZGQkEDz5s0599xztW7Hiy++mD///FNbNxUlJSUAFW61dFD7IMi8LJ86OJYtB5WK6dOnU1JSwvXXX096erplXjm+Z+bMmbz55psMHjyYQYMG4fF4WL58OU8++SSLFi1i8eLFxMTElKtuF198MatWrWLMmDEAfPLJJ/zzn//kjz/+4JlnnvHL/9FHHzF37lzOOeccbrrpJo4dOwbA4cOHuf322znzzDP5y1/+Qr169di2bRtffPEFX3/9NYsXL+aUU04B4Pzzz+fo0aN8/vnnjB49mpNPPtl2ff/5z3/y4osv0qxZM6699lpvnceNG8eqVav4v//7P79zfv75Z6ZMmcLgwYO5/vrrWbVqFZ999hlr1qxh7dq1xMfHB7yucGmdeuqppnnWrVvH8uXL+ctf/kJ6ejpjx45l/vz5TJs2rVKD3QUhePLJJ9myZQunnnoqF1xwAUePHuWbb77h+uuvZ+PGjdr3fejQIfr3709aWhqXXnop+fn5nHTSSUycOJHnn38egNtvv92bf9CgQZZ1OXDgAAMHDmTDhg2cfPLJ3HjjjXg8HjZs2MCTTz7JnXfeSd26dQPe07PPPsv8+fO55JJLGDVqFN9++y3PP/88y5cv95OLm2++mR49ejBs2DAaNmzI7t27+eyzzxg2bBgzZ85k9OjRfuXbaTOtW7dm4sSJTJ48mVatWvkQS9Gm77//fqZMmUK7du247LLLSE5OZvfu3Xz//fd8++23fs+rf//+QGl7Gzp0aMDnAASMo3HgoLyw3cYMBw4qEYMGDTIA49tvvw3qvF27dhkFBQV+6ZMnTzYA45133vFJv+qqqwzA2L59uzdt+/btBmBcddVVPnkHDhxoAEbHjh2No0ePetOPHj1qdOzY0XC5XMbPP//sTZ82bZoBGG6325g3b55fnfLz841du3b5pa9du9ZISkoyhg0b5pMuyps2bZr23nX3smjRIgMwOnfu7FPnw4cPGx06dDAAY/Hixd707777zgAMwHj//fd9yr/yyisNwPjf//6nvb6Kv/71rwZgbN682TTP+PHjfcrMzs42EhMTjZYtWxolJSV++cU7kCHqPHHiRFv1mjhxovY+srOzjS5duhiAMWPGDMMwDGPbtm1+5xcVFRlnnXWWERUVZfz5558+x8SzGzdunFFcXOx3bqtWrYxWrVpp62XW7saMGWMAxgMPPOB3zr59+4yioiLL8sX9xsbGGr/99ps33ePxGJdddpkBGE8//bTPObr73rNnj9G0aVOjffv2PumhtBnAGDhwoN81DMMw0tLSjKZNmxq5ubl+xw4dOuSXlpWVZQDGgAEDtOXJyMvLM9atW2fk5eUFzOvAQXlgt605bkQHlYp9+/YB0Lx586DOa9asmdZFcMsttwDw7bfflrtuDz30kE8Qc2pqKhMmTMAwDGbMmOGXf/To0QwbNswvPS4uThvM3rVrVwYPHszixYsDmqADQdRn0qRJPnWuV68eEydOBNC6EwcMGOAXN3XNNdcAJ1xsgbBr1y4AU8tkUVERb7/9NikpKZx//vkAJCUlccEFF7Bjx46wvCsrfPzxx0yaNIlJkyZx44030rFjR9atW0efPn28sVxt2rTxOy86OpobbriBkpISresqNjaWKVOm2P702wr79u1j5syZtGvXTmvpS09PJzraniNi7NixnHTSSd59l8vFY489RlRUlF8b0N13kyZNGDNmDJs3b9a6ecPRZgRiY2O1zy8tLc0vLSUlhfj4eG97c1C1MGnSpKAs8ZHEoEGDfKzKVQEO2XJQLWEYBm+99RYDBgwgLS2NqKgoXC4X9evXB2DPnj3lvsaZZ55pmqbGWgH07dvXtKxff/2Vyy67jJYtWxIbG+uNX/nyyy8pLCzk4MGD5aqrqI/OVSWC1n/99Ve/Y7179/ZLE8T36NGjtq596NAhoqKiTD/J//zzz8nMzOSvf/2rj1ty7NixQGksVyTxySefMHnyZCZPnsx///tf6tevz6RJk1i4cKGXsGdnZzNx4kR69OhBUlKS9/0IF7KuPbVp04YGDRqEpY4rVqzAMAwGDx5cbve3rt22atWKFi1a8Pvvv/vMO7Vt2zb+8Y9/0K5dO+Lj4733/eKLLwL6+w5HmwG49NJL+eOPP+jWrRsPPfQQCxYsIC8vz/KctLS0cstKVce+ffu47bbbyMjIID4+nvT0dE4//XSmTp3K8ePHK7t6IUHEE1otoUDEBgbT7ioLTsyWg0pF48aN2bBhA7t376Zjx462z/vnP//JSy+9RIsWLTjvvPNo0qSJN6Zr8uTJFBQUlLtuOkuNSMvKyrKVH2Dp0qUMGTIEgOHDh9O+fXvvgP7ZZ5/x22+/lbu+x44dw+1207BhQ229XC6XN4ZMRkpKil+asKCIIONASEhIoKSkhKKiIi1REGRKkCuBoUOH0qxZMz7//HMOHz6stWaEA//73/98vkZUUVhYyKBBg1i5ciU9e/bkyiuvpH79+kRHR/PHH38wY8YM7fsJFGMYDER7Csd0Hmb1Sk9P548//iA7O5v69euzZcsW+vbty7Fjxxg8eDDnnnsuKSkpuN1uFi5cyKJFi7T3HY42A/B///d/tGnThmnTpvHII4/wyCOPEB8fz8UXX8wzzzyjJbJ5eXkV+nXhkbwjrDmwhpzCHJJik+jeqDv1EupF7Hrbtm3j9NNPp27dujz22GN0796duLg41qxZw2uvvUazZs0477zztOeayV9VwF133cUNN9zg3T/llFO47rrr+Mc//qHNX1hYWOM+bnDIloNKxemnn87ChQuZP3++l5AEwoEDB/jPf/7DSSedxLJly3w633379vlNZBkq9u/fT8uWLf3SAO0cSWba2aOPPkpBQQFLlizhjDPO8Dm2fPlyfvvtt3LXNSUlBY/HQ2ZmJo0aNfI5duDAAQzD0A6S4YAgeIcPH/Yb6Hfu3MncuXMBGDhwoGkZ77zzDv/85z8jUr9A+Pzzz1m5ciXXXnstb7zxhs+x999/X+syhvAGX4vA9927d5e7LNFGdekul8trgXzuuec4cuQIb7/9NldccYVP3htuuIFFixaVuy5WiI6O5q677uKuu+5iz549LFq0iGnTpvHf//6Xffv2MWfOHJ/8Ho+HrKwsunbtGtF6AZR4Spi9aTZfb/mazOOZuHBhYNCwTkNGZoxkVIdRRLnL7z5WcdNNNxEdHc2KFSt8Jtxt27Yto0eP9vkS2uVy8fLLL/P1118zf/587r77biZNmsTUqVN5+umn2blzJ23atGHChAlceeWVwIm/F6xatcrr8jt69Cj16tXju+++Y9CgQd4/NXz77bfce++9rFu3jpNPPplp06b5KMRPPPEEzz33HMePH+fiiy/WKnoC4gtfAWEJb9y4MVBqke/WrRvR0dG88847dO/enWnTplnWtXXr1l6rfb16pQRY/iuDx+Phnnvu4Y033iA2NpYbbrihUj/GcdyIDioVV199NVFRUbz22mtkZmZa5hVa9rZt2zAMg2HDhvlpuUuWLAlb3XRlibSePXvaLmfr1q2kpaX5Ea3jx4+zcuVKv/wihiUYK4Goj+6TeZEWqXiK7t27A6W/VVExffp0PB4PZ5xxBtdee63fctVVVwGRdyVaYevWrQDaL+9CbU9RUVFBvb8+ffrgdrv57rvvyh2/p6vzn3/+yc6dO+natavXYmB234Zh8MMPP5SrDgJut9vWc2jatCl/+9vf+Oabb8jIyODbb7/1cylu3rwZj8fjbW+RxOxNs3l3zbsUeYro3KAzXRt1pXODzhR5inh3zbvM3jQ77Nc8dOgQc+fO5eabbzb9s4FK8CdNmsQFF1zAmjVruOaaa/j000+57bbbuPPOO1m7di3XX38948aNszVdhooHH3yQZ555hhUrVhAdHe2NywP48MMPmTRpEo899hgrVqygSZMmvPzyy0FfQ8aMGTOIjY3lhx9+sJwcWaBFixZ88sknQGnfs3fvXp8vrmfMmEFiYiI//vgjU6ZM4eGHH2bevHnlqmN54JAtB5WKjIwM7rnnHg4ePMjIkSPZvn27X578/HyeffZZr1bSqlUroNQ9J/+KY9euXdx///1hq9u///1vH3dhVlYWjzzyCC6Xy0sS7KBVq1YcOXKE33//3ZtWUlLCXXfdpSWYwp22c+dO29cQ9Zk8ebKPuzArK8tr6QumzsFAWKx+/PFHn3TDMJg2bRoul4sZM2bwxhtv+C3Tp0+nf//+rF69mhUrVkSkfoEg2tP333/vk75o0SJef/31kMoUsUX5+fm28qenpzNmzBi2bt2qtcweOHCA4uJiW2X997//ZfXq1d59wzB44IEHKCkp8ZmCwey+n3jiCdauXWvrWoGQlpamDWgvKChg6dKlfum5ubnk5OQQExPjN3GpaF9WFtJw4EjeEb7e8jXJcck0T2nutWBFuaNontKc5Lhkvt7yNUfyjoT1ulu2bMEwDL9wigYNGngtQ/fee6/Pscsuu4xx48bRtm1bWrZsydNPP83VV1/NTTfdRIcOHRg/fjwXXnghTz/9dND1efTRRxk4cCBdunThvvvuY+nSpd72/Pzzz3sVpo4dO/LII4/QpUuX0G8eaN++PVOmTKFjx462QkqioqK8fWWjRo1o3Lixj8dBTMPSvn17xo4dS58+fSr1zwuOG9FBpeORRx4hPz+f5557jo4dOzJkyBC6detGTEwM27dv59tvv+XQoUM88sgjwImvpT755BP69OnD0KFD2b9/P7NmzWLo0KFejb286NChA926dfOZZ2vXrl2MHz+ePn362C7n1ltvZe7cuZxxxhlcfPHFxMfHs3DhQnbv3u0128vo378/CQkJPP/88xw5csRrnp8wYYLpNQYMGMCtt97Kiy++6K2zYRjeOv/zn/9kwIABwT8EGxg6dCjJycnMmzePu+++25u+YMECtm/fzsCBA2nbtq3p+ePGjWPZsmW8+eabQT3XcOHcc8+ldevWTJkyhbVr19KtWzc2btzIrFmzuOCCC/j444+DLnPIkCGsWLGCkSNHcuaZZxIbG8uAAQMs38HLL7/M2rVrefTRR/nqq68YMmQIhmGwadMm5s6dy/79+23NszVixAj69+/PpZdeSsOGDZk/fz4rVqzg1FNP5dZbb/Xmu+GGG5g2bRpjxozh4osvpn79+ixfvpyVK1cyatQoZs8uv/VmyJAhfPjhh5x//vn07NmTqKgozjvvPFq2bMnpp59Ohw4d6N27Ny1btiQnJ4dZs2axb98+7rrrLr//Zs6bN4/o6GjOOeecctfLCmsOrCHzeCadG3TWHm+S1IT1B9ez5sAaBrSKjEzJ+Omnn/B4PFx++eV+MXSqvKxfv57rrrvOJ+3000/XzrEXCPIXreKPFQcOHKBly5asX7/eJwYLSvutUCxoAroPL8oDuf5Qeg/i1zqVAYdsOah0uN1unn32WS677DKmTp3K4sWLWbx4MR6PhyZNmjBixAjGjRvnM63C9OnTad26NZ988gkvvvgiLVu2ZPz48dx7770hDY46fPjhh0ycOJH//e9/7N+/nzZt2vDCCy94p5ewi3POOYePP/6Yxx57jHfeeYc6deowZMgQPv30Ux5++GG//Glpad7pCl5//XWvO8WKbAG88MIL9OzZk6lTp3pnPu/atSsPP/ywd1b6SCApKYkrrriC1157jb1793o7ZuEaDPQ7nEsuuYTbbruN//3vfzz77LPeX2BUFJKSkliwYAF33303ixcvZuHChXTt2pV3332X9PT0kNrTQw89xJEjR5g1axZLliyhpKSEiRMnWpKtBg0asHz5cp5++mk++ugjXnrpJeLj42nTpg333Xef7Z9mjx8/nvPOO4/nn3+eLVu2kJaWxm233ca///1vn6Djnj17MnfuXCZMmMDMmTOJioritNNO44cffuCLL74IC9kSg/yCBQv48ssv8Xg8NG/enM6dO/Pkk08yf/58lixZwoEDB6hXrx4dO3bk8ccf9/ug4fjx43z22Wecc845NG3atNz1skJOYQ4uXKYxWSI9p9DenyrsIiMjA5fL5eeOF4qKTi6C/ZG6sBbKsV9mbms52F64LyP5U2/1XoKpqw7qxwIul6tyf0oesZm+HDhwUGuwYcMGIzo62njkkUcquyq1FmJS0++++66yqxJ2vP766wZgLFq0yFb+8kxquuiPRcaYD8YYE+ZPMCZ+N9FvmTB/gjHmgzHGoj/s1SUYDB8+3GjWrJmRk5Pjd2zgwIHGbbfd5t0HjE8//dQnz2mnnWb84x//8En761//aowaNcowDMM4fvy4ARizZ8/2Hp87d65PuxGT1x45csSbZ9WqVT4TKffv39+46aabfK5z6qmnGj169LB1n61atTKee+4503uzW9cffvjBAIyDBw/6nKsrb/To0X4TCYcDzqSmDhw4qDB07NiRv//97zz33HPaf/A5cBAqiouLeeyxxzjvvPMi5gqX0b1RdxrWacjenL3a43tz9tKwTkO6Nwp/oP7LL79McXExffr04YMPPmD9+vVs3LiRd955hw0bNgScQPfuu+9m+vTpTJ06lc2bN/Pss88yc+ZM7rrrLqDUOnbqqafyxBNPsH79ehYtWhTQYq7DbbfdxltvvcW0adPYtGkTEydO9IlJDQfs1LVVq1a4XC5mzZpFZmam7f/iVgYcsuXAgYOwYPLkydxyyy1B/cTagYNA2LFjB2PHjuXZZ5+tkOvVS6jHyIyRZBdks+vYLko8pV9TlnhK2HVsF9kF2YzMGBmR+bbatWvHqlWrGDZsGPfffz89evSgT58+vPjii9x11138+9//tjz//PPP5//+7/94+umn6dq1K6+++irTpk3zmez4rbfeori4mN69e3P77bd7Y2GDwSWXXMJDDz3EPffcQ+/evfnzzz+58cYbgy4nEALVtVmzZkyePJn77ruP9PT0oEM8KhIuw5Acog4cOHDgoFpi0qRJTJ482TtfUm1Gfn4+27dvp02bNrZ+pq5CnWdLINLzbDmofrDb1hyy5cCBAwcOahTKS7YEKnoGeQfVD3bbmvM1ogMHDhw4cKBBvYR6FTK9g4OaDydmy4EDBw4cOHDgIIJwyJYDBw4qHNOnT8flchEfH6/9H6D4V1p58cEHH3DFFVfQvn17XC5XrY9lcuDAQeXAIVsOHDioNBQUFPDEE09ErPypU6fy+eef06JFC+/Pah04cOCgouGQLQcOHFQaTj75ZF5//XX27NkTkfLffvttsrKyWLBgQcRnHnfgwIEDMzhky4EDB5UG8YPkSFm3WrRo4fdDYwcOHDioaDhfIzpw4KDS0KZNG8aOHcvrr7/OfffdZ2p9ysrKsvVftPj4eJKSksJdTQcOHDgoFxyVz4EDB5WKBx98kOLiYp588knTPKNHj6Zhw4YBl6o8g7QDBw5qLxzLlgMHDioVbdu25corr+S1117jvvvuo0mTJn55nnnmGY4cORKwLCcuy4EDe7j66qs5evQon332GVD6BfDJJ5/M888/X6H1WLhwIYMHD+bIkSPUrVu3Qq9dkXDIlgMHDiodEyZM4O233+aJJ57g//7v//yO9+7duxJq5cBBxeLqq69mxowZAMTExNCyZUvGjh3LAw88QHR0ZIfrmTNnEhMTYytvZRCkVatW8cQTT7B48WIOHz5M48aN6d69O9dffz3nnHMOLperQuoRKhyy5cCBg0pH27ZtueKKK7zWLRWHDx+msLAwYDkJCQmkpqZGoooOaiGOHIE1ayAnB5KSoHt3iPQMImeffTbTpk2joKCAr776iptvvpmYmBjuv/9+v7yFhYXExsaG5bppaWlhKScS+Pzzz7n44osZNmwYM2bMICMjg4KCApYuXcqECRM488wzq7xVzInZcuDAQZXAhAkTTGO3LrzwQpo0aRJwue222yqh5g5qGkpK4Isv4IEH4IUXYNq00vUDD5Sml5RE7tpxcXE0btyYVq1aceONNzJs2DC++OILoNTydf755/Poo4/StGlTOnbsCMDOnTu5+OKLqVu3LmlpaYwePZo//vhDup8Sxo8fT926dalfvz733HMP6m+RBw0axO233+7dLygo4N5776VFixbExcWRkZHBm2++yR9//MHgwYMBqFevHi6Xi6uvvhoAj8fD448/Tps2bUhISKBHjx58/PHHPtf56quv6NChAwkJCQwePNinnjrk5uZy7bXXMmrUKGbPns3w4cNp27YtnTt35tprr+W3337zKlglJSVce+213ut37NjRz1Lucrn8ltatW3uPr127lpEjR5KUlER6ejpXXnklBw8etKyjHTiWLQcOHFQJtGvXjiuuuIJXX32VVq1a+bhNnJgtBxWJ2bPh3XchORk6d4aoqFKCtXdvaTrAeedVTF0SEhI4dOiQd3/+/PmkpKQwb948AIqKihgxYgT9+/dnyZIlREdH88gjj3D22WezevVqYmNjeeaZZ5g+fTpvvfUWnTt35plnnuHTTz9lyJAhptcdO3Ysy5Yt44UXXqBHjx5s376dgwcP0qJFCz755BPGjBnDxo0bSUlJISEhAYDHH3+cd955h1deeYX27duzePFirrjiCho2bMjAgQPZuXMnF154ITfffDPXXXcdK1as4M4777S8/7lz53Lo0CHuuece0zzChejxeGjevDkfffQR9evXZ+nSpVx33XU0adKEiy++GIC9e/d6z8vNzeXss8+mf//+ABw9epQhQ4bw97//neeee468vDzuvfdeLr74YhYsWGBZz0BwyJYDBw6qDB588EHefvttNm7cSNeuXb3pocZsLV68mMWLFwOQmZlJbm4ujzzyCAADBgxgwADnJ8MOfHHkCHz9dSnRat78RHpUVOn+rl2lx888M7IuRcMwmD9/PnPmzOHWW2/1picmJvLGG2943YfvvPMOHo+HN954w0s6pk2bRt26dVm4cCHDhw/n+eef5/777+fCCy8E4JVXXmHOnDmm1960aRMffvgh8+bNY9iwYUCpq19AuBwbNWrkdd8VFBTw2GOP8e2333rJS9u2bfn+++959dVXGThwIFOnTqVdu3Y888wzAHTs2JE1a9ZYfom8adMmb16Bn3/+2WtdA3j//fc555xziImJYfLkyd70Nm3asGzZMj788EMv2WrcuLH3+Y4ZM4bU1FReffVVAF566SV69uzJY4895i3jrbfeokWLFmzatIkOHTqY1jMQHLLlwIGDKoOMjAyuuOIKb5BwebFgwQKfzhfgoYceAmDixIkO2XLghzVrIDOz1KKlQ5MmsH59ab5INJ9Zs2aRlJREUVERHo+Hyy67jEmTJnmPd+/e3SdO67fffmPLli0kJyf7lJOfn8/WrVvJyspi79699OvXz3ssOjqaPn36+LkSBX799VeioqIYOHCg7Xpv2bKF48ePc9ZZZ/mkFxYW0rNnTwDWr1/vUw/AS8yCwUknncSvv/4KQPv27SkuLvYe+89//sNbb73Fjh07yMvLo7CwkJNPPtmvjAceeIBly5axYsUKr2Xut99+47vvvtPO1bd161aHbDlw4KB64eqrr/bGeaiYPn0606dPD8t1Jk2a5DNQOXAQCDk54HKVWrJ0EOk5OZG5/uDBg5k6dSqxsbE0bdrU7yvExMREn/2cnBx69+7Nu8K/KaFhw4Yh1UGQj2CQU/ZAZs+eTbNmzXyOxcXFhVQPKCVTABs3buTUU0/1lpeRkeGX9/333+euu+7imWeeoX///iQnJ/PUU0/x448/+uR75513eO6551i4cKFPXXNycjj33HO1ljbdlDTBwCFbDhw4cODAQRmSksAwSmO0dIRLBMdH6kcFiYmJWiJhhl69evHBBx/QqFEjUlJStHmaNGnCjz/+6LXkFhcX88svv9CrVy9t/u7du+PxeFi0aJHXjShDWNZKpC8FunTpQlxcHDt27DC1iHXu3Nkb7C+wfPlyy/sbPnw4aWlpPPnkk3z66aeWeX/44QdOO+00brrpJm/a1q1bffIsW7aMv//977z66qte8ibQq1cvPvnkE1q3bh32qTacrxEdOHDgwIGDMnTvDg0blgbD67B3b+nx7t0rtl5muPzyy2nQoAGjR49myZIlbN++nYULF/LPf/6TXbt2AXDbbbfxxBNP8Nlnn7FhwwZuuukmjh49alpm69atueqqq7jmmmv47LPPvGV++OGHALRq1QqXy8WsWbPIzMwkJyeH5ORk7rrrLu644w5mzJjB1q1bWblyJS+++KI3LOCGG25g8+bN3H333WzcuJH33nsvoBU7KSmJN954g9mzZzNq1CjmzJnDtm3bWL16NVOmTAEgqowVt2/fnhUrVjBnzhw2bdrEQw89xM8//+wta9++fVxwwQVceumljBgxgn379rFv3z4yMzMBuPnmmzl8+DB/+9vf+Pnnn9m6dStz5sxh3LhxPsQyFDhky4EDBw4cOChDvXowciRkZ5cGw4sxtqSkdD87u/R4pOfbsos6deqwePFiWrZsyYUXXuidEiE/P99r6brzzju58sorueqqq7zutQsuuMCy3KlTp3LRRRdx00030alTJ/7xj3+Qm5sLQLNmzZg8eTL33Xcf6enp3t9k/fvf/+ahhx7i8ccfp3Pnzpx99tnMnj2bNm3aANCyZUs++eQTPvvsM3r06MErr7ziE4xuhgsuuIClS5dSp04dxo4dS8eOHRkyZAgLFizwBscDXH/99Vx44YVccskl9OvXj0OHDvlYuTZs2MD+/fuZMWOGz5Qxp5xyClD6NfMPP/xASUkJw4cPp3v37tx+++3UrVu33D+0dxlmEXIOHDhw4MBBNUR+fj7bt2+nTZs2xMfHB31+SUnp9A9ff10aLC/QsGEp0Ro1yjymy0Htgt225sRsOXDgwIEDBxKiokrn0TrzzIqfQd5BzYRDthw4cODAgQMN6tWLzPQODmofnJgtBw7CjP3793PRRRdRv359XC4Xzz//PACbN29m+PDhpKam4nK5+Oyzzyq1nrUZ06dPr/I/rnXgwEHNgUO2HDgIgEOHDnH33XfTsWNH4uPjSUtLY8SIEcyaNUub/4477mDOnDncf//9vP3225x99tkAXHXVVaxZs4ZHH32Ut99+mz59+oS1nkuXLmXSpEmWXxnZwdy5c7n22mvp1q0bUVFRPv8Ns4svvviCXr16ER8fT8uWLZk4caLPxIPVCTk5OUycOJFu3bqRmJhI/fr1Ofnkk7ntttvYs2ePN9+kSZN8/rdWp04dWrZsybnnnuv9sXB5MHPmTC655BLatm1LnTp16NixI3feeWdQ7/ull16ic+fOxMXF0axZM8aPH+8NenbgwEHk4LgRHTiwwMaNGxk6dCiZmZmMGzeOPn36cPToUd59913OPfdc7rrrLp566imfcxYsWMDo0aO56667vGl5eXksW7aMBx980PvlTrixdOlSJk+ezNVXX+39hUYoeO+99/jggw/o1atXSP8a/Prrrzn//PMZNGgQL774ImvWrOGRRx7hwIEDTJ06NeR6VQaKiooYMGAAGzZs4KqrruLWW28lJyeH33//nffee48LLrjA7xlNnTqVpKQkCgoK2L17N3PmzOGaa67h+eefZ9asWbRo0SKkulx33XU0bdqUK664gpYtW7JmzRpeeuklvvrqK1auXBlwIsp7772XKVOmcNFFF3Hbbbexbt06XnzxRX7//XfLX7c4cOAgDDAcOHCgRWFhodGtWzejTp06xvLly32OFRcXG5dccokBGO+//77PMZfLZdx8880+aX/++acBGE899VTE6vvUU08ZgLF9+/ZylbN7926jsLDQMAzDGDVqlNGqVaugzu/SpYvRo0cPo6ioyJv24IMPGi6Xy1i/fn256hYuTJs2zbDT/X344YcGYLz77rt+x/Ly8oysrCzv/sSJEw3AyMzM9Mv7zjvvGG632+jXr1/Idf7uu+/80mbMmGEAxuuvv2557p49e4zo6Gjjyiuv9El/8cUXDcD44osvQq5XVUReXp6xbt064/jx45VdFQc1HMePHzfWrVtn5OXlWeZz3IgOHJjgk08+Ye3atdx3331+//OKiori1VdfpW7dut7fwYg4IMMw+M9//uN1J02aNIlWrVoBcPfdd+NyubyuuezsbG6//XZat25NXFwcjRo14qyzzmLlypU+1/vxxx85++yzSU1NpU6dOgwcOJAffvjBe3zSpEncfffdQOnPV8W1//jjDwAOHjzIhg0bOH78eMD7btq0KTExMaE8MtatW8e6deu47rrrfGZgvummmzAMg48//tgn/4YNG7j44otp2LAhCQkJdOzYkQcffNDnvlwuF5s2beKKK64gNTWVhg0b8tBDD2EYBjt37mT06NGkpKTQuHFj7w9uwwUx+/Tpp5/udyw+Pt50xm4Vl19+OX//+9/58ccfmTdvnjf9+PHjbNiwgYMHDwYsY9CgQX5pYq6k9evXW567bNkyiouLufTSS33Sxf7777/vk3706FHuuOMOb7ts3rw5Y8eO9dZz4cKFuFwuPvzwQyZPnkyzZs1ITk7moosuIisri4KCAm6//XYaNWpEUlIS48aNK7cbNRiISS4LCwsr7JoOaidEnxqoz3TciA4cmODLL78EYOzYsdrjqampjB49mhkzZrBlyxYGDBjA22+/zZVXXslZZ53lPe+kk06ibt263HHHHfztb3/jL3/5i/dHpzfccAMff/wxt9xyC126dOHQoUN8//33rF+/3vsrjQULFjBy5Eh69+7NxIkTcbvdTJs2jSFDhrBkyRL69u3LhRdeyKZNm/jf//7Hc889R4MGDYAT/0Z76aWXmDx5Mt9995120A4XVq1aBeAXj9a0aVOaN2/uPQ6wevVqzjzzTGJiYrjuuuto3bo1W7du5csvv+TRRx/1Of+SSy6hc+fOPPHEE8yePZtHHnmEtLQ0Xn31VYYMGcKTTz7Ju+++y1133cUpp5wSth9MC5L83//+lwkTJpQrqP7KK6/ktddeY+7cud6f9f70008MHjyYiRMnhvQPx3379gF437cZBNFRXY116tQB4JdffvGm5eTkcOaZZ7J+/XquueYaevXqxcGDB/niiy/YtWuXz7Uef/xxEhISuO+++9iyZQsvvvgiMTExuN1ujhw5wqRJk1i+fDnTp0+nTZs2/Otf/wr6HkNBdHQ0derUITMz01sfBw7CCcMwOH78OAcOHKBu3bpegm8Gh2w5cGCCdevWkZqa6h1wdejRowdQalk499xzadu2LVdeeSUdOnTgiiuu8OZLSUnhjjvuoFevXj7ps2fP5h//+IePReaee+7xbhuGwQ033MDgwYP5+uuvvYP99ddfT9euXZkwYQJz587lpJNOolevXvzvf//j/PPPDymoPRzYW/aPE91PW5s0aeITUH7rrbdiGAYrV66kZcuW3vQnnnjC79y+ffvy6quvAniJ2Z133snjjz/OvffeC8Df/vY3mjZtyltvvRU2snX++efTsWNH/vWvf/Hmm28yePBgzjzzTM455xwaNWoUVFndunUD/P/VVh48+eSTREVFcdFFF1nm69ixI1D677jBgwd705csWQLA7t27vWlPPfUUa9euZebMmT6zjE+YMAFDmQO7uLiYRYsWebX6zMxM3n//fc4++2y++uoroNSquWXLFt56660KI1sul4smTZqwfft2/vzzzwq5poPaibp169K4ceOA+Ryy5cCBCbKzs0lOTrbMI44fO3YspGvUrVuXH3/8kT179miD0X/99Vc2b97MhAkTOHTokM+xoUOH8vbbb+PxeAJq7pMmTQrJchIs8vLyAIiLi/M7Fh8f731OmZmZLF68mNtuu82HaAFa69Hf//5373ZUVBR9+vRh165dXHvttd70unXr0rFjR7Zt2xaWe4FSS9CPP/7Io48+yocffsj06dOZPn06brebm266iaefflp7rzoIa2Z2drY3bdCgQX4Exi7ee+893nzzTe655x7at29vmbdXr17069ePJ598kmbNmjF48GDWr1/PjTfeSExMjPe9Qan7vEePHtrfuajvZuzYsT7uk379+vG///2Pa665xidfv379eOGFFyguLg77D37NEBsbS/v27R1XooOIISYmJqBFS8AhWw4cmCA5OTlgLI0YOAORMjNMmTKFq666ihYtWtC7d2/+8pe/MHbsWNq2bQuUzs0FpdNGmCErK4t6VWRaa+Gm0sXn5Ofne48LQiSsPYGgErLU1FTi4+P93Gepqal+pLS8SE1NZcqUKUyZMoU///yT+fPn8/TTT/PSSy+RmprKI488YqucnJwcIPS2ImPJkiVce+21jBgxws/laoZPPvmESy65xEuEoqKiGD9+PIsWLWLjxo3efFu3bmXMmDG2ytS9F8Dvi8vU1FQ8Hg9ZWVnUr1/fVtnhgNvtDul3PQ4chBsO2XLgwASdO3fm119/ZceOHX6DisDq1asB6NKlS0jXuPjiiznzzDP59NNPmTt3Lk899RRPPvkkM2fOZOTIkXg8HqDUtXPyySdryxAWk6oA4T7cu3ev34C7d+9e+vbtG1K5Ou3RTKMM1VJkB61ateKaa67hggsuoG3btrz77ru2ydbatWsByMjIKFcdfvvtN8477zy6devGxx9/bNtS1KxZM77//ns2b97Mvn37aN++PY0bN6Zp06Z06NAhpLqYvYPKeDcOHFRlOFGDDhyYQPxJ/r///a/2+LFjx/j888/p1KlTuQbQJk2acNNNN/HZZ5+xfft26tev77VWtGvXDiiN+Ro2bJh2EW6cqjAjuiCEK1as8Enfs2cPu3bt8h4XljtBQKob6tWrR7t27bwxanbw9ttvAzBixIiQr7t161bOPvtsGjVqxFdffRUS0W7fvj1nnnkmjRs3Zt26dezdu5dhw4Z5j7dr167avhcHDqoqHLLlwIEJLrroIrp06cITTzzhRx48Hg833ngjR44cYeLEiSGVX1JSQlZWlk9ao0aNaNq0qdcN17t3b9q1a8fTTz/tdUPJyMzM9G4nJiYCaGcUD2bqB7soKipiw4YNPoSja9eudOrUiddee42SkhJv+tSpU3G5XN5A7oYNGzJgwADeeustduzY4VNuVbJ+/Pbbb1pX8p9//sm6deu8geeB8N577/HGG2/Qv39/hg4d6k0PZuqHffv2MXz4cNxuN3PmzPF+aarD1q1bAwbiezwe7rnnHurUqcMNN9zgTR8zZgy//fYbn376qd85VendOHBQneC4ER04MEFsbCwff/wxQ4cO5YwzzvCZQf69995j5cqV3HnnnX5zF9lFdnY2zZs356KLLqJHjx4kJSXx7bff8vPPP3u/TnS73bzxxhuMHDmSrl27Mm7cOJo1a8bu3bv57rvvSElJ8U5R0bt3bwAefPBBLr30UmJiYjj33HNJTEwMauqH1atX88UXXwCwZcsWsrKyvK6yHj16cO655wKlX7B17tyZq666iunTp3vPf+qppzjvvPMYPnw4l156KWvXruWll17i73//O507d/bme+GFFzjjjDPo1asX1113HW3atOGPP/5g9uzZ/PrrryE903Bj3rx5TJw4kfPOO49TTz2VpKQktm3bxltvvUVBQYH2o4OPP/6YpKQkCgsLvTPI//DDD/To0YOPPvrIJ28wUz+cffbZbNu2jXvuuYfvv/+e77//3nssPT3dO50E4CV0Yp41gNtuu438/HxOPvlkioqKeO+99/jpp5+YMWOGj5v87rvv5uOPP+avf/0r11xzDb179+bw4cN88cUXvPLKK94vcB04cGAfDtly4MACnTt35rfffuOJJ57giy++YNq0aSQkJNCnTx+++OILL/EIBXXq1OGmm25i7ty5zJw5E4/HQ0ZGBi+//DI33nijN9+gQYNYtmwZ//73v3nppZfIycmhcePG9OvXj+uvv96b75RTTuHf//43r7zyCt988w0ej4ft27d7LV52sXLlSh566CGfNLF/1VVXBbznc845h5kzZzJ58mRuvfVWGjZsyAMPPOD32X+PHj1Yvnw5Dz30EFOnTiU/P59WrVpx8cUXB1XfSGLMmDFkZ2czd+5cFixYwOHDh6lXrx59+/blzjvv9JlGQUC8OxHAf/LJJ/PWW29x2WWX2f5yUYfffvsNKP2oQsXAgQN9yJYOPXv25Pnnn+fdd9/F7XbTt29f5s+f73cPSUlJLFmyhIkTJ/Lpp58yY8YMGjVqxNChQ2nevHnI9XfgoDbDZTh2YQcOHNQyTJ8+nXHjxjluMQcOHFQInJgtBw4cOHDgwIGDCMIhWw4cOHDgwIEDBxGEQ7YcOHDgwIEDBw4iiCpNtv7zn//QunVr4uPj6devHz/99FNlV8mBAwc1AFdffbUTr+XAgYMKQ5UlWx988AHjx49n4sSJrFy5kh49ejBixAgOHDhQ2VVz4MCBAwcOHDiwjSr7NWK/fv045ZRTeOmll4DSCfhatGjBrbfeyn333VfJtXPgwIEDBw4cOLCHKjnPVmFhIb/88gv333+/N83tdjNs2DCWLVumPaegoMDn57cej4fDhw9Tv379KvEbEwcOHDhw4MBBYBiGQXZ2Nk2bNsXtrrIOuKBQJcnWwYMHKSkpIT093Sc9PT2dDRs2aM95/PHHmTx5ckVUz4EDBw4cOHAQYezcubPGTKRbJclWKLj//vsZP368dz8rK4uWLVtyzx13BD1rs5UdTD3mVtJdSpq6HyWlRylrt7KWzxVpcj1k/68BeMq2PWX7hrTt0SyGsq07F+mYnEdGlfRDO6gwmMmLTh9V85rJU6gI1DbVfTW/WT4HDkKB1VhiRz4ClWEHZm1Zlx5ueQj1vIKCAqY89xzJyckhllD1UCXJVoMGDYiKimL//v0+6fv376dx48bac+Li4rSkKi4ujvj4eFvXDdSo7RAtkS6TJTNypVu7pXJVEmZWR5kMCbIkfgFcIu2LtZqmbjuEy4Ed2CVZVgQrUF6zdDsDiFUbdUiXg0iivCQrXEqIrl0HUkDMzjPLawehnleTQoCqpDM0NjaW3r17M3/+fG+ax+Nh/vz59O/fPyLXrAyi5aaU7Yq1WKKAWGk/RlqbLdGaJUpznShl0dVVZ1kLJPw1RyQcBIJoGzq4LfLJbUosLvTtTsiI3IblRW2/ukXOr9ZHrZucz+69OnCgg5Vs6ORDbZMuJb/afoNZVLmxc11dXdU6BwtHjqqoZQtg/PjxXHXVVfTp04e+ffvy/PPPk5uby7hx48J+rVCJlopgiZY6kERp8piRHxmqJcpTdn5J2TkiTT1HoEQqV6S7TbZl96Wcrh5zUDMRijXLpcmjs9TqjgdCVNlaZ9FS26o45pLOkbfVfDLUfA4cqLArG2peuzIQKsmR26xcvioLaPbDLQ+1eYyosmTrkksuITMzk3/961/s27ePk08+mW+++cYvaL68KA/blgcM2dWnI11WREu1OrnxJ2jqNWSorj/hKhTneTjhVlT3BUR+t1SWDDk9EOGC2itQNRWRIllmFmJd2TroiFWUtK0OEsGQLp0rxWnfDlRYjSF25EPOZ4eEBbquXRe5mSyIslWSZiYPDuGyhypLtgBuueUWbrnlloiVb4domVm1dIODmq66SlSiFS3lidYc15EuuQ6gt2qJ84TFSiZexdK54jzZYibON/AVMFUIAwlMbRWomoZQBhKrQcRlkqbLG+j66nGZYIn2J4hXINKlG2TMtHr5uk4br70I1DaDUUTsHrNzXbVtysoHlE8B0Z0fiizURvmp0mQrkigP0VKhs2Tp3Ig6oiWv5XQd4dK5EdUvEIVVyyOdo96LIFy6+5EJmO4+dQNPOLUeB1UDwQwkdgYKM5JlFk+lu44OsjKgEiz5uBXp0g0ydrR6ua5OO689CAfJkvPZtfbajZPVWbbsKBFWpCtSVq7ahFpJtkIhWrpjOvKjDhqyG9EtreVFDmgX2278yZZbKV/9ClFn1RJ5ZeGQNR7wtWbJpE6UYce65bhdagbCra3LaTq5CJTPbn1VciWUBl0sotjXWXXlti2Xa0W4dPVwUPNgpz2GYs2yavt2PSkCqqKsUz5EG7dLuiLpZq9NBK1Wkq1QEEiI5E5bJkU6a5dKstQvCOUvCXXkTIYaqyWEQI3vKuYEEZMhC6OomxosKbsiAw06ZnAGo6oLO4MI2B9IrCxXqsKgSws0wIB+GgdVI5dlQyVZajuW0+QBwI5FQIY60Dmo3rArGxA60bKjnOjK0NVPdRfK8qC61a1IV3nlIRgSVVsIV60jW+GyaoG5VUtHstQpF+RFR7pk16JMuHTCJQ8g8leIVpAHIfH1ouxCFMfkwHpZIIKxbslwSFfVQDCDCIROtMxIlbofiHCJbbXdWBEtF3rSpbNygb0BBuy1c7XuTnuvPghWNqD8Fl+zfZWMqWXr3Oxy+xbtXqTL3gs7pKs8bnaHcPmi1pGtUGAVN6Kzaqnp6qCikiwzwqW6E83ciGJCUployfmQ8suDjlwfeaDRWbfU5xGKdUtGIGuFg/IjlEFDh/IQrWBIlrpt5x7EgCEPLCrRkrdFXt2AIsoLJ+ESsLqPSLX7cL3/ykJ1eS6hEC0zi68uLRiLr8gnh5iAf3tWvyzXyUMgN7sTxxUcahXZCpdVyyyPbrARa507UHYnyoQrBn9Xoi5IXg7+FQOJSrRkoZMX2ZolzhP10Vm31AFJve9grFtmqO6DQ01EuImWLv5Ql6ZTcMzalvpFro5oyXPJ6QaZiiBcZnDavR7V4bmEk2hZKSBoztVd08q9rnpB5GlTzORBHQsiSbhqOjGrVWQrFJhZtWQh0GklZlYt1aUoW6+s3IlupTzwFR7dF4gij7yUaMpSSZZL2tcJlUBNF47ajnASLTv/BI0yKVNXFx0xEulyWzeUYy5855hT3YqBCJeKcBEuB9UPkSJaunEjEOFSYUa0zLble9LJgygzEOFyYA6HbEkIRZOSz1GtWToBUt136heIVq5EVfDgxKASRWkQvBnRkr9U9ChrK9ejmTXPapBxBqDqDzMlA/QEyIxoyW3e6p+goLdwWdVFtWjJg4FMrOQ2LGQlVMLlDC4OILxEy0wpN1NcdNfXQede1ykisvVXlK3Kg6zgq5BlwnEnmqPWkK3ymqTNSIdV+ao2olq2dFYuHelyS/l1Gr5qEgZ/a5bsalTJlSpcqisR6ViJJt1BzYeZpUk3GOi2VTe6OvecztJlNbioH3WoA4hMuFT3txnhkvftDi7iXEe5qD0IRhFRFVg7yoiZLIgyVeKlgy7gXSVYcl+ujhFmCog8XshyEC7CVZPHllpDtgJB13CtXIi6c3XWLJcmXafB6H7f45b2VSuAgBAOOPEyzaxZ8vQNZnWRtRj5foP5KtFB9YaV1h4oXec61BEtXQyj2WCjXksN8pXbuky0VPIk7+sIlzxQBjO4qOc4qF2wIx+hWn3NLF8q2dK5/GQ5kY+VSMdUooV0jo5wyUHzVi52Rx784ZCtEKFqG7rj4GuNkgcS9UfTqpVL/VrRZzAqK9wwfIWvGH8B9nDCvSjKEPmQ8sr1Q8ovC20wcISteqK87kN1CUS0VCuv2cAiX18eKFTXiPwxhyhDHWwEVMJlNrjI+1ZKhdPmaz6sFBE78mFm9dXJipnVSyVbYi0UcbnPVi29aixXibSvwopwyfvBKNy1WSl3yFaYoAqYWKvkxcyypQ5YKhmLAqb+9BNP/rCU/Tk5tK7bivGn/Y2LuyaRGp/vPUfW9F9Ztoz/rljB7qws6tapw7AuXbhh6FBcMTHecuWvtKxQm4WkNsNKazdzc5hp5qoy4VbWgf6YYKa9lwAzf/qJ//3wA4dzcmjbuDE3jhxJ++bNtXV/ZNo0Nvz5p1961/btuenyy30GFJXgqc/AkYnaAzv9pIBqeRLn66y+ZkRLPibLk5l1S4YBrP7jDz5cupQte/ZwOCeHBy65hL6dO/spILLnYsP27bw/Zw57MjOpl5LC2QMGcGrPnqYxjVYIVfkwu6fqjlpBtsL18gKVY2XpUtPNtB1ZsOTB6IM1v3P7N3M5o+X9dGrQhy2H3+bW2S+wbOeHXNj5MCPbb8blNrzlfLJ6NY9/+y1TRo+mR4sWbD50iAc++wwPcMvZZ/sIrqiP7Cp0UPsQrFVLPVensetiE2V3uRnZUgcXAdmNLsjWgrVr+c+cOdx5zjl0btaMD5cvZ8I77/D6LbeQkpTktfoKjL/kEgpKSrzulKy8PB6eOpXeXbr4KUe6L7Uc65YDCGzVEvuq0m1m/VX7fTVd52LUXVMoC4VFRWSkp3N2z548/MEHXpkTCrZq1Tpw5AjPv/ceg/r04e9jxrBu2zbe/eILUpOT6ZyR4eNyF/fiWLfso1aQrfLCDsmySlPdiDoh1aXLy8SFK2ld9yLa1R9Nk+Qc+re8ked+WMjvB2aRV3Q9hgF/6bTJW8bPO3dySsuWnH/SSRQB6fXqcXb37qzetcvyPsJJtJyBp3ojGKuWnK6zeKmWLPXH6+rfEuRBRUfwZPfh+8uWcX6vXowu08DvPuccfty8mW9XreLiM8/0niMIV0qdOt6YrhLgx7VriY2JoXfXroB/u3WsWw7KY9XSyYPO+ivWqizorF+qNVmFBzijfXtOb9+eEuBhfJUYlTgBLFyxgoZ163LZiBF4gCYNG7Jlxw4WLFtGl4wMPwuzWUC8y+JYbUYwbajGQjeohOPBBCIvLmVtJkQHcqLZcugPWtXrQ/PUbKLcBgYGbdN6kV24kuS4Ar7ZksGRvHhvOX1btGD1nj38Vkaudh4+zPebN3N6+/aO5cqBH8Jt1VJJlsgna+1W/waNVdYxyhJbtqa4mA179tC/bVvvZMCxbje927Zl/a5dftfU1XHJqlX07daNhNhYP5nVWSoCwelUaxcCWbVEHtVqqlp/VQuwqpyoi5lsqPliyq6rkzn5Wpt37qRb27Y+8tEtI4NtZWOImXKlPgcHejiWrWqAH3YkYFBCi5QEDMPDt1tf43DeHurFN+Hg8R00ScphXWZD1u5vRP/WOwC46KSTyDx+nDFvvYUBFHs8/LVPH8YNGEBh5d6Og2oMnfUV9AOJGnMokyw5NkX3IYhOi5chrFqHjh+nxDBomJTk7cxcQIPERHYcPOhNK+ZETKNcxpZdu9h94ADjzjvPex15fi3dl7fqdm2BFYmsydYL3X1bWX3VfbeybaWY2HWzq0q5an1VPxyBE7In3H7F+BKArJwc6iYl+dxvSmIi+QUFFBUVERMT4003c7E7MIdDtqoAdAJyiDw2sZ88CvmlsH3pcaOAj9ZOZn3mYgC6NBoEBkS5DVwYZBfGeoPjl2zfzv8tWcK/R42ie/PmbD18mMe+/pq0RYsYO3CgaT1q2wDiILjBRBxzK/uiHHkQ0JEtlWCpFi15kJFdLfJ1jpLHBvaTQyH5Zc5BcT74fokofzQikyhR30WrVtGiUSPaNW/ujWWR5UCOSzGTDR3xqs6DTyiWOSt3Vm2AKg+yVQt823AgF7sqL2ZuRfU6WeSxmf0cp5AEYmlHOskk+MinIFsC8pfpIo+QAZXMybIgQ6eEBHIl1kZlxSFbEUQg8qJ+ogtQgof5bGIBWzhELm4gM7l0mJi5YQo5+fuIcsUwuvM9bD38M0mxaRSXuDBwkRhb6C3z8e++Y0yPHlzauzdFQKv0dI4VFvLvL7/ksjPPxHC7/eZiQamLAwd2XIginzqQmO2rwfKye0PV6OXzVdkAKK5j4HLB4pyN9KAZ0bjxAEdzc2mQlORHtuQlv7CQ5WvXMmbwYK3FTg4iVu+5ppGISLk+dcHb1QmBnksg15mVJUsmM6pbUZUFeXJrVREpwcNiNrGYLRwukwsDqEcip5PBGXQAfC1bAqKvLwbqJSVxLCfHWycDOJabS0JcHPExMT5/YxB1tlJCHPjCCS+IEOQGKH8qK0/NoFq0PMBcNvEhqymihI40oDONaBO7E1xucvL3ERudxJU9nqZbwyFsO7ySZild2ZuTRIPEXLqkH/CajfOKinC5XL5avdvtdz25fnbvx0HthM5FoqZbuUdk8qRq7la/qxLLQjbxKavxUEKHMtnoFN2Qek3r8PX2dSxhU6n26PHw07ZtnNS8uY+7UiV+y3//neLiYk4/6SSQjpvdW6DnUd0gP5eafM1wIxgXorqvs/7qLFtqbKMqG3Js1vdsYharKaGEDBrQkUZk0IASSviGNSxlEyhlqe77KKBTixas2b7dhwiu27qVds2baxUoFeF8p9VdtnSozm2+ysAOEVEnlhNkR/6FwlHy+I4tJBFHY1Jw4Wbb1iN8+M4yMErdJakNz4eoJGZteo4iTz7piRdxrDCODQfH8+wPs7zlndWhA2///DOfr1nDn0eOsHTrVl5esIAzOnbE7Xb7EC7ZuqabMNJBzUWoLkSVjKhpOnKjI14616I8+Ii0nDLZSCGOZqRQkufBDcTgpn//Vuz85Qgzfl3OmsydPDJ7NnlFRZzfsydRwKMzZ/LGt9/6XW/BqlX06dSJ1Dp1/AZBs3sOZhCoqp1rVSE7VaUe4YSZC9HMwiUfV62+sizIFi55ySWPJWwhuUwuXEWl+WNx06AgkeK9xczauxqAfUePsnXvXg4ePUo08Nq33/LEzJnea/+lTx/2HznCu3Pnsiczk/k//cRPv//O8P79tfFhZnKhU1Rq2nsOBY4bMYwQBEUOVlTdhOqs1/LvEtaxn0xy6UgDDODXX/Yyb/ZmPB6D5i1SSa13Mhs2fMOMfR9QN64T/Zq/TlJ8PYZnrOH/lu1iT3YqJZSahG8dMIASl4tnFyxgf3Y29erU4YyOHfn7kCF+15dnGZbrKlDdTP8OKhZWHa3LZDFzJ6qDi7CCbWA/h8pkozC/mDde+JGWbesx/Jz2dOrWiNzcQpZ/9yd/y5lGl8ZNeO2KK2iUlEQhcCArC1wun2vvPniQDTt2MOHKK01JVDDEqjrEoFTVAU/Uq7r2M1bP1awNydYjnWKikxNVEXEDm9nPEXLJoEGpAjFrE/t3ZzNgeFuiYqKYN2Oj95pPzZkDwLk9evDQBRdwJDubzKwsr6u9Sb16PHTZZbw5Zw6zf/yRtJQUrj3vPE7KyPD5VY+IfdS52B2YwyFbNiA3MjPhET/Glc8R6eK3Byq5kf9JVQLkUFgmZG48hsGO7UfxeAy6dG/EqNEdiYmGVnnt6L9/FF0L25IUm0eX9AWkJuRzVqervUSrBCAqilsGDeL6QYMoAgqBImnRuTRl0qWLNzNLd1CzESheC/yDadV86uBhFseikjBBtvIpLCNhbn78cSd5ecVk7s8lPq60C+vdrzl1+sXyV3pyJh0oprSdRwGvjBtHESdkwwW0aNCAmZMmnZAXqQ41bVApD8kKxZ0Tav9Q1UhXOOK1RD678YxWSogau+UCCsrkIgY3JSUetm08REF+MbFxUTRrVZfbJw1kIwcYQ0/OKJMLsUy84AKKKR0bxPVPbtOG5264wSef+rsreSwsz4ch1UFBCSccsmUCQZDMIJMplWTJpMWFP7kSazndDSQQW/YvQw8xLjfDR3ekRZu69OjVGLfLRREeohOO0af1Ns7A4yU/YsAQZEsIiJwukyiPlCYGEtUCp96nmVDUJmFxEBjyACO7icy0d507USZjYjuJWADy8gv5aVnpvD9nDGyF2y3iEj3efEKBMbMUyPVx42uNDuY+qwopsEIoRKu88TLq+cH2EdXl2coIFK9ldkzXPlXZUBfZuiXkwsDD7j+OUpBfTJ3EGJq3SMVF6VhCWT55sJe/zpXHIlluxPUMJU3383YHgeGQrTDAzPKlcyHKv0oQDV008rakU49E9pNDU1Jwx7jp1ruJt+PZQw71SaQD6d65skSZZqSrWEkXJMtQ1qprUY3lUlHdOkMH4YUuLsNOXrN4D3VRJyFtmdea4v0xfLFyNfn5xaTVT6Jz14begXk/OTQgkc6k+w0Wuuvq2m91HOTNECzJiuSgqesTA6Gqvotg2rsuv04ZMbPumll65bRupNOQRDLJYfP6gwC079SAKHfplTPL5KIL6X6ETfTzwgCgyiOate6+5HflKN/mqKpu/CoNXSdgFZOlC4wXa0GEioFEEjiNDHIoZA/HKCyzXhXhYRfHyKaQQWSQRILXTSLcI+patm6p19FZu8xciHY7PEfIah6CHYBFftW9qJalBkarZEzO6/G4+GpDB56d/xf2Lb2E7Wt/ByA59Tq2bjqNIg/s4Ri5FDKUDOqR4FOWfL1AA4aaZodQVkXNPphOXX3egfIFWsJ1Pah+AfSy5VSGSvjN8phZflUlRLb+1ieBs8gg21PAhvWZAHTo3AADj49cpJFgGR8mp8vPXa13oPuQn4W8dlALLFsV+bKFlhDIlWil1Z9WNifKUrawhYPee2hAIqPpzkA6UFR2PZW46axaYt8j5fEoi7CMyfUW5avXCYZUVUXN1EHFQEduVFhZAIQ8fLOpPe+v7k5KXAHxBe9ieHKIr9MET0pflq1JZgdZdOv0MxfRnSF08LMu6+qgHq+qVpRQYJecBPNugoFdF6IrwHEZNen9CNixIllZvwTxysqLJ2X/WSTtrkt+7jqi46LIa13ERg7SgET+qsiFbNkq0dRBdhPKzz0c76AmvsdgUOPJVrghd+ay9UcIgM6VKFx9orGrxEtu7NG4OZNO9KIVW9hPPoUkE0sn0qlHQpmly3+OLjkGS7Zgqe5EOY/O2mZn6gfHZOwgEALFPNrBkbx45mzJIDmugPp19vK/NR8BMLTVNaSlxrHnWDzxWy7itlbpNE840cbLi+qqjZeXaAVjcRIINHgGIlUui2PqNavbQB2I6OvSVaIltlWLk8fjYu6m9ny7JYODuYn8vn8xAOmJg+m2ZQSnd1hHd3cjksvGDPG7KjUmy0rxl6+pxjXafW8OTsAhWzahkijdYKISFZeyLVuSrDo2A6hDAifT2ueLLN0/DWUyJ8dtCQHTxWzJZEue56tEKVO+V0ewah+svr7VtQdV+ZDT1PaktimV6BvA2v2NyMxNpFODTJbt+IT84hwa1GlFj4ZDADdNk1ysP9iQXftb0rzsn6DqtQK1W9mSa/ecqgg7RCtYkmWnTLM86jO1Il12rVzVkXBFAm5gzqb2fLK6O8lxBXSsf4BF20undWifOoJNa86mJ82o12mTz5QN4G9Jky1Zdt3A6jnVUV4qA9XJJV7hCEWwzQLNVVefGjclSJEceyUvBfhP3yCWQmktypG3VQuX7EKUrWK6wHizgSuYAc1BzYWdd2/WnlRZUfPmFMbiwiDKbVA3oSkpcemc2eoqcJV2W2536Zk5Zf8E1ZUjrqP74EPNY/d+qhrCTbSsYqXsxmnJ7i/d+cHUUS23tuNoXjzzt2SQFFdAs5RsDh7fTFb+PqLdcZzSrBtJsQXM3ZLBkbx422XqYigdhBeOZSsEWLkS5TxWXzyp7g7ZEiAIkO4Tdp0JX44PU92EurgtNZ8ZMdTB0SwdWEHIgdwmwbetmikjqlu9TmwhHlwUe1x0Sz+Lzg0H4XZFecsp8ZT+E7SO9E9Qq/LRrK1gJ291IGd23Vk6YhRq+fJz8bq+NOeoz8+OpaS6WLh0lmGzewumHa2TLL4AGw8uAaBdWl9iouJpnJTDhoMNWbe/Ef0li6+DyoVDtoKAHVeinK5zJ5Yo+QSiKSVEYmAQ56uf5JqRLZG3RNqWY7Q8mn3dF4k60qVaJNRnokN16Awd2INVOxdzzBn4tnvd+XJbU0mWPKePON4p/QANEnPZl5NEs5Rs3O4Y77UM8P4TtFP6AUsXudq21UVWmKobAll67BAtOyTLDvGS37satypfRyVdoRCu6g4zWZEJvtq/i/Nki68BbD70PQAdGpyJAUS5DVxlFl+5zED1cRBZOGQL69gUu+eD3rol1rrGrKbJXzLKQYkibgv0nZhuEFED5nVWL/U81QKgxtVUpqslWPeB03lUHHQuZdV1KLcnQa7k+EX5TwoCKQn5DMnYwidrurPrGKQn5RDlNijxuNifk0R2YRxjuq8hOSHfR9FQXeRqms5iJcuwrs2rqArtq7xEKxDJsuoTraxVAoaSLpOu8hKuqmjdEu0sSklXg8vNvCA6V7jar7uhzJJbavGNdhtcctLTbDn4PRn1+wO+Fl8zi7JcvlwPee0gvHDIVgCYafWCoAWyboltAdGJyJ/Xyhq92JZJVolUlo5sqVYCsTZzG6ouRJ0lS71XHSIplOWNzTB7Jw4Cw0r5kC1Y8te38oAiDxKqtUp2j6ttU43nMYAhHTZjAPO3ZLDhYENcGBi4aJCYy4Xd1zCkw2Y/ZUJt37p2rhItXVpVRrBEy8qapX5lFsx1rORMJVnyvto31hTCBebyI8sO+LZ/IRMin24ReTulH6B+mcW3eUo2ibFpnNz0PO+5e3OSqJ+YS+f0AwE9GODb56tKkvp8zQiag8BwyFaQsBIk8Bcm1Z0oSFYUvlq8KEMmW+IcnQtRQHWDqBq9FemS8wUiXRVh1Yp08Gswn6w78Ifa9g1NutzWVc1cdY3rpj4B307J5TYY1mkTp7TawYb9jcgtjCUxtpBO6QdIlSxaol2rX9/K8mBl8RJ1N/Bv62ZKSFVFOIhWqAHTMpECfxIuytNZuaoD4bLq/wPJhnxMp9CqsiHW6tyNyQn5DM7YwqdlFt/GSTlESxbfnDKLr5APuf3KY4BOAdG1c13fX11koSrBIVvlgM66pdNehLDJ/0YEvXCKxq7OfyIPTmbnyMIirqGSKjOipdN45Gvo7t0MwXZ+lfGFkUO8SmFmmRWwsnKJ89XBwCXtizyqBStKkyZfR5Qp2n5SQj6nlAX7inIFqRJt2eojEDVNp1yo9y2v1XQVFTn4WL2vQMRIZyHXkaxgXIoCOsKh6wdFvnARrqoKM9kAfwuSWT8s2raqlAzusBmA77ZksFGx+I7pvoZhksVXp3CbWc3Q5AN/+XAQPByyZQPqgGRXu5HTBWTCpRIvVSs0C4xX6yauoZqGdQJmRrR0g40qaOHu8KrKZ9w6d68DX1iRMtUSq2rQSNsin9nko6pFQP4iV82nWs50P2BXyZZuMNPtizpboTIIQLAyE4hIuWwcs3Nt9R2p1k6VdAVLuKxQWe5EnbJtlkdNU629qsyItq8SLe/7kiy+m8osvkmxhXQus/iqfb2seNj5kERnxTLzdthFbe9fHbJVTqgCJzdM1Z0oIBMtcY78Y1BBstTzAsUh6SxcstCZaTZm58nlh9OEXFVIlgqHdJ1AIMXBTAHRWbfkgUU3eKiDtGizslKCST6dBctqXjmzD0RUpSMcltuKbkdW1iiVTAV7TFe+DFWm5eeokq5QCFdlWLcCWX2tzjMLkpf7eLEty4SqGMtpqpJiUPoRySmtd/i8G5k8BSJa6jih9v9m5CuQxdfMMlyb4ZCtEKEbjGThNJR9HeGSiZYQLDlmSyZvQujM6qJqH6oZ2o42X17NJdA54SRZVgHc5YVDunxhRrwEVEVD7rzlQURn5ZKvIZ+ri11U6wT+7nGZbMkDlDzIyG1dXevqog5AlYVg3Id2yFSgdLWcYGK3VBmSiVUkCFdVCJaX+2qVnFgpIlbWLV08o5giSHa1m1l+ZdkwUzx0X/LKi1yezgMSKc9HTYNDtmwiGFOxKlxmhEvV9lWiJQRT13HKjV4eHHTCESzRstJaQhWo8hCtYDr5cJKw2kK67GrwVsqEmibarjxwIm278NfS4YQMqO1fJgY6xULV4FWSpQ4ggTT5YN55VRtkwkG0dFYuu3Io94dm/ZVdwhUMKopwBVI+ZAgrl65PlWVEZ91SrVnyMxXt1CzMRJUNnUzoLF12ZMHKalXT+8rywCFb5YAsdLIW7laO6QiX3MEEsmiB9cCvEiyRFohkYbKvlolm2y5CJVnBEKxQygvmXqqC1lwZMBtUdOmqa0TV5OXnJ86VtXQB8azdnJj6RGdx0bVplWzJ2nwx/oRMlQfVsqtLqywtPpSgePUcK0JlRrLMXIoqdPJhZs1Ck6YSLrnOdq1blQG13zfLA3q5UK1buo9FhJyIsuS88hyMOmuiSrbMPiQx0MuPbgyRr+HAPhyyVQY72kogC4COcIG/MIqGqvsiTiVquq951GvKZViRqPIQrVAQCtEKlmSFSqLkTt8OaouVywy6QcXMuiUf00FOl7V+cY0o9JatEk0Z8oCiG1hUd4mZEmJm5VXvyy4qqp1YuQ91aTKp0pGvQJYuM8jv0ezegyFc1d2dqPb3Zl/sqtYtlG2dYqJzsZuNDbogeZ2FSyZd4lydXIDvs9fJigM9wh6r/Pjjj3PKKaeQnJxMo0aNOP/889m4caNPnvz8fG6++Wbq169PUlISY8aMYf/+/T55duzYwahRo6hTpw6NGjXi7rvvprjY7BumyoNZQwtkbTLr4GWBUAeKQIvsHlG1EnWgCZZo2REomTCG8tWUVYfuMllCzafLbwdVNbi/vAh2gNK1B7U9ezRpZlYo9efp8g/Wxba8FCvHigMsOplS66ezaqn3p957RQw0dttcIPehGdHSyYvb4ligJSpAubo6qHUN5r4jiXDIhZyuuvd0/b7cp8v/ti022dctQjbMzpHL1ikkOqsWytpMDtS8ZqhtJC3s7XnRokXcfPPNLF++nHnz5lFUVMTw4cPJzc315rnjjjv48ssv+eijj1i0aBF79uzhwgsv9B4vKSlh1KhRFBYWsnTpUmbMmMH06dP517/+Fe7qBg0zc7lu2y7h0jVsnctDNf2q5Er3pZVK6gzlXLnOZh1LMNafcFqzgiVCga4RqDy71wr1PqsjdG3bo6QFshBZfSlodxApCrAvkzX1eirR0hFC9V7k+6uqg0Ig96HOFagSMjmPSrLEOkpKF+7dKJN0M8KmXkPel+ts916t5LQiZDNYuQD/PlinXOtceWbyUIJeHqxIlk4B0Y0xgeRaBytiWtnWxqoAl2EYEe1LMjMzadSoEYsWLWLAgAFkZWXRsGFD3nvvPS666CIANmzYQOfOnVm2bBmnnnoqX3/9Neeccw579uwhPT0dgFdeeYV7772XzMxMYmNjA1732LFjpKam8q/77iM+Pt5WXctr3bDq1OQ0tcOR11YdpRWshF8le3Ies45Ctx1uWJEeHcrTiQZLJO3ed03rRHTPWNeurawk6qAbJa3lbTf6wVod8NU6CKgKjKqAqIqJ6mqU3SzqtqqMBGPVCmebsBurZcdVqPY/qttQl8fqOgJmfY/6/FTSofZP4eybKuId2JELt2ZbR1zVbd0xOU1Xvgz5GaptXRcwb0b0VDmSywkkIzrDg66OOuTn5/PwE0+QlZVFSkqKRc7qg4grAVlZWQCkpaUB8Msvv1BUVMSwYcO8eTp16kTLli1ZtmwZAMuWLaN79+5eogUwYsQIjh07xu+//669TkFBAceOHfNZIgU7gmxm4RJrnQtDl26WT6clqRYxVYPSaVrq/VQm0dJ1GnLHpMsbaNGV4zYpJ1BddKgNFi67RB7pmK6dqiRH1urNNG+dBctMw1e1eNU1oxIqdQl071UJdq1acppItyJaqhtQl25GklWrlhkpUC1cKPu6PkCHirJu2VHU7Fp9delWVlg7FmEzy5aVi111I5oRLTtWrYoaM6o7Ihog7/F4uP322zn99NPp1q0bAPv27SM2Npa6dev65E1PT2ffvn3ePDLREsfFMR0ef/xxJk+eHOY7CA4Gvh2AvO/BN7BRrEXjVTuHEvSdiS6oXq2DvJbzBdI4KkpQzIiWDB25slueYeOYLuDdpeQxS1NRFYJywwXRTq0gt12zYHl5W3w1JbbBd6A3lGvKP+VVrS1yHeS1mYKi0+qtLFmqxi7KBnuDSmW0AzMXm2p5Ua1X4E+EVCIWyLolnoNZgLwsG2rQu5xmmORRn7MurSpCHQvkdHlbyIZVuxHyIQfZq4TV7FoqqVPXOqJlpixBYBnR3aeDUkSUbN18882sXbuW77//PpKXAeD+++9n/Pjx3v1jx47RokWLiF3PbFCyS7hkWJEuFSUWx3QWBjVd1yHqzo2UsJgNDAKBSJYda5NZHt270ZHXUDr5mkS4dDAbPORjgQiXsI6USOlu6Tz5M3ZxzCOlBSJbYq2SLpV4mZErVXOv7AEjWIuOaikSaVaWWzOipSO5ZvVR5UZVZHSES3628nGRLiukBubyZSWXlSWTdpUQsW1VRyEX4pnJz8WKbOmUEJVoibHELC44XFatmtwvBoOIka1bbrmFWbNmsXjxYpo3b+5Nb9y4MYWFhRw9etTHurV//34aN27szfPTTz/5lCe+VhR5VMTFxREXFxfmuwgNdgiXnC53MHKnFEpnb8clGGhAqQpEy0yTDlfclvxOdNYuMytXbSBcdhQJ1bpl1sbVskqUfXGeW9lWLWeB6iuTL502r0tT0+U6qSSuqg4qVjKlPjvVlReIaFkN5rIsqFYtHUEy2wZ/GQskR5Vh3QpGJnTHVJmQf89mBpm4CvlQf9Aue0DMlBEzBURWQtR01cJlx/KrXtuBL8IecmIYBrfccguffvopCxYsoE2bNj7He/fuTUxMDPPnz/embdy4kR07dtC/f38A+vfvz5o1azhw4IA3z7x580hJSaFLly7hrnLICCQoZvuiIYt01USrEwq7i1mZujqXh2gFO7AEGhTUwFJ5W421UmM/1Dy6+BA1j+64XB+reltZ12pDDJeAjpDoNGpVu9a5LnTbaoyJbpFjs9RYLVVbl9N0RCvQIFKRCLYdWSkqKoHSESrhptLFa4m1+hWiHKwt57FL4nT1ke/HrmWtKsmjznNg1s8G6uvVuEZd3JYuLVCco67sYIiW1X3ZRW0kZGG3bN1888289957fP755yQnJ3tjrFJTU0lISCA1NZVrr72W8ePHk5aWRkpKCrfeeiv9+/fn1FNPBWD48OF06dKFK6+8kilTprBv3z4mTJjAzTffXGWsVwJW2reVpqOeK1u35H05zQx2SJMd7aOyiJaarutcddqbmkcHs7rqrFgCqiskWCtXTbBw2dHkdflVK62VhUtn0ZLLV627Os1d3raycOkIn0qs5HNlVCWrlpnFV93XuQ+tiJaduC2UfY/meCBLlpWFy8qdqKIyrFtmMJMJ+Zhoc2byoOuf5X/mqpZk0BNS+Xx57ZHWqnXXzIWoKupy2VaWXxnVvR8MJ8JOtqZOnQrAoEGDfNKnTZvG1VdfDcBzzz2H2+1mzJgxFBQUMGLECF5++WVv3qioKGbNmsWNN95I//79SUxM5KqrruLhhx8Od3UjDh3hQkpTOyzd4GK3U9Hls2virUyipRtA1GNWBMwK6n/J1PpbzXytukLUDr6mEy4zmLlO7BAu8B0k5HNk0iXKhsDvWSVYYlu3H8iiZXcQqUoIJBOq1UglVYFciGauRPHRg/zMVCVFfgdmJMsMOtkLRa7CJYtWirUMlRjpZERXntzmZKJlppDYrU8gd6BKuMxkxczya0cZcUDk59mqLERyni0dAjV6XdlmHWM4oOtcymPN0pVpp65mmnggMiWnm+VVt3XQdQRmA6o6OOvyqmXq9mVUd8Jl9Y7tvFv5varv1KXks0OwdQj0HnXEyg7RktPUbd31wwU77jIrl1sgd51syVKnZ5Dd9XZceeqzUsmVnKZaTewO8HLZoB/kdftm9SwPwi0PVi5Vq225DCvo2r2d7VDfg/oOzJ57oHGnJs6z5fwbMUwIpGXoTM06zV01qwdbB7NrB5MeTNlWMOsIrAbVQGm6TszsWqoWJpvjhfYodyRymeogotPY5WvXVAtXsNq8fI6ZhUvkQUqTLVsiXexbfYErzlXLlQcFq8GivESrsmHX0mtmyQpEtFRiLF9HyJDY9kjnqM9T9+7ld61aTVCOifyydctK7qzKKQ+s5EFuv7r+Xk63Y+EK1cUuX0tem1mpKppo1VY4ZKsCYSWAaI6Vt7FadUR2OymzOgRrybPjHgxk+dDls6qPXHe5E1Q7ajPS5cH3GlYdfTAdf02B2p6tCJdIg8CkSyZYajvSkWFxnrptZqkM1npZnSyXVpYQdVsmXzLRUoPeA1kYVZKlvlOVPLg58VWqPOir5AvlWCD5CpSnohUflVjJip7cH6HJoxIy2aUI/rIHermSr6EjTirJAvtES71XB9ZwyFYYYccKYEa4xDEsjttFOEgWhK9jMrsfnUld3VfdS+q+1TXkwV7uKFTiZUj5RR51X+4ogyVcNdm6ZZdwyYOMrjydNq8rX2fl0hGjQITKLtGqaATjQjSLpdKdq1q13CaLaukCf/k0q5uqyMjTfKjWLHm+NVmmRDkydNYtTPYDIRyyGIo86GTBipQKqHFbqnVLrZfueZgpHPK+GRGTz5PLUtNVVOf+LlJwyFaYYZdwQWDSZZXHLH848kH5hMWsznZiF+Tjqnats35ZXU9A7jjkjk7VxkUno3b8sgYeqivDIVyB3UtyunzMTB6s3BZ2yJQdolVdBhQzWVGtwDrZUuVOZ9myO8ALEiW2PUpe+bjOQi3fi5V1y0yeqoKF2Uyhtqt8qPnAdz4tnVwgHTdrwzrSpVqzRLqZUqKWqTsWCJX9fioLDtmKAEKJc7HKU14EW0Y4BxErLdxscLAaOIIhXKpGFshUrnMjqvuBCFdV6OwjhXAQLvAfaDAp1+yXVWYwGwCsBo7qTrRCtWrJxAp8fxAur3Uyp7u+rLBYTdgp2oLVWpQnIxjrViAZjLR1S0Uo1l6dpVesVbkwI19WCoaOgFmlqelquWbXdXACDtmKEIIhXBDcoGIXoQz6dgTF6r507g51X+3ARZoZudK5OKy0bRVyB6e6OtS4LnnQ0Jn71WPqueq2jOpu3YLyES7QW7kCkS67sEOiwkG0IoVQXIjqcXHMzKqlyhP4EiyZZEWhlzOddVGVKd3zdCvbat8n6ie7zszIF5RPnirLnSifZ0a4dG1UvY5q9bKCrv2buRbVNLUeNVWRrAg4ZCuCCFb7ESgv8QpVICJFBKwGBjPLla7T17kcdaQNfJ+B2rHKn5zLHb0dK5dKuIJ1Z9QEwmUF3SAD+oFGpJsRIAiOfKnn2iFZ6jHdfqDrVAXo4qrMrFlq3JbOkqWmI63lGCOd/Ajri/ruZPeiSqhUuQJz65bu/QRr3ZLLixSCsfYipYu6ybByI5qNF2Zl2yVfcrquPMeqFRwcshVhBEO4BIIlXuHQNsIhJHasWhDYRRjIpWHlVpSham06s7gYHMS2XF9ZYzQjXPK+nQ6+JiBQm9a5x62sXKAfYGWLl10EGhBqEtEy6xusrFouzXFZ7tzKYiVn6juTiZb6w3HhXgT/L+vEcbe0DmTdckl5y/NOynt+sLKgI1xyukpywuFeF+Xrtu0QMPW4bt9BYDhkS4JukAgHQiFcApFu1JEePFSrVjBES+34zbRwM7IFvgRLnURR1E8XsCsPGGaQB5tg4rdqgnXLziAD5gONKAP8NXwzy0YwCJZk2bleZbwznQKjkyl1W85nZtVS/3MoKzNW1mQdyRLpQm5kmXNL23I5MsFyadbyNeR7Cqd1Sy4zVJSXcIk6qP2Gy+R4KKgIF3t179MiDYdsVRDKQ7gihVCEI5R7CKSBi8WKaIUSwKtatkSnKrsr5IFCbMtESyZcOuuWOLc2anp22nQgt6IoB3wHYRnlse7q2nhVJFrl7RvMLMlmio2ZoqOTNdVFKbsR5WvJ8iZkB06QLFGWgZ6ARcK6VVGEKxDsutflvOFyr+vOD7eL3SFageGQrQpEVSJc4RYOuxq4rvNX47N0RMtsINDFlMgQli3ZjSgsXIJgWc3dJE++qA4ydtyJNdm6BaERLpGGkq7Gyal5g6mTWT2CSa/KsOtCdKOXTZ1b0czCbFaOTLLEM5ene7Bq37I1OVjrFpjLVXkUn/LIZHnkwMrKBXqLXSj1DMbFHoxCEkxdqqOshQsO2apgVDbhqugBXjcoyB237NZQNW83pQ3UjGzpNHQVMsmSY7RksuWStovLzhNadZRSlqppq1p3bYvfgtAHGpGO5piunZq930AoL8mqLqRYdSGq5MjMrahTaHQWZDPrmUyQPEo+cUy1XKnuxGCsWzqrspWlORhZrGzCJcoRdRHHBcrbr+jODYeL3UFgOGSrEmAV/FgR140k7Fi1wLcD13X48r48CERLx9VFvj74xpTIFi2ZEKlWrWhOEC7wn+U6lE6nplu3wP5AA8GRLvUadhHoPVU3omVXrtBsm8VsmbkYzSzOatkCsuVYyFSUJp+A+iVeIOuW2bsKhnhUNcIl6mSVphsngnGxW91vdXGv1yQ4ZKsSURGkqyIJlh3orFq6Dl8QK0G0ovElW4Fit8CXbHnw/eRctmip92D2PtTPrwNZt2ob7Fptzaxc4piMYNqWncE0GLJc2e8wmHtXzzEjUSjHZfKls2zZtR7Lli3Qu6zEe4+Sts2sW+q9gLmly451K1hEmnBBaO51sCZfgeplVo9g0gOV50APh2xVAUSCdEVCEIKtn86SJW+bkSzVyqUSLZmEyYvaOavxWiVS2cVKnYrxFQZh3ZK1cFHHEmXb6v4DxW7VVtixZMn5wnU9u6iOA4nO+iVDJU6q/Onk0Uq+oPS56maMl13uckC8oZxjZt0SsqXKWHlchMHKYGURLpEuoB4PV9ssj3u9OspHZcMhW1UI4f7ipLKg1lt1fYg0nWtDHFNdidHoyZY667U4X9awxUAgLFslSn6BYk64P0SHo2rhBjBnyRJWrV/PvoMHiYmOpk2LFpx71lk0bNDA51wZa9atY8GSJRw6fJgSj4cGaWkMOO00evXoUWXeWzgQbEyiXdIVKqoDyQpFiQlUho48gS+JUpUcnTyaWbZkhUaN0/rwp5+Y8cMPHMrJIaNxY+4YOZIOzZv7ufDdwIPTpvH7n3/63c9J7dvzz8svtwyUD8W6VZFKTzCEC+xZe8sjJ+Gy/Nak/qoi4ZCtKozq1qgDdQRyR62LD1HjtGSSJRMtK7Kldm66eC3hRrSqt0rSRJoL2PzHHww85RRaNmtGkcfDl/PnM/Xtt7n35puJjY313qvsXqmTkMCQAQNo2KAB0VFRrN+0iY8++4zExETaZ2QEeHLVC6F8BBJO0hXqYFrd5E1AdQ/aza9z06nkS0e41LJkAjNn7VqemTOHB845hy7NmvHe8uWMf+cd3r3lFpKTkrxlCVm875JLKCgp8X4VnJWXxwNTp3JKly4++ez+wscOgiFc5Q0JCEYW7MhAJIhidXKtV2dEMlzIQS2GrlPWpes0bzM3RrSyxAIxZdsxAZZY5Ty5HJXg6dwncp1uv/JKTuvZk2aNGtGycWOuOP98jmRlsWvPHu09ArRr04bunTuT3rAh9dPSOOPUU2mcns4fO3YEeJK1C4a0VMR5AtVtENHFYcnHZOhcgDoXok4BMnMtql8Di+3/LlvGmF69uKBnT9o3asQD55xDXEwMX61apS0vpU4d0pKTqZucTL3kZH7fupXYmBj6du3qV1+rZyGvdZb0yoSIGbWL8rblYK9jF9VNRqoaHLLloFwI1AnqOnodgdFZtcwsXarFKybAvu4c3XEzwmU1zYQLyM/PByAxIcFW524YBlu2bSPz0CHatGpVI4UwHB2zEcRSHlSXQSRQO5Hbns4SpboA1TQzpUfnXpTXQj6Ki4tZt2cP/du2PXHM7aZv27as3bXLT951StXCVavo360bCbGxpgqbDDuyY4eQWiFc8hnq3FjhJF6hllddZKQqw3EjOqhQqB036Dt4uUPWBcqrBEnkk8uTg+PVLxFleMrKEyZ/dQCQv1wUx7yfuXs8zPzmG9q2aEHT9HS/iTllV2Jefj6PPvMMxSUluF0uzh81ig7t2gXz+ByEEdVlAAmHdcatrGVi5VbS3cp5OuuWgPwMs44fp8QwaJiU5JO3fmIifxw86ONClK8tZGTLrl3sPHCAv593ns/11ZglO65EO+6/YNyJ4UJ55lm0qqvcRsJ5T9VFRqoDHLLlIKww60jM0nXarRw3Ilu4VPeebOkyI1twIl5LoFhTjvpVlViLPMfI4w/2k0chMcTSmHTiSeCDr75iz4ED3H7NNX73pXZ6cbGx3HbDDRQWFrJl+3ZmzZlDWr16tGvTxuTpOIgUnEGkFGZWLjP3vmodyyaPjewnh0LypRnqZKVEPt/KcrZg1SpaNmpE++bNvYqROF/EbVndh4E5gSovsSpv7JaMSHx9HgnS6MhIeOGQLQcRhy7uQkew1FgReVsXNG/2I13VsuWitKOOVuog5xHrEnwJnwcPK9jEL2zhKLlQli+JRA7NzmLXpv3cMW4c9VJTfb6G0nV+brebhvXrYwBNmzThQGYm333/Pe3atAlrZ14VUJVdozXpOYcC9d2ocmmWXyZaJXj4jk0sZAuHyMUFFNcxcLlgYc5GutMMF25cwOHcXOonJXnP1Vm18gsLWbp2LRcNHux3/UBEqjyyUxnWLYHKmtw6EGq7fEQKDtlyEBbYdXXo3IdyGboYkShlW6zlCU7lPOr1RIcu9G45bkEmWKIs2W34C5tYzGriiKM5DQA3RUYJv3y1gYMbjnLR1SNoWK+epdZtBsMwKCkuDpzRQdjgDCT2IZOAY3nxbN7fiOOFsdSJLWRf+g/MS1hNEnFk0AA3boqjPfzSdAdztq/jpM7NGUgnPB4PP2/bxl/79vVzW8pTRiz7/XeKiosZeNJJfu56GSo5siJL1UWBqSqkqzo8q+oMh2w5iCgCkTBd3Bbog+h1s8brrFsydJOK6tyVurisXPJYyRbqEEc9UsjPK6aoqIgNi3eQueYInf/Whu2xu2if3Zp44omNjycmJgaA92bOJCUlhVHDhuEBFixZQvOmTalfrx5FJSVs3LyZlatXc8GoUYEfYjVDZQ8aZnAGk+Dh8bhYuKk9S7ZkcDg3ERcGhXjYkZhMekYSbTusxjCKOXgwh/rpSZzSvxXzPt3A202XU69ZIp8u/5W8oiLO7dkTgEdnzqR+SgrXDBvmI6vfrlpF306dSK5TR6u46KziqgW5OrkSdajJv3Fz4JAtBxGAHStXIHeFzsKlbsuuQ91cW6ITEcHxIibLzIolk7CioiIW7VvDht078ewxOLR7AzmH8mjVszF/rtoHwJrpWwBYzm8AXD56NH3LBpUjWVngOnGXRYWFfDp7NlnHjhETHU3DBg249MIL6dGtm8/9V/eOr6oSLQcnoAZpGybbHmDRpvbMWt2dpLgC2jXIJNptsMOTRc6xArJ/SOOP5Zlk7ttASYmH6+45jQ7dGpGbW8iK7/7kqpxpdGrchP9ccQX1k5LwAPvL5EKW/z0HD7J+xw7+deWVEbxrc1SmK1EH9SObSF+jolCVnnFlwGUYRo18BseOHSM1NZV/3Xcf8fHxts+z6w6rjdAJvplFSqSp1ijdl4VibTZvVqy0RHFinqwY/L9IzMqLZ+3+RuQUxpIUW0i39AOkJORTwgmXYTFQJK3zPR42ZGby2549rN69m99372bL/v2UePy7pPT2afS/vLvXBbmLA5xGT7rQwTufjtC4hWCpX05ZCVx1JVvVgWRV1Wdr9fGIvFa/JNRNn6Cz9soyJ9a66VBUuczOi+fF+UMoLnHTJCWb/KJj/HFoGWsOLWD/4VUYniJvXRPqxDB67Ek0bJyEAWziAOfTkzPo4JU5T9m6mBNyWKLZFouQV0OzlmMtxTboZUx+7+X5RY1aVmWgPHJW2XUPhmjk5+fz8BNPkJWVRUpKSsTqVJFwLFsOqgTkTkQ3gKj51IGoxONizqb2fLMlg8wyd4eBi4aJuQzP2MLZHTbjchsYhsGOo0dZsWcPK3bvZtXu3azds4fjRUWoSEqMJ6ZZNE2bppHWLJW6TZOJSYzxdloePLiAOGKDuteaEGciozoQrdoAeUoFecoRpDQRB6Wmy8dE2qb9jTiUm0i7Bpms2/cNCzY+hSG1zti4+iQkDubkvh669z2Iy1169eKyPHXK5KIqafNVzYoVLKpb3+DgBByy5aDaQRcEP3tTe95f3Z3kuAI6N8gkym1Q4nGx/Wgxzy49yodrV5NVsJoVu3dz8PhxvzLrxMbSuUkTujRrRsdmzWjfrBnRqbG86VpAESXUI8VvJugsckgikaakm9azOnfsgVDdSFZ1JLJwgkSp7j95Xjhdfjmf/IWunK4SMPkL3uOFsbgwiHIbNExqj4GH+oltadXgNI7Vb0VMUnMKMzuRmvY1Lvchb/mZ5FCPRDLwnXdO9+zDJR/V9d06qD1wyJaDsEPt7M3SZKimftHp2+mgD+XFM2dLBslxBUS5VvLT7lXsyd7A7uwNHM3f63d+tNtN1/R0ejRrRreypUWDBpS43RSBdykE+pDBAtbg4RipJAFuPHg4Qg4FFHIK3UkkIawdfVUfOCqCZEWKqFb1Z2sXsjyZxVxZnWu2yOQrPrYQDy6KPS7SEttyZd93qZvQFIBtHGSD5yD5FOOOLVVeivFwgByOU8hf6E4qCV53oJlL3YGD2gKHbElw4rXCDzOSZdbZmpErlXzJA8Tv+xuRmZtIpwaZzN06i1/2fOFzflpCCxJje3JupyTGdK9Dp/R0omNivKRKxG6p8SAAp9CBYmAFW9jNQe+xJBLpR3e60aHWkIJwkyy7X6qqKO/zrorPtryQf5gO/lYqOZbJo6Tr0jxARvoB0hJzycxJonFKNikJTb15WlOfIzlpHEg8zLH0n9nEMQwgjURG0Z0zyuTCUOoj6orFvtU9Rgp2yX1NbDsOKgYO2XIQFgSyXFmdJyAGDDnwVQ2ElQcGsWQXxkKZu6N13V7kFBymaUonmiV3onFyR+Kjk1h7oCFD2/1K7+ZbvEG66gCkdrals8q76UcnOtKK7ewnt2wG+aZlM8iLIN5IQP2qsqIRCQtWOBQauYxQiVdlP9vywEqBEe7GKClNJTUqyZJ/YyXP1J6YkE//jC18s6Y7+45Bw6Qcot0GxR4XmTnJpBbGcUH3nTRO6MxxCkkglvakkyzJhZUMi/rJddPBzjuqju+xtqGmh1UEgkO2HEQcKqEyc3+oA4MZ4VKP1SlzdxR5XHRuOJDODQf6lFvkcWHgIiG2UFum2bXkOiaSQCda+31RVRGdvI70hOu6FRV3FSmrcXmJV02zVAjCJdqxIF2q3KkuQ0G4BNES6ad12AzAsi0ZbD3YEFfZFdISczmn+xoGdviTKFr7yZNHKleVM/CXOwHdF4YVAbttpya1FQcVC4dsOQgr1CBeGbo4EzlIV6SbkSp5pncxOLiAzukHaJCYy76cJJqlZPtdd29OEvUTc+mcfsDn03LdYKBeUzcY6O4L/DvsSGpx1SU4vSJd86ESr8q2cpnJjCovapC8oewLcqW67lT3IFKaWMtly9Zat9vgzE6b6NVqB1v2NyK/bAb59ukHqJuQD/j+e1S1PAdSlNR66p6BvBZwSI+D6gaHbDmoEKgxJSJNaNByJyxPQKr7lY68FAPJCfkMzdjCR2u6YxyDxkk53q8R9+UkkV0Yx1+7ryG5bL4tdR4feW3mqtQF+ZYHNd2kHizJCia/neems54GQk2wcqmuRJVoyVNDwAmiJbbNZm9PTMinZ+sdPvl1XxrKMiyvVfmWiZg4DyUvyjG5fDXdLmqyzDmo2nDIViXAjlWiunT6ZvEj4pguTdW2BWRiJQiQTLBka5ZANKWEa2iHzXiABVsyWF/m7jBw0SAxlzHd1zC0w2afyRVlgiXHl6ikS6eB61wg1eV9RRp2SVN5LF7BfHARLOmqbCuXXdiRO5XAqNYtl8m2WpY8NYQ8wapOhlVSJcuUbFFWZUlVaOR6q/elu1ez41X9PUYakQxBcBAcHLIVYYTq7qmKQmLlIlShuj1cSppZ/Ijq0hMES/2Fj0y63ECU2+CsTpvo12oH6/c3IrcwlsTYQjqXzSAvd/rF+MZdqbNVm7k/RH1191rbYYc8RdKlGMiFGKyLsSpbuQK5EoWsmFm3BGTCpLtX2RotX8fMKmvmSpRly0y+1HJ08V1mUI/VVnm02zeb5auq7b2mwCFbEUCk4mnUCQ2rGuxq2zLREp2xC3+CJZOsKCldLVdo3i4gKSGfvq13+BwXXx7KZEv9NYhu0cWeBArgDea91ARXYjhIVnnlRX3mdolXoGdfkVauYOK2dMd056lr+T7MCJZ8vky0hMVZlKf+Qkh1I5pZuHTue10Qvc5VqJbtoBThGm+q+vhS3eGQrTCiIoOWq7K7w2zgEJqy6kpUNXMdwSnWlCfKiZLKMLuuKE/+D5v6VaEZsZKvpdO65eNymrh2TUM4XIWBZMXqXHWgVctSSYXunEDH1PKr+nu0sm6p9Rck32zKEkGuRFmyfKkWZau4LVnmdC56K/lCWdt1IVrJntl7ro7ELdJjTVUeX6orHLIVBlTml2FVQRsx07DleCYzV6LqMkTaFwRLdRvK5YtOXPyIWv53onx9XeyIlYVLF9elc3eI8kNFVbduheL6Mzsn0A+Xy1u+eI46mbCydtl5B5U5+OisW0KmVEuTeh74KjVyELwgUSrkawnlSFiXxXEzAqojXKoiY2U5NjSLfD/qNWsbGajoscYhXeGDQ7bKEMqgUtU+v68IDdzMaiUQyLXhwbczV61bcgcM/iRLpwnLsScy4RL1VTt+lVgVK4taB91gYBZvotbTDkL5ci4URHoahmBIli5veeqna3eyhQb8iZf8vKuClSuQbFlBtEnVuiUfV9c6y5bsPhRkTnYhykRL97zFtQO5E1XrsY54qWXJ19FZtWREwqpV2YSjKij1lf0MqjMi/v6eeOIJXC4Xt99+uzctPz+fm2++mfr165OUlMSYMWPYv3+/z3k7duxg1KhR1KlTh0aNGnH33XdTXKxzJlUOQnlwugBvqyXUelUFEmilgZp1yiXKtkyOVEIkfrFTqKzFdqEmTc1nFShvNSWE3NmbuUVCQbDtoyLaUzD1VqG2RbUuZh87hLLoylLrZVYfO/ei3ldlQrWo6iyuZsqBSnjMYqhUq69O/tRF/trXzEKskyurD1PU+5Fhl1BVd1SVPh2qTj2qIyJq2fr555959dVXOemkk3zS77jjDmbPns1HH31Eamoqt9xyCxdeeCE//PADACUlJYwaNYrGjRuzdOlS9u7dy9ixY4mJieGxxx6LZJVtwW6DK+8AZ6Y52kFFx5kEciWqbj2ddUunbZs9A6Fpy19bRWnOka+pc2PIA4tq9ZI7f/mzdbNBrjYhmHgsHaGxm9curOK2ZMjWLtnSFaxrsarIl5rHzDomHxNud3ktE1TwlS/ZogV6q5Z6LfmaqmVLlUWd69BMeTGzaslpNYmEVUVy41i5QkPE3mVOTg6XX345r7/+OvXq1fOmZ2Vl8eabb/Lss88yZMgQevfuzbRp01i6dCnLly8HYO7cuaxbt4533nmHk08+mZEjR/Lvf/+b//znPxQWFkaqygFhR8OIpCUh2LIjpREFEjJV89adp9O4ddqvrGELDVpnsSqW0swWca5OI1dJl6qRm8WS6LRus/uvrrBrJQtkOdJZsMzKl5cok0XOo1q3dFYvO3VQ79sKlSFbAmbWLSt3t5mFS7VyWVmUVVlUY7LUj090CoyOaFlZt3TEKxyyVZVJWHn67oqyfFdFIliVEbHndfPNNzNq1CiGDRvmk/7LL79QVFTkk96pUydatmzJsmXLAFi2bBndu3cnPT3dm2fEiBEcO3aM33//Pex1tdPQ7JKsQMfDJQDBkq7KhBk5UTtatbO1ciHKrkTZpaiSLpWcyeeoA4husNHVy8ytUZU7bxnh7oTtkiwdAZK3o6TjZqRKR8LUc9RyzVyNdkhXoGdQkbJl1r50lla7hMvKnahzAaqxjbp4Rx0p05VvRrTAv77yM9ARTPVZoDlWXVDeEJVQEUoZwdY1EkaI6oKIuBHff/99Vq5cyc8//+x3bN++fcTGxlK3bl2f9PT0dPbt2+fNIxMtcVwc06GgoICCggLv/rFjx8pzCz6walBmjSdcjUouR9dxuCyOyagIt4fs6hDbsltD5+aQO0yzT9HVa8jTR7jKzlMtGOo58jXUtRpjYjYgyYOBGemqCp17RXRodoLeZTKjpqnHVIJjVqYOqltJ/j+g7KJW09UyZBlx4Vuuui8j3LJl5goEvYyp5+hkTy1T3hfyI5fnUtLU8+0EycvX0smgGSHUPUsz96GMYIhWVZBTHYIhL5GUc7vjClS8S726Iuxka+fOndx2223MmzeP+Pj4cBdviscff5zJkyeHvdxgiZYurTzar9yIrQTAajCQ6xEuobAaEHSQO3JdGWLbrKMVa/kLKXGObMkQUJ+VmTXNzIWiat86F6KujvK9RRoVqSUGIwfyvkqmZJKlplmRMyvoLBviPZkRLJksGNK6qhAuu1DlSr6XQIRLxGpFYU6+wJ942YH6HkSaGbkKZIWzIsk1BeGMBQ62bzB7jlVJma/uCLsV/JdffuHAgQP06tWL6OhooqOjWbRoES+88ALR0dGkp6dTWFjI0aNHfc7bv38/jRs3BqBx48Z+XyeKfZFHxf33309WVpZ32blzp636hjJg6SwoaprsMtHls+u20ZVjZsGxYwKOtNtDp33q3ABmbkNdzIjVF1G6uKxi9G5DNZZEjinRuUx0pMyOFh4ulNe9FwjBfO1nVT/dvs51J1x+smvQLR1T3YLRSnq0tKjpOvej6rZUrx2Ffx1lt6O4J/WezRBO2bJqV+rAp8qZ6l4zkzdZ8VCtu6prXZZB1VWom0rFE6Acs20d0VKfSXndh1WRoIWDaJWnbwh0brj6nNqMsFu2hg4dypo1a3zSxo0bR6dOnbj33ntp0aIFMTExzJ8/nzFjxgCwceNGduzYQf/+/QHo378/jz76KAcOHKBRo0YAzJs3j5SUFLp06aK9blxcHHFxcWG9l2BcJWbn2LV+ga8mqUtXvwIx0zoCWbnCpYWYWbesrFiibjqNW0de5I5VtVLoYn9Qrh1o4JHdhSqpCuTuMLuGGazeS6Q6snCT60DtOZAlS3VTqfvg37EHuge5/cCJ9+RS9sV7U+VI3tdZuSrLwmXXnag7J5CFC3zLltPEOeL9lOD7ftRnodZL3Q7GyoWSJu+DXtaqu7XLjowGIlmhlq1rq6F6UBzrljXCTraSk5Pp1q2bT1piYiL169f3pl977bWMHz+etLQ0UlJSuPXWW+nfvz+nnnoqAMOHD6dLly5ceeWVTJkyhX379jFhwgRuvvnmsBMqMwQiWlYkyyqfVflmLjSVPNghXRVFuALBzM0B5gOA3LHqPk3XDSB23IjydXSkSyVeKiHTadp2On8ZkdYOw0muAtU1GJIl0uRB3Ixw6cqwgp3BXCb5MunSuRbLS7gqCrIs6eTKinDp8rmUNLlM1a0oQ0fa5DqKdaD3pKbpzpPXuutVN5SHaOnSg5V/Nb+dsBWHcIWGSplB/rnnnsPtdjNmzBgKCgoYMWIEL7/8svd4VFQUs2bN4sYbb6R///4kJiZy1VVX8fDDD4e1HsEQITMCZYdk2bF2mV1XdcHJAqAjXRVNuOxYt9TOXu20VSIkd/CyVi1bt+Q03UBiVled9mzlIpSJFsq5ZteoSIRCrspD+OwoGTorlRnJskO+7MDs3aoWSpV0yfviftR3HSzhqijrlopQCRdSmo5kydYtOa98Xd22XL5O6ZHTzEiVmqYr26wuOlQ2QZYRKtEKhmQFknX1eahjilyGzqIZyvOsCopKZcBlGEaNvO9jx46RmprKv+67zzRQ3y7pCUSidO6rUGI+wL8R6rQ4K1N9oDJUhGNQsCvoamctD7Dqvm6QVmN9dGXormvmzlD3dW5DXb7K1LSDJVfhsKQF6tzNrFl2iJUa26WzVJoN8laDsxnZ8pgc0xFtpH3d9TDZV+sXDli9d6s+xs57CpSmk1uza4P589G9LzPZ1OVXt0NxH5ZnsAu3bAeSZTtEy64ibxdmZNnsuFlaoGcV6D3k5+fz8BNPkJWVRUpKSoDc1QPOvxEV2CFaVp2OHQJmB7rGqmodqrtDHAvGwlVR1i2z/Ia0b2iOy/uylUvcp7pvBZ3rQt2vikQrkgQr2M45mMHbiiirC8q+jlTrYPYOxSLOFcRKtCMdMUNK08mamYXLDBVl4VLlTGfhktOtrMHy/av5ZeuWnMeqznKddGsry5cuXVe27ngkEM73GSzRCpZkBSvX4vmpFqtA3hMzOO5Ef9RasmXHFBsMmVItLbq8ZteVIRqy1ZxAsgDIHaI4VhmEywxyx282KMgDn3xvug5fdUOqRCvQoGxl5dK5NaxcGpEkWpEgWKFqvYHkwo7L0GqRyZjZzPBm9TcjyfIHD6pMqMTBJeUT26JsWTbMCJfdAai8CJZwwQlSBf7yBP5yR4B0+Tp25sWT6yJvByJZumNq3XTlVweEk2hZjS3BuODNyLo6rojr2B1bHJxArSVbgWCXaAUiWWYDhVnnphITgxPEyyyWBJP0ihQKuwOBqlVbadu6r6BU0qVqZJjUw6wTV0mXainR5ZPXatnhgN1OMtwEK5hBwKytm1mz5Nnd1WkZ3CZpOsKlQiVawqIlzx3lUbZFueo7FeRByFsJ/u0rWMJVkRq+ThZ0Vi61ziohk6GL1dJdJ1CdxPXVdN1xHTlTzw8V5e0Dy/s+y0O07JCsYN2Koj0LyMRL7bdVwiXSxb76XK2eVW0kaA7ZKkOghmxGqlTi5TLJZ1aubl8e8OVGqSNdOq09FMIVSXeiCh3hkuuncyOCb2cvdwTqAKLTuK06dSvyZZVfPhYOhItkBToejNXMrJ2aKRTy73JUMuWStuU5t3Rpallm96WSLd0vaNTfxYiyhHyUKOXJecS9ButGiRTsyJiZBVmcD3oXoBXpAn83okAg5Uauh25bR8Ks8uiO1wQES7TsEDHdvoCuncieBZ3ir7oV1evUtHcSTtRKsmXX3GqHaOmsW4EsXro66DoWufHKlha1wZu5Fc0IlxkiSbjMBgCzzl/X6evcIaIsMO9U1Hro1qHEjlQ00SoPySovkdN17DprlkyS5LUgUmICUnlfJVryZKNmhEuVC9kSpf7Lz82JiTZFuYKEqSjhxHQjYi3uWZavyrRuhUq4kNLUWC7dNQQCXSsUd6KVKzCQ21CXJxhUlnXL6jmGSrTsKPJm17W6B5V0yeOKOG5Hma9Iy25VR60kW1awasw6EiUPMuo+0r6uTBU6E63o4KOUfdWsK++rggH2BoVII5DGHcitiJQXfAcL3T2pAzOa/VBjR6oS0SoPyQqGwOnc6FZES/1JtG7md3kG+Gj8SZcoS74e+Fsj5UWexVwmTsVSfQXRUuNVwN915lG2rQacipKtUAiXmqaTI/C/n2CIlxUCxVrZIVm6fKGgovvAUK3JwRAtO0q9DDkuGHz7QjOF3Q7hcqBHrSdbdhtzMERLHRzsEC7VpSYGEZe0Lc7TuRHFOWYDgopIauF247fUfZ1bUaSLusnQfR2lXsusfmoeKwJmdryiEArRCmStDeZ6qozIbVm1QKlxWSqRilYWN/6/4ZGtX3KZOuIgky3VoqVaqcRSLJWns24JiKB50MtfMANMJDR8K5efgM7qq7NyyeVYEcfy3oPumVUkyZJRHpIQzPsMVeExG5sCkSwrK5cVzFyEZp4TGVYKh2PdKkWtI1vBND6RX228osG78R1w5HQrN6MOgYiWblt1cYB/RxqKOzEcKC/hktNVa5NZuWYxJeq1zfZDjR0xK9tOWyuP5Ul3LFSSFajDVzt2M6KlugJ1hEosMSbp0Uo5qgIjoFq11P/2yf/FVBUju5A/0tC5yyrTuiVQHisX+MqegE7pCed9BXJd2skfDlQU4bK6vm7fDtEKZOGyq1ip968jXWaeE/kZBPssa5tFrNaRLTPotATVamJFtNQvr3QkTC5LB4+yrWruakBvlJSuc3cEIlyRtG6JewiGcIk66eK4dKRL5AkGgTr5YGNHzMoMByJNtKzKN+vsVfkIRLR0Fq1YThAtda1auWQLl5llS7VoyYuwZBUp9TW7f1nhEXKrkhIza3IgRFLDt0u4IDDpEuUJqBYvGXYGdCv5qAyCpSKSg36o7kM1zYpomcmnVbngr0SaWaR044gZ4ZKv6Vi3fFGryZaZIKgN1YpomS2yNm5GutQGqevAPdJ58qfoYpCRtRAd4dJdpyIRiHCBtZULzEmXTMyQ8tqpk1ld1G2z/Lp8wcKqruEgWuEiWWK7PEQrlhOkSiyx0raOeEUBbpdUuI4llAmF4TlBsEqAwrLtaEqJlqhXocW9C7kT22ItXJGqi148F7O2WdEyZ4dwQWDSpR6zkrFQ7jHQoFsZfVWo78uKRJTXfWhFtNT9QFYuq+uLNq56TuTyAsUGVwULb1VHrSJbgQYf3eASiGiZfXkF1hYuXUcnN2h1tnRxTKzleBSZjOgIl3qsogUi0CBg5VZUz7eqt458WSHS8SM61015ESmipZYRDNGS+ZBKtMxIViy+hCsOiWSphQQiWx5wlUBMceni8ZSWVQQUSEWosigXI6+F21EOsJflVx1wwFfurBBpDd9OHJeAGemSj+mOR6L+1XlQ1r3TYC3uVmRJR7RUa5aaFoplC/TTC4l6BSJc8vWq8/uMFGoV2ZJhNrionbEV6TL78srM0qWzbgmIjl24RlzSthybJceNWH2mrsaXBCsA4R4UQiFcSGlmQbxynlAFPJj4kYroREKxSIVaphVRU9dqOxaL3PZ1rkNBsuKkdayyHUsZyYrD17zlltayYKo+dmHWKgJ3IcQVQbTHlwTKA5IoAmVftFNVFt3Kvvq8qpo2b9fKBdakSz4uEA4Foio8IxXleXeiv7T7zFVlRncskPKvk0udqxH866W6B1WSJVu0dAqFXYVDtZIFcjfWZNRasiVg1SDd0lolWrKGr37irhIv1QKgXhdONHTRoYuGKWvVOgIgEy1ZeGRBrArWLQiecIk0pHQ7sSR2BgOz+60KMSTBIBirVjDnWhEts7YvL3Jslkyq5LWXaLmkRNn8pU7CJbMlmWiJmUyLKfUTxpauowogofDE6WaQeVs0vpYueZHv38ytqD7LymwzwVi5wNqaZZavpqG8hMvuNQKdbyV3ZgYAM2uY7pqq0qGzbOnisVTCJZdl5k50UIpaT7ZUmDVslTDpiJVugkb5PDtuDI+ymJEsGbofNKsDQXmtP+GCHcIFgUmXKAtNeeGOI6nsZ1Ze6Dr3YEiarlNX0+S5s9QvDlWiFV+2HQ9ERUkJKuHyCd7CnGzJEfFlREuc54qCuAJweXzvWx5QxLpEKlIlV6LdqpZn9TlVxYEmWNIF9omXg/JBJlXyvkgLhmjpxhcr5R5OtFfRZnUxv1aES9620+7D7TGpTqg1ZEtnwTJj/fI5qllWFwysEq0ozXHVnSjqIWsJuo7fha91S50XSHZzyGnqPeiEJZBwREow7H45pXsvVqRLwO6gYufeqtKgaQd2BsZAREunIYvzVKVDlgcz96EcpxUvLW6RIB/QBnFJF5Eh/49HfHJYgK8vs+zc2Dy8L1wmVB58v2YUsqe6EgXRkuVXlS27baWyBpxQSBdExo0Y6BqRvFZlws79qHInp+vIlTq2qFYuNOV5lONCFlzKtm56IXU/kPekqikflYVaQ7YCwaqxmmkPKtFSJ3C0snAJiPFDdleorkHVuiXPfq2OP3Lj1rk5zBp+RQuEXcIF9kmXXHZ5UdU6h0gOOmZES05T5UF2zYk2L9ayC1FeC9ehl2gl4Mu+4qS1zrolV0RmSoJoFZXlF4RLCGgZYvPA8PgqNrI1S3gjo/EdcFSLgpAtO7+pqYoDTaikS6Ai7ydQPFkkUFHvTGd90smdmeVK/QBL9aDI0O3L/aRKtESaUDhKlHPMxhCzYw4cshXQqqUz3aoavY5oqQq5TLp0nZxs0ZI1A1EX8Rk7+GoQ4lxZ83bjT7ACxZdUBuwGlJpZucQxLI4Hi6r0fGRYPYNIIRj3oWzdkmVAGKkEj/IhWoJsydvCuiUTLplsyY1eMCQxc2kB/tYw+aEZEJcHHsN3lnmxHY0v+RLzdMmKkGwR0L2P6uQmKS/pqkhURvuvSKiel2CIlmzZUqcbUsvWXVPu8zzKcXlOR3VssuNq1Fm31LxVUSGJBGod2TLzjcvbOhOsGckKNImjnFcWEhWiIxeNWg54F2vxSbpKusSAIBMt2bpld+ZrHSI9eARDuCAw6RIItmOuDcJuF1ads07p0CkcZm7EqBhOkKo6nCBbdSglWHXwDfCKVS4ioPkKkRggX6qYQrSENhNX4MvVZKIlczTVuiwvcnvRafzVaQCpLqSruhMunQXZbXJcTlMX3XRDMvEy89DIUOOE1fYq75dIaaLOOuIUiku9NqFWkC07Aqpqqjqrluo+lLfVQUdHtlRhEJAbtSGdI9yFav1Vjduj7MukS+6g7JiBK0NIgvlk2q4lq6qYuSt6gCjP9czOM7NqqbIht315Li1h2YpxcyI+S7ZqJeJv3YrHl6mpMVvCDCyIViH+Vi05r8Ss3MUQU1J6mqirPOO8+nWx6k4M1pWoQ1W0gOm+9HUQeVhZtcBX3lSXoc7CZUa4VAixEG3Ro+QVyoe8rRKvylTUqxtqBdkyg5UmIaCSLlXzNfsKy+o/bzqyZeCrSauN1NAsaudvtl8dEAzhgtBIRU3UtkL52MDOOapSoNPKVRnQuRLl6R98fImydcuMcMUCMS5OUDc5CKsYooohthBKSk7EaamxXaoJq2yJOX6CxxXja5EWpEv+yldegnWBVCcrl0BVJV7V1boVjFXLTLHRuQwFGVLHF1UM1HcoW6Pk+F71C3iVcIl8dtyJqpW3OspBOFGryZYKtfHrtHjVfSLcJlakS3U/mlmaAs2p9fFPP/HeDz9wKCeHto0bc93IkbRr3tzHFCzKLC4p4aslS1j2228cPXaMRg0acN6wYXRs397nfquS5hGsO6MygmfDCTPSY/Ulpp17jdSApHb4aueutnuZaLnVKPk4TpArsQjiJciWK5ETrCtWqgH4TKwVdZz/TPuOp576gX37cujRpTEvPjSSvu2b+36xWHRi7YqBmCJ4f80a/v7JJwzr2JHn/vY3r6tetm6Je5efgxkRhaonV+VFVSVeNQ2qFUrnQlQ9KeA7Fslk7Pc//uCjpUvZvGcPh3NymHzJJZzRuTPg/xV8CbB03Tpmr1jB9n37KCoupkWjRlw0aBDdMzK8Mo50nuxOtLonnSzUNBmxg1ovOzq3npnbRGeiVad/kGNW1PmG1IFIXpvlE2V+u3YtL86Zw9WDBvHa9dfTNj2die+8Q1ZOjp9r0wV8vmABS375hUtGjuShm2/mjD59ePODD9i1d6/fM6hqZCVYIRQdRm2A7j51z0vNp+4H84xVWQC9DKjuN7lN+024JQiVSrgSgYQocNUD6gKpQL2ydVrZUldKS+WDDzYzfvwcJk4cysofr6fHSemMGPcOB3JzfLmaEnS/68gR/jV3Lqe2bKl1zejcOVauVvAfLGsaPMrioHxQ25SZVUv90lCco1qVZaW/sKiIjPR0bh81ypvX7Iv5aOD3P/+kV9u2TLr8cp69/nq6tW7NlPfeY8fevX5fPupIoG5bvi8wl4maKCsqahXZ0nWIKnTHdJqs3DGbDThW5MtskQVCLue9Zcs4v1cvzuvZk7aNGjH+nHOIi4lhwapVfg3ZBSz77TdGnnkm3Tp0oGFaGmeecgpd2rdn4dKl1cK9GEpHXpMIV7D3EgrhsnMtsw5CJfhyW1WVB5ccKS/PbKq6EusAsTGUkqkUSslUXWlJVZbSfM8+u4B//GMg48YNpkuPZrwy9RzqJMTw1qerfK1pUgxYSZSHK2fOZMLgwbSuV8+HOJpZtHUEzMwVVFugkq+KIGHVTc4DuRDFvhk5Ud2GqnVLHX/EOHJ6+/ZcP3Qog8usWYHCXW4ZOZJLzziDTs2a0aJ+fa4aNowm9euzcuNGUzelnTHVQSlqFdkKBFVr12m1qs9cdSuafZkVqKFbka+S4mLW79lDv7ZtTwiV203Ptm3ZsGuX1uJWXFJCTHS0j3DHREezfceOoJ9LZTWSUAlXVeuMrepjdY/BkqNgCZcuLtAKKrGSO14zGYgG308TVVeibOmKiwKSgOSyJUXaTi3bl5dkCgvj+eWXbQwb1qvs3CTcCVEMG9KWZb/u0s/ZFQ0Pf7uIRsmJXNurl899qB+yyGsVzgBjjupsAauo/kPXfmTSZWYt0hEt1ROiWrDE9QIp+XIZLo+HvIICkhMSvOfLpEsXXqNT5M3Gj9pGPmrb/Xpht0HoXIjqQKNqw+qgowYNq3FcunR5OXb8OCWGQYOkJJ/rpyUmcqTMjSjXE6Bbu3bMW7aM/YcO4fF4WL91K7+tX09WTk5Qz6W6oqoRLiuESrhCdSkGC7kDDeRGUNu0243v7KbqfBDyJFzUQRCm0iWRUlIl9lOUdRIHDxZTUuIhPb0JJ8xj8aQ3TmRfZo4fySIavt/6J28uX8nrF53rE0NpZsUSkPsBB8GhuhGuioKOpARyz8lrnaVLR6IwOa7mlcnbp0uXkl9YyJldu/p8GKarl6i3gJUrsbbCCZBXoHMLqIIg1lbahm4Rx7Ly4vl9fyNyCmNJjC2kW/oBUhLycXFiYDxGHmvZzzEKyS2bBMLK4qY26stGjmTaF18w6aWXcAEN0tI4tWdPlq9aVZ7HU+EI9ivFmohAk7qqx3TPTM4nb6sT4tqFbgCQXeveAF6Zfcm+RZlwxQBuMclWorIWk3Gd+BrxSN5h1hxYS05hFvlHhPIgzi8szS8qpowo2YUFXPnap7x++Xk0SE7EyD1xP2aaeqD2J8utA3OEU5Yj9RFIuN+jHReiLr8VsZGVGlmxOU4ef7KffApJJJb2pJNKgvd6gkTp5tiSr1EMzF+9mvcWLeJfl15KvaQknwmxdVMMCRlwE3jy7NoqLzWebFkJpJm/2aqDVa1cOreKqmkIwTA8Lr7a1J65WzLIzE3EjYEHFw0TcxmesYXhHTaDu4Q5bGIuW8gkFwMormPgcsGSnI10ohku3LiAI7m51EtK0hLElMREbvnb3ygoKuJYXh7Jycl89u231K9Xz+9+qrrWGWonXZU+Ew9Ul0D3GArhQinTjHDZhapwiG2dpVe0fz/fhvqViFh8Jt+Sg7lKydaRIzH8+ls0321cyW9HlmE0+pW4pBxKiotwuV18+cuP9O3XhqiyHy7uP5BH48ZJvmp7FGw9cJg/Mo9y7tT3TjwrT2nX337yZGbfeisN0tJsESwHwSHcSlO45buyCYAuZEVV5nUuRPCwnE38xBaOkustJ41EziCDgXTwlq8jW2JmFIFv16zhmS++4F8XX0yfdu288z2KsUJe6+Z1REqTp0+p6uNMpFHjyVYgBBLWQNYkK7+1mv71pva8v7o7KXEFdGmQSZTboMTjYm9OEu+v6Q5ASacv+IjVJBJHexrgwk1RtIefm+5g7vZ1dO3cnNPohOHxsGrbNs7r2xfw78jEfcXExFA3JoaikhJ+XbeOk7t2DfFJVS5qg4XLDuECfZs1O6aWqSNcoTxbuwqHHwPT+i+i8GVewq8YS0lJLLNn1+Hrr+P4bdt+dmbFEO0eSlqDgbTvt4EOfX/j+3Y/89Hs7zh1WAfO69QVj8fN/PnbuOXGPn4BJp1aNmDNUzdCLqXLcbj/2wUcLSzkobPPJj0lxe9n7zqS6SA41HTZDRY6tyH4tzHVHadTbH5kE/NYTRJxtKUB0bjx4OEwOXzDGh83ohnZclFKjOauWcPjn3/OpIsuon+HDt6pUMQ8kIZUls66JZMscT92SWxNt3jVerIlw05napbHjHiJ5WhePHO3ZJASV0DzlGzveVFug+Yp2ew8Bl9sbkVB4924XS5iC6LILMwhr6CY5HoJ9O3finmfbuDdpstJaZbI58t/Jb+oiOE9e+ICnp85k3opKVw8bBguYOuuXRw8dozmjRtzKDubWQsXYhgGQ08/vRxPyEGkYXeSUrBPulQrVyArWbBQBw3vIkYEMz+jl5Gp5q4T27NnJ/Luu/HEJhRSUO83GqYVkxiVwoHtO1j2QR5wEgMu7ctnT8zj+Vc/odHYWKa98hW5uYWMu6onuGDsXTNplpbC41cPIz4uhm4t0iEbyAGyITU+nhKgY3o6edLzcshV+VARBCsc1q3KHOB1XgkzgqWGo7iBXPL4kS0kEUc6KRQeL2LT2r1079uM+gWJZB0+zixWA7D36FE27d1LakICTevW5alvv2XfsWM8ceGFuICvV69m0mefcefZZ9O9WTOOZmeXTmoaE0NcfLzf/3p1FitBmMyIk5pemyxeDtmygN3OwkrYRaP8fX8jMnMT6dwgE4AF299g25FfKCjOpaDkOIUlxyksyYNf/MvoP6Q1fQa0Iju3kJXf/cn1OdPo0LgJT15xBWlJSRQBmVlZ4HJ561JUXMxnCxaQeeQIcbGxdG3fnrEXXEBiQkLIvxhxUD7YHRjsTu4aCulSCZdq3Qpm4FKtvQLecnRmL/VTJrc4w9/feORIDF9/HUNyMhQn7ufI7t8p3vcr6/9YSH7OXuLqtGDz8v8x7LqTGXZDNkve+YUBLz9Az5Nb8c03/yC9SRJkwY49WbhL7N1ZTdasI43Ksl6VZ3Ljin7fVuEpYN9jIkTqD/aTRS5taUBJYQkfv7yC4zmFpKTGERsbzfwZG73XeGzOHADG9OjB0xdcwKHsbPZlZXmvPfOXXyjxeJjy1VdM+eor73nDe/Tgzgsu8Pl7iaizzqJlZaWTFZnaJmsO2SondAKuS8spjMWFQZS7tIkdydvLnuwN2jLdbhdxcVHExEUTGxdFfEIMAD36NSehXyzn0JO+dKCA0kmxAR4dN877NxKATq1b8+9bbvH644W5uLY18KqGYDTxcJEuu4RLvXYUwcHUHaLzw4PEytRIyFKsWRNNZmYUnTvDh5//i+1r3vEec0fHk9ywHblHojiwrSmnXtidpDNiuKbnQP7Svi1wCIpKLcgL3x9XasnKwlcAyrZfu+ACcoA86d6lw35rB1XTLRiMbFX0uwykkOvyqBYuncckn8IyHcaNOxY69Ejn1x92suTrrVx+Ux9umzSQTRzgYnoyrCx2C0rb+DNlBEqMD29LY4i6FOFL9lD21UB5+R6cn1OXwiFb5YSuAan9uQEkxRZi4KLE4yLabXBq84vonj6M2KgE4qISiXYnsjKrPu5+Mzij3U5crigfolQClJQ12wRiTU2vgeaZqs4NvjbEbelQXtKlpgfzHNWf00YO/upATg64XAZRUdCkeT82//4Bac370aj1YOo370dUTBwHtidQmBdLiacYMEiKjcErNUIYhAAZ0iWkJRQyVZ3lKBRUF7mrSu/FKuTELI/OhSgv6lQPiaVfl2DgwY2bUwa0YsuaA2QfzWfl9zs4dUgrXEAKsT6DvRADYYkSSrqhWYTiJbbF9eVt9bnLcVsOSuGQLQuYfUKvws6cR13SD9AgMZe9OUk0T8mmWUpnn/N3HUumY2oRRrN97Hfl0pgUvzIPkkM9EmlHurZOtcX3bRdVNeYm1DgTu/+osyJdKuHSpYlz1Xqq7VF0vrrjhppRzizvG+LK8lKqTycllWAYBiUl0Lv7pWTVaYArJoGUuGTAg6esN49NyGNvTiYN6yTTvVEzvD9BLNVQfEcWw/dShuE/sMhZ7AzeVWmADyeqC8GqjpANvWJfl0fe1rkVM0gnjUQOkkMTUoiNi2LAyAy++uB3fvlhJw1PSqRBg0S6ku53PTmEAPAGw6t2ZvXDFzVeS7Z0yV8l1kZXoRUceZJg1jB0ZMpvvECrNHv7+7oJ+QzP2EJ2YRy7jiVT7HFhAMUeF7uOJZNdGMe5Gds5JyGdbArZzTGvJasYD3s5Ri6F9CeDJBL8xq9gZwN34A+/AG9pCTfK+37szM5t1m7lMuzk0+2bXUdLssTFZMIjW5y0jotCunc/TsOGRezd6yE5IZlOjbtTUJzPsYIsPEYJOYeTSEjNorjhL2QX5DAyoxf1EmIpnWur8ERR4p/VJ0zE3ocnf/quyq/6nKrrjOjBQB5oHVQs/LzryloXs1WPBM4ggzwKOcAxDDxkdKpPq4x6lJQY/PjVDoYa7WhAgvYPJ2Z/f1CnLYrS1EGtn+5e7IbZ1AbUestWICuD2unqJnfz4D9+iAYqk64RHTYDMHdLBusPNvRevUFiLpd0X8PwDpspoQNFwDy2sImD3jLrkshIunMqHXy0b/Dt/NVB0eqYeryqI1IDgB3ht7LwhAqrWKtgEMjipV4n2NgWK4uWzmXulQcDXLILT9VAxGIUgauMIJGPmAKiXr3jjByZw7vvprBrl4u2jVsBJWzM3MbOnQbF+QW0OONbklKOMzJjIKM6dKL0E8N8KDJKDVxikYlXyYltmWTpqqoqNepzqAlwiFXVgc6apVqb1GD5AXQgGljKFrZxEJcLWv8ljZ3/OcrBbbkU/F5MVDf99eQ2rBuz1G9Z1EW2bFnFbTkuxVpAtqw6RNmVop6jm59IPk8mXqJhmjVW0Rij3QYjO22if6sdrCubQT4ptpAu6Qeom5Bflt/NCDrRm1asZT/ZFBJHLK1Jpw4JFKHXwnWDntbaEAA1ZQAJlkCFWnY4npdcRiSJl0yyZBeCLAPyV4lykLzc5uX4DaRtHWnxkq0T3kH/yNsiIDYfKOAE2Sq98qhRpSV//XUSGzdEAe1oXtKCFk0P0OOMLQwe3oyTm5xCvQQDOAYcLy1DR7TkpYx0yZxP50LUKVc6RUeWR/l5V2U4JKtqQPcedGlmVq4Y3AylE/1pxRb2U0AhyWmxnHRmE15c+D0TvplLvdghQCqJ0ngTha8My3NnCXIl8lhZ/GXipd6DOt+W7ovE2jL9Q40nW2ZQtXsxiMgDTCAio1q1VNKlki3KjqUm5HNa6x3e8qC0/5eV/mQSOIXWPuNRock15bqYuTnK05hroiCEy5Qtk5dwQFdOqHXVBdZbES61HqqGqpJ3M8uu7DF0y0RLWJTkRu1Dto7jOwUEREV5OO+8Ys488yhr1sSTk+MmKamE7t2PU69ePUonPz2OzyylhSWlvK2AUqGRtyXyZZT4ehhlXigTRvleawIckhUadGQiklCng1C3VStXXRI4ldZeCep9WnM+XLGZ/Tn7ufebHzkp/R4MSv9YclbGFs7qsBm32/AhPKVfNp4Yx3SxWmodZPJkBTsxXDU5zqvWkq1gIJMYYQXQWbXkqYSKOTGQFStl+X7g7jtISR4OnzFKdPw6t4dqZUOzL6OmNmYBO0IfqWtG4tlalWnnXnRWWp1LUW47Ucq+nEclV7q2KDhVjEqwdESrAIgxIDaH0i5JXE1kjqFevWgGDHBLVxDqRz6lrsPc0rUnrzRJkCuxFEnrskXjVdTGb5lZt8wsx1U5fjKSBCFUuapKz0cHM2VTp6RYwSqv6j7UnaOmqyRMJURLtnWhXdpk9ufcwPbD7zCkzQDq18lgX04SH5T9sWREp01+LkIP/kRO5EG5JujrUZOUk3DBUXIkWLkLdNYjWftV0+QxJthFPl/VuH1CXfDv9HWNXD5uFmtT1RGuhhrp4MxIBdSbwcyVrEK1eNolCDqiIaerCoCfW04WhBJ8zbTCayg4U1ERpa7A7LLlmLSfVbaI7WPA0bKlLK8nt3SyrHxlEZYtmXAV+3oZZcIlEyyZQOqsxqHKTkVbi8MZ9B7uj0jCWVY4YeYlqEyYBZyLdyve87G8eOZvySCjfh86NRiIgYdvNj+H21VCs5RskmMLmLclg6N58VqrlS7gXb1OqPWtrahVZEtHNFSoxMRMk1WtUfKiI0+yYq8q+eoxXRkeaa0OcHJd1PqZad81FVbCXZGCX1mdTCDipSNcZnlVQq7GKqrKhvKh34lJdks/LjyxCKuTHKaVV7YUFXCCSAmCdZgTxEpsH5GOH4WSMqJ1nBNrmXAV+i4lHn95Uy1bVsHysuJi9QyrAsLRyVc0Eaps0mWXZIWbjJnds5m1yyzPuv2NOJibSHpSDkMzbibGHc+uY7+zen/pLPKNk3LIzE1k/f5GfpYquTzVCxNK3R2Uota7EXWmYDluC/yJFfibV8386vL5UfhrCOJ6qpXA5OMpnwFNDexVY2ZkK4TZvdcWVEZHEEnXoh2I66r3Lrd52Y0op8uuREM6piMfZiTL6wSUrVliZoZ8TnxbrgpLfBHEZoHrxJeJvk4OcZV8MAp8rWR5yiJbtqTYLWHo0lmUVVe9lduwqluLy0O0qsLgWdEyFMk+0Y7b0Y7iY6eOudIfS1LiGjKg9Tj25mykXb2+QOk/eV0Y5BaWToqqcw2aQQS+1ypLTRgQkee1e/durrjiCurXr09CQgLdu3dnxYoV3uOGYfCvf/2LJk2akJCQwLBhw9i8ebNPGYcPH+byyy8nJSWFunXrcu2115KTkxPWeupcKWauRF2HK3fIqhsxkJtQDVuRl0KTc8xciKoWbnaPHs1xq+dRVVARJmvdZ826JRRU9qClaxd23GE6l6Ha5lT3ofaDQwPfECt1kS1SIs49BygogJJsSq1Zh4GDJ7ZLskqPS+Fa3u1c/AmXZOGymhXCZDouHyXG7Jnafa4VhfK012DarJlb0c4SqToFi8p2GYajnchlJEp/LAHo2/yvnN/5IZLi6gNQ4nFh4CIxtjAMV3ZgB2EnW0eOHOH0008nJiaGr7/+mnXr1vHMM8+UfTlUiilTpvDCCy/wyiuv8OOPP5KYmMiIESPIz8/35rn88sv5/fffmTdvHrNmzWLx4sVcd9114a6uFmauRJ1FyWyAUV2HKpkShEomVjqSpVusrFpmLkQzrTuQkFcX61eo7sNgSVSopKuy3SJgTrh0hNzKiqNzJQpbk0y65DZvqC5E1RJ1nFKypK5zgGwDckvgeEnpOtvwJVc50jnqIpOtQvAU+cbLi0W2HssyZseFGCwqQqYi2UbDGV8VbFmRkKGq1sdZWbfMlCJxD2It/liyLyepNMHl++T25STRMDGXzukHTENlAqGqPbeqjrC7EZ988klatGjBtGnTvGlt2rTxbhuGwfPPP8+ECRMYPXo0AP/9739JT0/ns88+49JLL2X9+vV88803/Pzzz/Tp0weAF198kb/85S88/fTTNG3aNKg6ya4SszRZi5engJA7WRcnTMFqwKyus5C/XNR9TitfV3VVyoRKDpRXBzV1UFCtENXdhVhebcCscy5vubK7LRi4qFxrh04WzI6prkMhA7LyIWRFdX8XUdq5iHU0EFuAr+tQFgJZcxGuxhh8fwbnwldQxMVUAmfmTsz39SbqpuCS5Q5p22xAUskpVO77heDbdiRIjq4OdmTFjtswXDIUrj7Qjnsw1HJ1ZZu1O3nsSknIZ2jGFj5Z053dx0pjtKLcBh6Pi305SWQXxnFJ9zXUS8j383jo2rdaLwfBI+xt5IsvvqBPnz789a9/pVGjRvTs2ZPXX3/de3z79u3s27ePYcOGedNSU1Pp168fy5YtA2DZsmXUrVvXS7QAhg0bhtvt5scff9Ret6CggGPHjvksOugais70b+ZGlMmN6O9VEqRaqdSPsFQXhvKRlK0vFXUWBiurlk441Xuuaiiv+zBSREstKxKDWyShdtC6NCtSIR+z+jhE/uiwECgqwZ8MCQtULr7TZeVQ+pGhWB8zWYs8OuuWSrQM3+m2xGJm1VKVGFWuVFSFQSjcbTGQxSkYd3sweQNdt7wyVBXelUCwY5LY1/X/8npYh82M6b6G6CgPGw42ZN2Bhmw42JCYKA+XdF/DWR02+40jZoq6mqarc1UcQ6oSwm7Z2vb/7X15mFTVmf5b1U0vdNPdQNMbOzSrAqIo4oJGOgIat/hMxJCJMY5mkUkcTVyS0UQniURnkoxJRpOZqJlxi/mNmkzixuJuC0JARBFobEAEmqXpbrrpppc6vz+6T/HVV9+5S3VVd1X1eZ+nnrr33HPPPffW953zfss99fHHePDBB3HzzTfje9/7Ht59911861vfQlZWFq655hrs378fAFBaGvlnyqWlpeFj+/fvR0lJSWRHMzMxbNiwcB2Oe++9F3fffbfv/jpZ+ibvlrbqNfR2JroHa4VIrxZdh0snF0oJ9Hxgp8SKvi1FlcPk4fIS6kjmEGIqEC2pXa/PLB7WuZfzvXixqAeLnyPpgJY1XU9b0504scZcBrpJTbBn+3hP/WAHkCEJv2Y62Yj0aullt/RbJVTwtVJo9sQXMSVEqz0UnSsv5UhKL6DwiY5OOE6/QbISMsAbyZIQC5lzk1Mn3XE6P1Yd6qvfwGluMdXnHiaqc4CZFPF0lwC6k+AX9fxjyUd1JTjWnhWxgjw/lxvyJvLFDQ6/OjBQEXeyFQqFMGfOHPzkJz8BAMyePRubN2/GQw89hGuuuSbelwvjjjvuwM033xzeb2pqwujRoz2fT921dJsOsAFEkiwJetKi4Re+QJz09iMQqSxdZJvORZyIcc+Vk1eLXy/V4YdouU0SXgZFLwMHXVHZDb0hXF7PkwgUPeZkaNDInS6joXTq3dIy3onIN3QzECn7AQDZ7UAGdxXTRK9BOBF75CFH3XFqhfBFSymrIh4tvpA8z5Hka23xLoKV0+5IHsK+RjxeJPFLsnobgqTPy0S64hl6T5axT8sM/bcSavxIz0zrHSdDlCxpVdF1u9D9jyVnjtsdtbCxaS7hbTp5tml7YHUtIhF3slVeXo7p06dHlE2bNg3/+7//CwAoKysDANTV1aG8vDxcp66uDqecckq4zoEDByLa6OzsRH19ffh8juzsbGRnZxv7ZbLkA+wYB62rc1Sol0siXwqR3gBKskzucSch5xaHyfrmCihdQ0KyKYaXSSMeRMtvKELK8ZPgx8vVVzlcbla2ybtFJwUn75aWb022NMnqgMyVcjqADGpFaNKUhRNeLd0IXYdFNyAliTHvlmqPXs6L5ulLbyNKE5BJr5LJok8U0ZLajdUrBkQ/FylHS9IfUy5Xf+dAegXVP9O2dA43nPkcwaMl0t/E6fkohGi95ga95N2SchYlrxv/HaRQ6EBG3KMqZ599NrZu3RpRtm3bNowdOxZAd7J8WVkZVq1aFT7e1NSENWvWYN68eQCAefPmoaGhAevXrw/XWb16NUKhEObOnRvvLochWQsSq+dhO56zYnpDkc4LTvtSGyaiRfvk1Hd6j4C74PeXBRhPomXKCYnnW1ROSLZVlqXf3OTxdPOS8oFa4k08skdXfmhF918YRuRt0TcQW8jnKNs+SrabEZ2z1QJ0tUev/BCRP4ZIZ5j0Igq9P2mS4c8vWXXKDaaXe7j8SvUCwsfLtXhdqcwr0evvHEigd7+t5BWiRIbLHx37Q+zDc4i5x5YbFvxb+kj9ocfc7t9Uz208SjfE3bP1T//0TzjrrLPwk5/8BF/4whewdu1a/Pa3v8Vvf/tbAEAgEMBNN92EH/3oR5g0aRLGjx+PO++8ExUVFbj88ssBdHvCFi1ahOuvvx4PPfQQOjo6sGzZMixZssT3m4huMHm3tHDxUIr2blGPFl+gVBvjIbJPw0tePFsm4aYTm8nqAKKFNhW8WrESrXhY5E7WpZf+mOp59XL1p4dLkn2TLvABX4cs9OBOJ01979L1wkaKArLagYxORIYPeQiRKxk3xXsYXqgr0slF/6XnOCnnXi1pLTtTDmSyheV7Q+q96I5EsGLtgwYfC+nzpLpg8nIl07jlB1qPpHIaUnTSNxppoV4tTlgzSdvSW/DceJJ0wMnLS9uRnBKAt9+pv/WnrxB3snX66afj2WefxR133IF77rkH48ePxy9+8QssXbo0XOfWW29FS0sLbrjhBjQ0NOCcc87Biy++iJycnHCdxx9/HMuWLcOCBQsQDAZx5ZVX4oEHHohbP52EXgumhmL1aTycg7epSRefeKSQlES2gGhiZfrm3giuGF69Wv2BeBGteFvDElFwqudEupKZcGlQWeVyT8t0X7UOcLk2hc253OpPRqh7aYgMnVVPyRZVSMmt1tlN2qiXWPr/aUqydDm39t1yIU2eLDcPV39PKLEQLTeS5Tc8T58L585cf/i+m/4kKwFz0jUtV5RkcTmicshfutJhwxAro6CGvkS2FDtfeuOd6qqbd9cLkvF36gsElFJpee9NTU0oLCzEXbffHkHiTG5qPknobT5pBMk3TQIOsG3+arMfsqXBrQkpbEgVwhQ+NCmG6Yfv64khUUQrkSEHJ6VxUyi35+tFIeOhtKbJ1E3+qdzrFwY1P+KOqaye7UHko9OyaDk/T3uHA0D3eoxs9g2pE3pgCtXzUGEnzESLhuvphCN5mN0MmL4kW/EOu0vHvNbzC/6cTCEmkxdRes691b3ewI18uukXnTtoymImInVNf0ufDFbf9HKWFEHhqTBu6TC0Dj2fz1dO85LuC1hZW1sb7lm+HI2NjSgoKDA82dTCgP5vRDfvFmD2amkBkZITg+y8AKvv1B99TUlI+WDvlKcF0lYyI5a3BP2SLNOE4DX0IQ3QTp6s3nq5vFjp8bDkudWt96kVDUTLP3cyae9Wp3CNdshy3YXuwUcP1hLRCpMtZvJz+TflSXKCRfPJTESL52jx3BQKPwZMsiEWouXH6+UGSfa4jFEvl+ThSjZvlmk+0eD6JZ3vZSFtGkaUwocgbXCyxfsjkS3q5TKt7UjPlzy/fA5Kpt+pvzCgyRaFVgSuEFpATYRL1+dEK8C2Qc6RBiYqpNwj5US4TEQrFuub1u0LxJtoxRr2cAMPeUjXMJGuRBKueIBPehpOBoc0oNLzJNKlj1FCowdxapFHES1E/27c4OAvJuqJwrRIcAerb5psTFa56XdJBp3i6I0OcU+M6bjUjhv4ZEz1iI6TEuFyQrIRMDdoueJLQND7D5F9Hi7koUM6d9C5yRRR4WRLCidKnivpJS0+z3DDvz/1IBkw4MiWaXJxqg9EWyTcOjERLZB9E9Hi13IjXE4WBdh+rHH1RKO3RMvJm+VUz9S2Cfy5mRLeTYN8bwhXX4FPdpK3i4J7dwOIzt3qJHUzESmz1LulvVp8pQe+vBYntdSz5TRJuL3hK00kXMeontFnACSfXvmFV6IVi7fLBMmLpcsD5LiJcPXGu9UfOmeac7jeAbLnmEZGuD4EIBs3dC7Sc5Pk2QJkPeJv55qS5LluSLoiIdX1JhYMOLLFwS12ybvlhXBRS0LyaFHhcvJsSSRJIlVOHi2wNkzX4uirQcgv0Yp3yMOt3PRb0XKJdJm8XLESLreJJFFWvEn+JdnXg7QOf+h/UND/psD1g3qz6LcmWUH2cSJbIfbhVjh/7Z1v88mFe47pN302yeIpjsVT60VHTERLOre34UQpdOhEuOh5yRhO7G0okdeh3i1Kujj56mTn6+dDE+RpfV2PyrmkD3z9OVrXlMaiIZEuvp8MxmZfYcCTLY5YCBcQqTj0FV3dpj4XQn0+mNNvbjXwwd7kvuX3JG33B3pDtHpDsvxMAiYLUCJTElmSBv9EEa54weTRcgonUtnnni1dzt+04vmM2rOlwx38BRPeJu0Hta5N5KmTbUvhECeiRa/p93fo74nEq8z7IVpuni63a5q8qDxx20S4vOhDMpEvN5gMGcm7RXO0tGxx44Ya/TwUbxrXJKNFChdyD5fk1QKidYUbLG7PIl0xIMkWn1hMVoYXwiXV0W3TeDr3bjkRIIk8mciXiWj5sb5p/UTCiWj5CRt6tbTdwolu4NagiXQlmnA5IZ4TC9cLDS3b1Mo2ES6+Bh1vn5MtPYBzj5bk1aLtSJ5dp4miy1CHf6R2uf4B3vUqmeCmD36JlpMeOoHLuUSu6DET4UpW75YEiWS6ebeAyGeRAecxgv9rif6mhj83qCQdonpi0imTjlhSZcaAJFsSuAVvUgzuJqVl1KPFrTB6nlMYke+bSJapTJdLbZqQKkQr1lCH6RpO0IO5Brc2dZuUcOl6EI47lbmhLycSk+eB7rsRLv7cqFeLEiz6p9WUYAXYtqmffKCXck8UoieMkHA83kSrv71aXuGVUEnkl59Dy5xgynsEoskVL/OKRBg2XuFEoiSYvFvUMNH3YzJkdDt87qE6JPVJIlv6OpJBwg0bBVlfuO4A0b9HquhIvGDJlgFuhItOOoAcRqTlvD2n62p49VpJVpCJvPUH/BAtr94sN+tbOuYVEskwWdFuRMgP4eqP5F03mGQfiJ4cgOhnTeWfe66od4uHPXRbJtmhgzkd3CnhAtvvYnX4tjRZ0OuBlSWzhe5Vr5zONREtL4aOFziRLjd9S1fvFt+nY7zJc6zP0Z4v2naAfPjvTmVaMliooSIRMBOp4ki2Ma2/MGDJFp9QAVnQvXi49LlAtEDzV3OlOvR8em2+7TTgx0q0Eq0IvSVa3JvlN8QhXceULyeBygDPqTB5uBLxTJ0mknhPMiaPFpd9PllQ8kQniRAi/0mBf4AT+VtevFq6Xdq+ZJ2bJg9ex4loUYPKK1JlcvFiqDgRLafzaTkgE1cNTrq0vFFZovtc3k0615/kK1bvFn1mtA1KtJz+Lo4aRNxTTOci/ntw/eFeLv0tHffj1TL9HslMkuOFAUu2vMJEuAA5XCiRLj55ObmC6TkablZ1KhItDieiJREqpzIvg78GTeDWMIU3uDXtlXClonert4QLiA6r65wSXYd6s/jkJJFt3j99Pf1tGvhNBEyaGJyIlhevVl/9dn50yw+4HnKi5aaDvA3arn5mGYj+/biXiuuSpFu9IVP9pWdu3i0Fsz7pbU64TAYN1VEnD7Fug8q7ZJz4IVq8fdN1BxIs2WKQLBJJQWhdacI1WW9e+8CvL2071ZP2na7R1zB5nrzmjJhIllN+l1t/6ICuwSfbWAlXKsIr4QKiJwXgxG/Gn5tbXkkAskdY6h8nSU5Wtps3S2qTXkvapkiF39tkgHCvFt+nnhJeh3shncY6aVwMOexzjw/XNU64kj2U6AR6PyaPMRDtpaK/RVA4H/D2m5h0xc0rzL1YQLROQagz0DCgyRafTDT8Ei7ATLrA6vnpm2nfjWSZyvoSXsOHbkTLZEk7kSynsIgE/mwl0sTPNxEuE+Lh3eqPicQL4eIThGSxAycGah7eMIWnAPPvxgdwk4fKRLLgsA/IEz7fThWYPE1SGX/2TkTLybtlAvVuargRLu7NkvQjmXRGwxRKdPJuScY83dayrD3ydDFtJ93iERdJvk3GickTzH9HKXxocQIDmmw5wQvhAiIVA4i2+HWdWATQC2Hz481yaieeSBTR4vtu1rbpGhpSOEIajGgZlwFOuNLNuwX4J1yAHErUvxkPL4Lsc2Lrh2zpfYlwuZEyqS1axrc5kul39uLJpeB6QX8rycvFJ3SQfVObQOQz4qSL75sIlxcvltfxti/0M1bCBVbG25LefJe2dTuAfL9ueuPHMyw5BCSvlhdnQTrCki0HuBEuvQ9SJk3SvRUmLyTL63X6c1IwTQJeiZY0ATh5SPg1+e+WwfaBSMvRZIGbCFc6w4lwATKRlcKxNLRI2+REywth4ERIGuBNpMo0GfBy6VocqfrbS14sp7oS0fLj3ZJytaTQs9PzlIiDlqd00EM/hIvXNxEtyYDk1wRkffHqGXbSLYtuDHiyxScRr+cA0ZMPL5Nc3V7gNGCYBDhZiJbXezQN0G4hC6cBn58nta/BLS06YOsyfT4lWNzCDgjbvbW4Y0EsbfuVTxPh0m05ebl0uWSR67bA2vea+8MJk4k4eSFZtMy0b+pHskPSBS9erd7oHYVJzzhR0jlJbuFFfT03XUuk3nmBH+8Wr8+9Xvo4EGm4UJ1yMmIkmLzDnESZiJXkvZLap9cYiBjwZMsNJkUBZKLG3dy8rVjgNFB4HUT6W8glD1OsRCsI54EfZJ9eh4NaxkDk4MEHf4lw6XMAeSJwKu9vSH1yknUNr4RLuhZt2/TvCtxYMU0SJkLkx2M1kIiWE0xeLScd0x8gWvdM4B4Zk57pNnkZ34+Hd6u/9dML4QKiCRevI5EsXQ540yOTF1jydkn1dBmEcgtLtjzBjXABsjBzQfMbGolHvb4cSEzPyBTK43WcckOk//mSyng7fryJ+nxF9qk3hOcl0Pvx6t3y2he/iNegZgoLSteS7p17s5zapt4u2h6v49ZX3i/JkvZLsvxeO1nhpG8c3LCRyk3GDq1rap8+dxNpChqOOy2X40XXktW7BbgTLq5fpvC8Ppd6kEGOO/VNX1v67m0Inl+HYiCRMUu24C2U6Gb1u1kQtE6s6G2YKJkgeaDofhCRgzsnXU6DPs8lodcDogcoaXLmg73pOD23v61kr/DSx3h4uQBn0qWPS+d7WYuOnmdqXyJZTnWkfY5U+I1N8DIBSx5lrpOx6JwmPDR3ix7rIudRLxWXM6/erf4mWBxedApw9xZLBEsqo3OSmz6ZDBWvnisno8aiG5Zs+YAfq1/DizfLCX3pHekN/Hi1TLkjJi8VJ1r820S6nMIjQGQeAh+Y3SYAIJpc+f2tEl2/t4iVcAGRkwUgky7A+z8sOPVR6pO0zev79WaZrpcKcNJPrmf0GCdT3Jts0jsOuniwyYtFF+qkYwUnXqb7iFU/ksFI8hqep55zp5xIXa7b0pB0VcMpD9IpPOiFaPX3800GWLIVA7xaKIDz4OClnl+kilAHyLcbudLbEtkCIkkXb4dei0LKPdADFS03TQC9DWuYkGy/XyxhRanMRLqkZ+XnGXghTKZwo1sZR7L9NrHCzQDkuiN9JKIlhRUpnEL1+rgT8aLbbt4tCPvJ4OnykwPs5uECokkXhfT8TDB5d2MJw3t9xv39W/Q1LNmKEX4Il4R4C1p/TgSxeLV0Gc/3kAZ2TrR4CJGTMDdLm5Msbi3SZ0knAD6og5Tp8xNlJfc3GY/Fy6XLgMhJQ8Pk7fKDeOSHDCSipcF/S6o3ep+HDaXQvpQ3CUTLAS3jREu/eUgh6R3VLek3o2QqGbxVJvglXIDsyaJwIl1uaTLxDMNLv0uy/g59DUu2euAlb4vDi9WfaKSiIPPnzAd5E4HiRCuISCImDf78etyK1oM9/f1NcqDDGV3km96DNAD6sfhi/S37ykL0SrgAZ8+tRLxA2o7lfpyenSVZMiRPlBvB8mLkALKcUKJEiZb2YkkkgRIL3Q+6fIgppO8XfU3O3AgX4O7lonV52xxO3kanPvBtv7mO6aw/fmHJVhzQH6Qr2YXYq1fLFIag35pQSQQrg5T5JVs6lMg9WnoCADtPTwpupMwL/BCKZHK3e5V1E+mix/jxeMq00zPz+jyTXcckSHpn2jedawohcqJF9+m26Vrcs6XDhVTvKIngXjaeLO+Wv6XPSbZQooab8eLFywVEE1K/pCceofh4XDPdYclWHGF6yyoR7ScL/N6nacCXiBYlVtK+iWjRHC5+TYlsSZ4q+p9jQVJGrWv67ZS86wVeLMxYjnu5Rizw82aVhhvxcqrn9Rq9qUORjLoWK7iRw8E9y/qbGz26XHoxhXq9TNdxy/UBIvOMqMdLsW8n71asetgfoUe/hEsqc3sJxQvB9BqOjzXf0cKSrYQhXsQr1Qd9r14tkH2Tt4sTLIl8OZEtKRGXv3UogU4AQQBbdu7EX95+Gzv37kVjczO+cdVVmDVtWsS90kH/iWefxbvvvRfVbumIEbj1xhtjJlr9Db8eXTfiJdXrLQYyyTJB0j2QbUnvgqw8g+xzvXt27Vo8/dZbqG9uxsSyMnx78WJMGzUqKt8xAKCjqwtPvfEGVr73Hg43NWFkcTG+VFWFmZMmRb2Fp8/x493ye6w/4IVwAbLRSMtNc048DDK/JGsg6JFfWLLVBxhogufXM8EHfj7Y81wtSq4yheOShc0tYBpmcHN3074pAO0dHRhbWopzZ8/Gr/7wB9f7vWLxYnyuqio8OHWFQvi3hx7CrOnTXc50Rn95tUxt+zEqpPy2eCDWSXSg6SgF92yZDB034yYDwOrNm/HgSy/hls99DtNHjsT/e+cdfOexx/DEsmUYmp8f9iaj5/u/Vq/Gqk2b8K1LLsHI4mKs27ED9/3hD/jRdddhXHl51IsnJu+WKX9SCiUmI+KRD5mIkHws+Y4DWZec0JdpRhZphlhCiNKgTvclkgVEky5OtPS+LqP7pg+dOHSZlBOmt3U/Z0+ahL9bsABzpk3z9Cxyc3JQMGRI+PPJ3r1obW3F6bNn90n4sK/gRlydoOL06cs+pxKcwvd0W8qdlI5xD5f+/mN1NS499VRcOns2JpWU4LbPfQ45gwbh+Q0bovQtCGDVe+/h6nPPxZmTJ6Ni2DB87vTTcdqkSfjL229HhTHp9aW+c09dLOjPCdGrHJpkvbe64KUNt7a93EMyjl19AUu2LBIGKYQo7Zu8Wpx08dAhJ0pOBCsolEkETQqPcAvfdJ9evTNrN2zApAkTMKyoSDweb6LVH3koyU5gUqGPiQYPIVIiA0TnZXFyxfUy1NmJrXv3Yu6ECeHjg4JBnDFhAj7cs0fU346uLuRkZkZ4yrIyM7Fl927x+hIJ9EIk+X68PKnxhh+5dCM+8TRWvBC4ga5PbrBkyyKu8DOISZYqJViSVU29VppA8UHcjzfLROJMfwvE++kXjU1N+Gj7dsw99VRfa0F5PZ5MSEZCk4x96itI3iEI+0HhONVJk84cPXYMXUpheH5+hBd6eF4eDjc3iyTtjIkT8f+qq7H38GGoUAgbd+zAO1u24Ehzc1SuJu0bJ12mcn3MD5JhUvQjo731ZsWj3YGqU35gc7Ys+gSmwd0pTCHla1Hiw4mTtMo8vQbNFTFNujrHIwMnBpoQgGNoxQ7UoQXtyEYWRqIUOciNuj+33JB333sPOTk5OHnq1KhjiSBayTAIJvotXb99GAgweZVN9XRdTlj4N/f8tqAVtahDG9rRgc5wOxmkfVofiFw+5abFi7H8z3/Gtb/6FQCgYtgwVM2ejRU9YUeaj8RzseixVDJCvCIeuZCANwPY5jomHpZsWfQZTN4gJ6uUD/imRF0pcZe3BZwgT9IAxElWAEAIIazBNryLGjSgJXwsH3mYicpwH71AKYW1GzZgzqxZyMzMdHwFPupcj9dIdvQl8RpoE4Hf0JiTV0jSu0iPbghvYRvWoAaNaOnWlcEKgQDwRvNWnIKRyOypeaSlBcX5+cjEid9Efw/Py8NPr74axzo6cKS1FUVDhuA/V65E2dChYYLGCVaAfDslygPRRIzuO5E0bXQlA7wus2JCosaOWJ5PuoxjsSAZPKZJg4EsCH7hVXCc6pkmB4lcmT4S0eJrc/k5hycAr8U2vIJN6EIXRqMYY1CCkShGCF14G++L92q6rx07d+JQfT3OmD3b4alEI12tzhD7xLtNCzPcZJWHzKm3OAjgHWzDCmxCCF2YgGJUogSVmSNQVDEYK2s/xJvY1l0/FMKajz/G7FGjwrrGQ/9BAIMHDUJZQQEQCuGtDz/EvClTHNMMTPlX8QolxnpOopBMMp1MfUklWM+WRULgZmXTwV7KJZFCilIoked10DCHk2eLExju1VIAmtCKdahBblcWsBfYumsP8oblomTiMATrg+jsCZt82nAQw/YVITc3F0VFRXhu5UocaWrC33/+8xHXWLNhA8aMHIny0lLPXq10JVoSeJ+9TnapeK99CadEcb0v6ZOUnK5Dh2tRg3xkoxQF2LXlEA7tO4q5F4zHnHljserZj/BYxTsYMTIPz7yzEa0dHbhy9mxkALjtmWcwoqAA366qAgBs3rMHdU1NmFhWhv1Hj+LhV1+FUgpfOPvssHdJ94mvu9UXocRk8nAB/RuST6bnkIqwZMuiz+FExCRLVvpwciXlluj9htYcbKkrwbH2LORmtWNK6QEMyW0LD1YKJ4hWAMDxjg5s2rMHf931Ht7etQ1H97Shq6N7qCmfPAyZuYPw+u/fC/f52ZdW41msxrxZs/DlK65A09GjONLYGHFfrW1t2PThh7h88WLPz2kgES0J6XIfyQCev8i3TXWlXMpa1KEBLZiAYhypa8HLf/gAADB6wlBMPrkErS3tWPfKLnyp+RFMLyvH7770JZT2rLG1r7ERwUAgrHsdnZ14aPVqfHrkCHKzsnDmpEm4/YorUJCbi06c0G2FSKJF+0pDiab7MemSF5KWbIRLoy+IVzLed6oioJRKy+hZU1MTCgsLcdfttyMnJ8fzeX7zHgYqJOWWBnLqXTJZzdIbhvpbfwa57NPzNdFSoQBWbZuE1TWVONSShwAUFAIYlteCz1TW4PzJ2xEIKjQeP453P/kEa3ftwrpdu7D500/R0RWZ/ZGdm4kRYwtRVjkM4+dUhL1ju3EA52A2TsZkhHDiT60V2dYDFn2zx82zNdCJloUz/OqfyXtFXyjh68yZllRZi214HhtQiRIEAbz+523Y8rd9KByWi6u/cRoGDcrANhzAFzAbn8HkcJ+0znSR7xCAzp7tTrLPP/y41jXdRoi0z0PJkt5R/fKqa6mkW70hX4m8T6/Puq2tDfcsX47GxkYUFBQksEd9B+vZsuhTcDJryh3hhIyXS3V4Uu+KbZPwv5tmYEj2cUwpPohgUKErFMDuhk48+NZBPLVhDfYf3YjN+/YhxGyO4vx8jB03Ao1jWzF1bAUKivOhgoGIQTzUs5WDrIj780OeLNGy6A9IeU50Pygc15/BPfKuegJ78z47Abu2HUZjfSvefW0XzqwahwCAPGSJ3mMg+j8PaRjf5Ml2CyXy+4u3R0rfSyroWSr0caDBki2LPgEfwPkxDVMdSrKktjjhamzNweqaSgzJPo6KgqPYcbgaH9evwZ7GTTjUUhvV/qiiIswZOxanjh2LmWPHonzYMBwJtOE/sAod6EIQgYj/dgsAaEAzCpCHUSh1u33PsEQrGl6s9HS+/1jR26RwKWcrAKASpRiKPBxCM8pRgJzcTJx38SS88IcP8Le3PkFB5Qhk4Bwcbp+HtVnA1NIDKMxtC8t2RuQlo8L4EtGiuVu9CQnG47xUIl0WyQNLtiySHtzKptv0Q/FRXQkOteRhcvFBAMCHdSvw0cFXwseH5o5BfvaZuGJmEa46LQ8lhYURYYsOAAXIxZmoxMt4H4fQhCLkI4AgOhFCPZrRhnbMwwzkIdfxj6w54jlIp+OA31uSkI7PJBEwJc67vZxShFycjUq8hPdRhyaUIB8TpxVjwrRifLzlEN56+ghOHnMN/hooAhDA8LwWnF9ZgwU9YXsgkji5vQgjLdUikbBY87ZihZU5Cz+wZMsiLdHSnoUAFDKCCgEAU0ach8FZQzGqcCZGFs7E4Kyh2HJgBE4btREVhTU97xWegB6052EyugC8gxrsxqFw/scQ5OFszMDMnlyteCDZ/4Yn0YhXki9vxz4n8/n8rV23nFVNiuZjMjIBvI0afIxD3eHFyRcjuPUZtB+rRe7x/8GUMUsQCgVwoDkfz70/AwEAn526LSKEKIUJnV6M4X2MNceWk6/ekjFLvCzcYMmWRdIjlsErL6sdCgF0hQLICCpMHnEeJo84D0D3oNoZ6h6+B2e1O7aTgSDOxlRMw1jsQB2a0Y5ByMKonhXkdYJuLOjN4J4uA3pfvL4+0MI+iXjJh3uPMxDEZzAVZ2AsPkYdDrVm4v8+vRpZo0bjb7t/hHd2PYLJI87FsNyRqCg4ir1NwCs1lTh97G4U5LZFkSzu4TKB5m3x31MTJu3l4uUwnBdvDDR5s/CGvl6qw2KAgr6VJx3TMNUxvUVk+v+uqaUHUJzXgrrmfLG9A835GJ7XgimlB1z71O3JysXJGIc5mIyTMA755K96nPprajNWpMuCgjQHr6+vaQc975DIDyVFRcjF6RiH0rqzkNlShrPHLMCYotPQFWrHym3/Bv2ye2l+Mw635GFrXUnESywQ2ufXoKTMT7/7G1bWIpGWyx74QNxloaurC3feeSfGjx+P3NxcTJw4Ef/yL/8CusKEUgp33XUXysvLkZubi6qqKmzfvj2infr6eixduhQFBQUoKirCddddh+bm5nh316KPIS0mairnxILWlY7R/zIsym3DBZU1aG7Pxt6mIegKdQ+/naEA9jYNQXN7Nj5TWRORuCv10e8AEeuAMlD+7DVZJqBk6UcscJODRE5qJhKjw/aZGcCCyTcjM5iDwYOGojPUBgA94XyFlvasiPN6ExJ0O9fppRy39uKJVJY1i/gh7mHEn/70p3jwwQfx+9//HieddBLWrVuHa6+9FoWFhfjWt74FALjvvvvwwAMP4Pe//z3Gjx+PO++8EwsXLsSHH34YXhNr6dKl2LdvH1asWIGOjg5ce+21uOGGG/DEE0/Eu8tJjXgoaV9N0nzBQb/l9LVuXo8SKfpfYXo/QI7r7QWTuwn8qppKbD00AnqdreF5Lbhixvv4zOTtESvKcw8Z97jxfvA6iUSqE61knWxsyMcZToYIBQ3bF+VW4CunP4LCnLLw8a6esH2eIWzvNWcMiF8oMBFJ806wsjawEXey9fbbb+Oyyy7DxRdfDAAYN24cnnzySaxduxZAt1frF7/4Bf75n/8Zl112GQDgv//7v1FaWornnnsOS5YswZYtW/Diiy/i3XffxZw5cwAAv/zlL3HRRRfhX//1X1FRURHvbicNEjEpJVvCsNe/p5HChBLx0uRLv42kF0wMBBU+O3Ubzhi7G1vqStDSnoXBPSvIF+S2GRdCNC2OaOEfvZHnWDwNsU6eybpKeDLAi+d3UukBDO8J21cUHEUBIVpAd9h+WF4LpjqE7aV0gL5GXxAwS7oGJuI+t5911llYtWoVtm3bBgB477338Oabb2Jxz9+U1NbWYv/+/ajq+W8sACgsLMTcuXNRXV0NAKiurkZRUVGYaAFAVVUVgsEg1qxZI173+PHjaGpqivikCvo6l6Qvruc2YPHVnen/FZqIVZdQFmLH6MrSmnQNyW3DGeN24zOTa3DGuN0RRIueZ/JcSdej98DvN96DdaoOyn7ly2kpj75oY6CEe7jcuv2bgS6XDCHdXmFuG86vrEFLezb29YTtFbo9WvuahuBoezYuqKxBUY/ucZ2Ptf+JQF/le9kcwoGFuHu2br/9djQ1NWHq1KnIyMhAV1cXfvzjH2Pp0qUAgP379wMASksjF4IsLS0NH9u/fz9KSkoiO5qZiWHDhoXrcNx77724++674307CUOyKFiiX1mmIT8OKbzI87Go5yrssSIf7tnSoGsGmcKB/G9DOJmTPrSftD297Qa/fxeSikQrGfJkpPa9Tuzp4uVy0j3AWQYlwwOsjOvm+T1h+9dqKrH90AhoDR+e14LPz3gfF/SE7Z3C8/za/Hpe+99baLnpK09bb+eDdJDXdEfcydbTTz+Nxx9/HE888QROOukkbNy4ETfddBMqKipwzTXXxPtyYdxxxx24+eabw/tNTU0YPXp0wq4XK5KFZEnw6t6O9R70oEnX9dHXk3Ku9DlOH0q+KHh7tA/UquaerS72oWFFwH2A58djtd5TFV5lw8t6Tr2B9Mz9TKDpHuoxGTqATNJ4mF0TLKrTwaBCVU/YfmtdCVrbs5CX1R5eQV6340SkvHrEvfwu8dC7viZdscJJ79JVhlMNcSdb3/3ud3H77bdjyZIlAIAZM2Zg165duPfee3HNNdegrKw7ll9XV4fy8vLweXV1dTjllFMAAGVlZThwIDK239nZifr6+vD5HNnZ2cjOzo737cQVsZKU3kw8sQwSifB28cFdsXJOjOggLnm0KMHiq7crRP79BxDprZA8W5RcmSxrU8hTIlTxGJxTaZDsLcnymhjNYXpGTh4tv6QrlX4HE7Sc8r/K4caN1jkTsTLpBtXBgp6wPTeouJEkpQV48SybfjeT5zkeiMVDmixItpzdgYq4O1qOHTuGYDCy2YyMDIRC3T/x+PHjUVZWhlWrVoWPNzU1Yc2aNZg3bx4AYN68eWhoaMD69evDdVavXo1QKIS5c+fGu8sJRyxrxPQ2dyVebUl/n+EEPwORKaRGB14e3uOeJxoC1H+1Q7c7e47T7y6hHvViSRMBtcDdCJY0mKXaAO0HXmTbJH9SuSRzpmt4qWuSf686kczeaAovMubkmaVkB3AnPlxPqHeYe8JMuiXlW0ovq0i6Zjrmh0zEMibGc3zuD9hcsf5B3D1bl1xyCX784x9jzJgxOOmkk7Bhwwb87Gc/w1e/+lUAQCAQwE033YQf/ehHmDRpUnjph4qKClx++eUAgGnTpmHRokW4/vrr8dBDD6GjowPLli3DkiVLUu5NxHiFVfzUcxp0+9JC054lHpagFjYlKAG2Tb1b1LOlB2H6FztakFVP/QzSnmTZ8QmDD/SmyYTeG/d20fvxilQnYW7y7dWTJbXTm7cRTd5ZyavlxdOVyh4uk1eZ7nOvsv7mpIgTDen/COmK8Pw6QHSuJPUySx4uNyMHcNa/ROtYqoQaJaR7uDyZEHey9ctf/hJ33nknvvnNb+LAgQOoqKjA1772Ndx1113hOrfeeitaWlpwww03oKGhAeeccw5efPHF8BpbAPD4449j2bJlWLBgAYLBIK688ko88MAD8e5uBOJppcSDZPXmjSwOaSDo60FCX4eHLXTIjw76dJt+h0gbQOQgkYHIAVkvasgHErckefqRLHU+EfD7M8EPIUuFwS8WouVEsrzUd4JpgpeIl/SKv9tr/8lEuLwkvzs9O6p3/DyqZ5Rk0XAiJVn0meh2pQVFuXFC9c20LemaU0i/P5EOocZkke90REDRpd3TCE1NTSgsLMRdt98eQeKcEC+y5TWs4qc8FpevSXG85Dz0BgFhm/41hx6I6bbez8CJwToD3daAPq73M9gnyLZpe6Y8HykcIg34PAzpVJcTOCm3C4h8zqZnnuyDnl+i5ZVkefF4mcCfmVvIzHTMK2nub7h5A6nu0ZAXJUNc77ieBXu2M9l2BiujOkv1WveDGlcmI0cK9Uv6x8P+XP/cyJiTnMQbsbQdT6M/1ntLhIz76UtbWxvuWb4cjY2NKCgoSEBv+h72j6jjjHhY+07t+AkjmhIjTRZYvDxd1KqWQomK7fMQRhe6B22eeMuXd5AmSZ67I4U5pAFZIk8hYds0oINt8+ulE5xk3E2+JZLldtwruKfG6begXiru0XLTg2TycHmF5FXW5dyTLHmu9D4t19+dOKF7VAepXutrmbzK3JihIUbuVeb3xT1rsXidE4X+zumK1duWijKe7LBkK46Ip7UvHXcrB8zhA6dwij7O20/EAOXUP90vOlDzcCIHHegzyPk0AdSUOxIyfDjhovUlC5q26Qep6NXyQ7S8kiw3AuZ0Xadn5fRWLdUHKV9J9yHVCJdb+JBC6wqVYa5vQfLdRc6DcB16Pl/eRR/X51MPlxQ65LoIROqcpH/Sb+Xk0Uwl+A2nu7XhZbyKd2jRLUyf7rBkK06It7XvdK7fNVX4AMwnGnodr5ONH3j1btGcK2phe2mfJuVS0gU4D/rcwtbX5+tsuSXz0vZ4uT4GpPZg01uiZSJZXsiXCXQpAx4movJrmji07Dh5uVKNcGlIegdEkyGqi9zI0fonLRisDR3J2OFeLX0dej1u4EjheZM+KtYevUYq61g8PGFecnb9EK9kl/NUgSVbcUA8rX2nOlJ7HFLSK3en00FWIl3x8HI5Wdhu3i29TcOJpnYo0aIhEZ4zIl3HFNLQ3075IF6san5P6YpYiZZTmXSOE+hvT3WAyw4nFPo7VsLVn+Akyg36HjjJos+Le7d4OA7Cvi6jekdzxPj1TWRL0ju+9h3XP1PYMBlCiF7gRbbjka/rRK68jPGWcPUelmz1EvGy9um20zEv4MrjNPnoer0JqXiBV+8W3zYpuOQVoxOHaQLX19XnKUQO1tKHhjtMhMs06PtFsg5oXnMIvXqt9GTM96W60nUoTB5ESrzoc5W8XG6Ey4RknIS4QeU2dlAZpmTUyatM87MkI8cL2ZKMG/7CCa/PPdBOv5GT0Wmqk2i4/RZ+IyQc3JOrQe/dRK7cZD4ZZT2VYMlWguDFU5WIsAogTz78+iY3fzwtfK/eLYlkuZGWINumEysd8N3CGfqanFgB0d4up490f5zgcs9dqsCrZe1FjgMO+/R3k9pzgiSz1NsieWl4uMtJ/lM5nAhE6hXd58co0eJhfUAmYCbdAyJ1z408caLlFD6UPHGSsdOfeubVQJbk23Sum9FhihjEK183FWQ9WWHJVi/gxdr3GlLR+24TFt/m4FYt92rxUEtfhlS4d0sKa9B+6m3ehvRN23ca8KV2+EDtFl6UzomHVysZ4dXSdpJbyZtlIlkmb5epL9LEwWXXyasl5RfFQrj6C6ZQohePFj9X0i1OriTDhRKtWMgW1y9q7JhWlDfpH78WvVeO3v6WfqINHF7SQ6QyN8PDbfyhxMuNdFnCFV9YshUj4kG0TCQrYCiX2uWQvFp6IPIaUuHlvSFcpkHfKZyoy2hdXZaB6NfPpWuYQhm8b9z7JHm5+ITgJXwRi1crlQYwP0TLRKokkuVGuDgoaadyRCdiSrp0OZ0wqKwrUuaHcCXzBORm5ACR+qe3pZxJrqM0jKh/N0peTWTLZORw3XMjWhyJ9mrFk2Dx9qQxzO+13XJ2NUykyxKuxMGSrTjCC9EykSqTtS+d5waTtU8HRT4RuSUOx8vDxYkRvZY04IPta6KlSZeUM2IalEzE0slLZSJaTvXTBX4NCrovyTRf8NLrtnQdCicvJZd1vm4bJcNO+17QXxOQybvlBpPO0XJuLPBvXS9IzgXk8Upqi4fuOblyMn5MXuVEebViJVpeSZbJoPY7nvGxWfoNNDjp4l4uyetF27WEyzss2YoBfiwUJ6IlTShOFj4gK6YGtdS5d0UiWLSMthlix3tLuEwEiw7ufggXEE20+LZkXTuFJL0QLjeixds1Df7pCCfjweTNouQrw3Aebd+EENuWCLImWtS7ayIUdIKRZD9WQ6OvQfXOpHO0LhAdXtXHtHEjeZb1eZI3mf9u3NtOPcqcTPkhWvw++P1AqOMHsRAtvyTLDwGjyGD7/N6pvJoIGjcu4mVcW5yAJVsJhJSrwvelSciJcJkGMh6W098mosVDA9J+IgkX7zc9zgmX7hMliV6IltOzov3S39JE4IVo8bb4NWCoQ5FsFqIfrxafEJyIFv+rGPrRxyG0IV2fTip0MtETtCZYVEb1pM0JvUQynGTfJPfJZu07ES5dJnnGuOeKEy2tczRfi7cjPQs3r3JIKPNCtKgMOI1HfUUYnEKAJpLlVs63JdDfm3ul+Bivy/g4r8u9jPXJJu/JDEu2fCIWa8WNaPkJr0jXAKIJBB+E+GBGSZfJyxVvwkUhkSs6kTl5tfg5nHTpNoDIwUOCU2hDImB+B3oTGUtFeJFz6Rwu59K3JlkmD5dpkpHkXJMpLata5iF8622JcFHZT3ZIhMkNXO/dPMqcaFHjR9I/N4OD6hLdNxEuvk/7J+mfyZOdSMRKsqQyP94tDaoP9PfhRIr218nL5UX+LeHyBku2egk/ExAdlIKInEg44aJ1qDJKLmdAXktL8mrpQVNBnni8Tjq9JVh8nxMuWibtSySLtse9fE590m3TdnUZ3fdKtPwOPMk2UPmdtLkHUTIiOLHS29y7Jf1JsknmufeFyoAmXbpPbv9E4ES46L4X71Z/wUS4nLxbToQL8OdRNhk6vI9AtE7pbcnYMREtei5tE4jWqWQkWrSM7kvGudS+BOqVBSLHfv0Mpd9Y77sZ18km86kES7Z8wEnY+URgUiY+iZhCKfpbOt/JuuGDl5R4GkB03oUpcdg06XB4VUKvhItPAJzISKRLl9P78WoJ0m0+oEsTglSP9s/UdqrCaeLQZSavLCdaEsHKINuS8SF5zkweEe5x4fLK5Y/KFydmdNLyimS39L0SLrjsU12UDB2nZ+Bm3Jh0kNfnbUjXjYfu8XGLw0S03LxZEsniBjaXfdM8xMdKSrToPiVdkqEdC+FKdplPBliy1QO3SdnrOZwMeSVafJKRrHy3kI3kgaFv7dEJidfX39QbwAmXruPHxSwhFsKl+wFSj5bp+pREck+ZCRJB8kKqvBItr9dOBsTi1QqwbYl0cUJFSRc/JhkgEsGjv4G0HholW5R882dOZYaTBmkySmZL34t3S6ovES4TAaPhxADb1216NXJMnirqzaL1nPRPl/Nr9DWciJZEopwMlaBDexyShzDI9ulvahpjE+nhSjZ96UtYshUH+LX0JYLlFFLxYuUDsqVPlYZb7HTxQP62kb4fk5XfWwX0S7ioJU4hWdeUeNF+S32Q9iXLWX+biKqXdlMVbl4tvs/lnMs+lfVMyCSMhhv5pKPBjYoQIpO4Q+Sbn6fb1Ns8+dtkaHhFMlr6VMe8GDgm40afx3O46PPxSracvFSShwuGMtoGL080uOzTb4lo+SFZ0pjvh2xJRIsa3/Qe/BIu6Tkkm8wnEyzZ8giTm5iDEyGqLJLFHquVLymc5IbnycJ68pGse/5at8nKj5d14pVwUUheLqmcTyxe+kLboWVO5Mt0jmmfItkGpli9WqaJgRsWXJ410cokdTKFel7IlpZ1LucBAJ1wH+joUiJO6K1Xty9g8m4B/gkXWFtSPWpsAd6eoxdvslMZ/Ta11xeQiBY/5ka0vC6BIumAdL0Q+aa6odgxapDq850IF0cy60AywpKtXsLJ0qdWjkS6+ASUCZlsSSFGk2eLEy5NoExhFQq+YKjJyldsvzfuZS+Ei5ZTLxcQ/dwpWYShDoVTfodpkJeO8ePSfqrDzbJ2ssydiFYmnOXf5M3lhoWWR31uJ6lLCRef3LmXS98L/W2diHsyTjrxIlySR1kK1UseZQ3T8/Gia34MHd6mBD/et1hBCZKJaDktgRIk5zt5uCgkb6CWYcmrxf/o2yl9xM94b71bZliyFQPcvFpOlr4pb0Wy8vV+AOaJh08C1IrR3/ocU1iFQ1qHyOsAGgskwqWvwcOEvA8m0kUhETB6HVOZk5fLdNypbYpUGZD8yDrYN/dKUbmWiJZJB0zeXG658xAiv4dORE9knGAEES0vfALxOqGkysTjRrh0OeDNowzI+syvydvwQ77oMafreCmPB+ni8q+3nYiWlyVQJE+X1GduQIQMH5Bvp/QRUxg9FTy7yQpLtjzALbwSi6VPrRo+ydB9JyvfNPlkQl6EsLNn2xRW4Va+/pYs/nh6t+i1TWVOpIse13Wk9r3Ai6eL10tXoiXBSRckWZe8tJKXi36oLtCPJB/citd1uHGghI/uj+Tdom2afs9kn3C8erf4Pg/hx+pR9gI3/YpF/9zKTfX89t/vuC8RK25IS8clI9vJ8KC/n/T/kl3sPMA9Xxcwy7v1bnmDJVu9gGTFSNv6m1v43IPFJx6TlZ/B2tZwC6nQfpnCKhQ09Jho7xa9vsnLBUSHFqV+9FbRnciTmzWdqkTLzaBwmoiobPN9PkHQSYWTLC77kpFBQS14algEhLpabijBUuRcKaRI5Ytb+NIzSEbi5ZdwAbF7lCWDyQ1eCJRfb1asv0Ms/Qei5V46LhEtbkx4TSGh16R954RL//aaYFGiFUA08eKE2Y+BbeEOS7Z8wk0ZqbL5tfRNkw4lXFTx6PVMuVrUo8X7TsMqIfYtTTwm5Ys3/Hi59DENJ+tLuo5THygSOcgnK0yyzuWa1jXJuttHknspUV5fRz9rvtQDWB1teGSSfYXIe6D7/B7TAW6EC3D2cgHePMq6np9+SX2R9nsbMvSKWAkXhVM0Q3rjlhsgJg8w1wGp71oX9Dd9acRk6OnztBHvJZwo3XO6jX/xhiVbvYTJkpEsfW6deLX0KRmjSkevTcmWVjRajyYLg9WlVrwprGLybun7iEcokfdPt2Mqk0KHkhUulZuuxyENULESrWT1asULTiQswI5zry6X/yAp5waGhp4gpDwtKtPUCNG6wb1btF88JOIk86kCJ8IFOHu5+PlOOu73mbh5huOpf/EE9zDx8VgiWrzMS84uj2rQ8yno+B8k3zSqYVoKR7fFX4ryO+ZLxy1OwJItF5gGKMkKdrP0pYmGTzgm0uUWVuFuZC9EC4gOo7iFVbiiJtqiMXm5wMpNOVux9M3JAvRT7qXNVIJJF6RyPrGYwiR8suFeXjeyRY0ByftBvV7UuxBCdP94GW3f9Bu7yX8yTTp+CRcv4zomGTax6JsXj5mpXiLGnt54t6jniY77XnIWpbxdp3GfPm86/uvfhqePmO4pg2xzwiZFNHS9WDFQvWCWbCUA1OrhFg1XRC9WvmTpS2EVGqvXx7hVEupph+ZzcXImhVXA9hMdSqSQyBUt58fiObn5CTWakCyTbaxwmni4ZU/lhoc+JKveFEqRQolcJnn4EKydLrLtlttlmoz4/cVjsulPeCFcgLOXS7ejYfIq+0UijJy+gOQpkoxtOsaaiJY03kveLZNnqwsnXpDiRMtp/KQkC3DP19X3Rn+zgUqivMKSrV7AadDini8e/vNi5UuEiysdhVa2kOG4Jlra6qHWUIiV8bCKVqT+DKuYSBc9Zjoey3ViPU6RCkTLSY45+CRCy2gdk4UvTTqmPEa639iagw/rStDSnoW8rHacXHoAhbltUR4t7qHl3ir+Ld2fW4jEhFSZbNwIF+DsUQY7Jsm4W/tuehEPI6c38Ovd4saAJO+Sh8spZ1HybmUgWu+ozNM30fVY7eTV0udTwuwU0ZAMjlSR+/6GJVs+4EX5uJXNFYNb6W5WviZf3LVsIlt68uHHKdHSVr4UYqGTYCLCKhRuyzWY4ES66PF4I91IVqxw8gAFhTpB9nEiXlwOEQrghW2TsKKmEgdb8hCAgkIAI/JasLCyBhdO3o5AUIUnMi3TJo8Ct9a5rDvdYzrBK+ECYjNuYpH/eBo5fQGTjJi8Whls2/RiFCdfej8A57FfJ8bz8CGHIh+qdzSiIe1LRrYTkimEngywZKuX8Grp83LJyubhRIloUaVraM3B5roSNLdnIT+rHSeVHkBBblvExEGtHv3Nk+clV7PJ9czvSW/7HQi5Ekqvkrshnt4sr9fxgnQYYGJ5npLcc/LFCT3f5xPRi9sm4Q+bZmBI9nFMKz6IzKBCVyiAfc35ePL9GQCAxVO3ATgxOWiPlkSuaN+ke5QmiFgmmlSAV52Lxbhx86R4RbIRLCdweTd5U3kUg0c0suCcQiJ5Zaknl0YmJFLGP/RNdLd8Xf57WELlHZZsxQATmeJlJuUzWdqSJaQ/7+zciV+9/TY27N2L/c3NuPWcf0Jj20UR1n5xXgsu7LH2M4IKb9TW4nsvvYStBw+ivKAAN86fj0tnzw4rZSe5Dl2HiLqfgWgvF1h5PLxcGrGQLgjX6Q356s0AP9AGHul34mWcaIHtmwyVhtYcvFxTiSHZxzGq4ChCoU7Ut9ZhWO5IjCo4ij1NwEs1lThr7G488f7r+Pe33kJdczOml5XhnsWLcfKoUUZP2jPV1fi/detwsLERQwYPxhnTp+PvFixA5qBBvXsgKQgvXi7An3HTW5KUKB30a8yZCLmGRGqcxnZuULjl6krhRBOJ0p4t6tX6n7Vr8Z9vvYVDzc2YXFaGWxYvxuRRo8R0kSCAv1ZXY8W6dTjc2Ij8wYNx6vTpuIzoBTVc6Piut2MZ8wcK/M5pFjHAbfLhE0IGK8sA0NbRgRmlpXjg4osBAG/uHIuOriCmFR/ESSUHMa34IDq7gnjq/Rl4edsk7D5yBFc98QTOHT8eq77+dfzDmWfijj//GW/V1Biv4xRG6euwirbQYoVkwXn99Ed/BxKkyQKI1osP60pwsCUPZfnN2Hd0O/59zVV4fNOtUKr7SZflN+NgSx5+9c5O3PHSS7jt/POx+mtfw0mlpfjSY4/hUHOzeP2VmzbhdytX4ovnnYdf3XgjvnnppXhn82Y8vWpVAu42NeBXfuOhM/FsLwRv95BoPZXGTcl7KxnUUvhQe7kG9XyyyLb0ySTfz2/ejHtfegn/eP75+N+vfQ1TSkvxrcceQ0Nzc9S1gwDe2rQJT61cic+fdx5+cuONuPbSS7Fu82b8SdCLvp4P0gFpT7b6mmWbHii3SExEi5Msvb9w0iTcs2ABzhs3GwCQO6gTowuPIjNDIRAAMjMURhUcRUHWcbxcU4lfv7MRY4uK8OOFCzF1xAh8de5cLJ4+HY9WVztOdpR06T57vUev8DPYeR1E+wvJ3LdUR3N7FgJQyAgqDB88Ch1dx3GkbS9qGzYAADKC3VPz05tX4NpTT8WXZs/G1JIS3Pe5zyFn0CD8ccMGsd0PPvkEJ40Zg/NnzkTp0KGYVVmJs2bMwMefftqHd5eciFWWe2Pc9NaLFUufE6GzUhSD70uhcqmMEi8aVsxAJKkahGhvmP78Z3U1rj71VCyZPRtTSkrwgx69eH7DBjG6svWTTzBlzBic06MXMyorccaMGdj56adi6NLCH+zzSzCcLHgOiQTx8MoHdSUAgGG5rVAqhE11K/Dwhn9ER9dxBAJAeX4zDrXk4Y2d+/CZCRMiFP28ykps2LMnom1TbJ+7ypMByURskqkvqQSaPygd098KQF5WOxQC6AoFkJWRixmlCwAAG/Y9DwDoCgUQUh3YUV+L8ydMOJH0Gwzi3AkT8Dci6zQ/5aTRo7F9715s7TleV1+PDdu3Y9akSQm449RDsst2vAywvrhH7s2SQoym9eV4PpeXjz4v1NmJTXv34rwJE8JtDAoGceaECXh/z57wdWlfpo4ejY/37sWOPXsQAHCgvh7vb9+OGUQvkmUuSEXYnK0kRSNaUYM6HEc7CpGFmSjFCOSiuT0LAHD42C78198ex96jHwEA1u37M+aN+ruwtX/4WBNK8kdHtFmcl4fm48dxvKMDGYbclFRQpljzuuJ57XSH11ffdVItL6O/jWLftJy/vEG9HdNLD6A4rwX7mvMxquAoTim7GOv2/hkfHXoDH3XsxpHWUVBZ2xBSIYzIz484d3heHrYfOiQSuwUzZ6L+2DHc+vDD3bkuoRCq5szBZfPnRy0ArO9nICLWt4UThUT8Dl7z1dxAjWJTagZYueTVCsA5rNjYmoPNB0rQ3JGF/Ox2zCg5gKG5bVAhoAGteA91aEQ7jh3rRJdSKMnPj0icL87LQ+2hQ2JO2fkzZ6Lx2DH88OGHAXTrxXlz5uCi+fOjXi6hsEny3mDJVpKhCyG8hm14EzU4gpbwQFCKPCxCJY4dzwEArN75XwCArIxcnDPmi5hTfmn3+aFuFQoEnJ3z6aAc0ltjib5GOiJeEw6HJlB6W3/z5Uf4G1H6U5jbhs9W1uDp92dgTxNQmj8ZQ/PH40hzLVbsWo3BRV/AiMq/ApuAN7ELMzESCsHwxMDfuNLX21Bbi6feeAPfuPhiTBg1Cp/W1+ORF15A4Wuv4ZLzzovor9/7TVf0l4GTrPrnlN9qqktDcfytREqypHwuhAL4y7ZJeHFHzxIoAbIEyqRtUJV/xcuB7TiAFigALegAALyLXTgFIxFEMCqNhZPCzbW1ePaNN3DdxRdj/KhR2Ftfj8dfeAF/fe01LDrvvD41xNNRlyzZSjCcwiUcIQCvYxuexyYMQTYmoRiDEIRCCHtaG/C9N1bg4zVP99QO4NTyi/CZcV9FfvawcBv7mvNRnNeCUQW5ONCTIKwnm0MtLcjPzkb2oEFo99HfVEEsiyu6nW/hDZw0UTkKsQ8nQfS/Cnndz07eDgBYUVOJVw/lIpT/OaD5lzh2eBUuOKcMYypr8d6fgeebt2IiilGFqVAADra0YER+fsSf8eprPvLKK7hg1ix89rTT0AlgZGkpjrW347/+7/9w0bnnQgWD4XuicHrz1oR0k6lEGzj98bwSZWyYUjOk3F3+glQGK3t+2yQ8+f4MDMk5jmklB5GR0bMEytF8/Nt746FCQzF1ShemoRhBBNE6uBMrAjV4oXkrJqAYF2AqggAOt7SgOD9fDGk+8cormD9rFi4getHW3o7f/9//YeG55wLBE09JeiNRgvV6nUAyeIfTCibBohMNEB1CUegOHb6OGuQhG2UoQBBBdHUpvLd2H/70y4+w7e3D6OzqvsKssu/ilLK7kTtoOACgsyuATxqH4Gh7NhZW1uDssSPxam1thHfhjR07MHvUqIjr8z7w/gKpT8C8fizcwWUGiJYPXs6ToTXBojrRJXw6AaigwsKp2/CPC57H8Lm/w6xz2pCRmYnjbXuQm/cSsgYFUVYxBA21rViJGhxGKzpCIbz18ceYNWpUhGzrdts6OoBAIELmg0F5KJTuMZX1Id7wq1PprINOLx3xZHleRkkPr9fQmoOXepZAKR9Sj0CwEwgAGRkKwwuP4FBWHQ5vPx/D2yqQ2dNqdmYmyiqGoLG2FatRg0a0IhQK4Z0evZD6eryjA8FAIOLFqIBBL0z3a2GG9WzFAJ7P4jb4OuWw0IlrG+pQjxZUohgKwMfbDuP1lz9G/aFjAICComyMnVeE91+ow6nle9F4/CPsPVqKvEHleP/AL6DwKZZ/9u9x4eTtmFI6Bw+tXYs7X34ZV82ejddqa/HXDz7AQ0uXhtdjkZKVJdIl3Y8XSM8l1QfUgQg/+Vva0qVeqiAiZY0SLCl5mC8gqgB8krsTGePW4AwUo75mODZvrMPGv+1D+ZhCnDZvFF549iPkVAzCSyO34t13PkFrRwcunT0bXQD++ZlnMLygAP9QVQUFYO7kyXimuhrjysrCYcSnV6/GKVOmIBAMioaR07OxMCNV9N3k3fIq+xxevRj8Bagg2w6i+6Wog8fyMG3EQby283/wxs7/QWYwC4MychDMyEZHMBPBUCH+e89RDBnSiqysDJx59micMW8U/tKjFytHbsWaHr24fHb3G+339OjFV6qqAACnT56MP1VXY2xZWTiM+Nzq1Zg5ZQqCPXphETss2fIBSfF0GU0U5lZ8ENETEPcohQC09AT3MhCEAvD+3/ah/tAx5OZmYvrMEqxfsxfvv1AHAHhkw2MAHsOFE8/Gl2d/A79eswVH2/di8dRtCAEYPXQonvziF/H9l17Cb9esQVlBAX5y6aU4p7ISHawf/BVsKVxiJxULaUKihCQg1OWhQU2wdOgwhBOL6+ptqd3GHt0IIIiZp5Zj88Y6fLT5IM5fVInJJ5egpaUD1a/sxG3Nf8b0snI89KUvoSg/H50A9jU2IhQIhK+/ZP58hAIBPL56NQ4fPYqCwYNxypQp+PwFF0TJvpOn2sKiL0CXQGnvagUAdIba0RlqR09qFoB92L8f2L+/e2/GKaWY3qMXb72yE99p/jNOKivHf37pSyjOz8dxAHWNjUDghNZ+Yf58qEAAT61ejfqjRzFk8GDMmjIFl19wgdgvL3OC1ZMTCCil0nIebWpqQmFhIe68/Xbk5uS41ndajkGq57aOCn21lr9ZwtdK0WupvI+d+CPWYGJPrtbRw634YP1enHnuWAzOzUQIIWzHIXwDczEf40Tipye1TvLdiW6d7ATQ3rPdQcrbyb7+dLFtU+iHTopSMjSHVb7khWSNmyxvaU04ujYcXxVbWh2byr/pT9f19xrsxMNYg0koRlAF8MbKWoyfPAwVYwqBQACdPbrxJczFaRgXJff6045I2aZ6QvO7rKwPPJi8UU7eJy77VAdM8s4XKM0iH7qv61fvHINfr5mL6SUHoQLH0d7VivZQK9q7WrG78yA2te9F6EgJJk54HYWFtWhv78KkqcUYUpSDDoSwBYfwlR69aAciPnT8p3OCk17QsLxkoHsxVtxIR2tbG/5l+XI0NjaioKDApXZqwHq2XOCUPOlm6dOwiq6vz5FCKuNRiiLk4SCaUY4CFA7PxTkXTgzX349mDEcepqI07A3g/dFtUaJlUpguRCoP9W7xcCKHKYclLZm7RQSoPOuJh3p49b5OoNVhQSkZ2MnIUTjh/apEKYYiD/vQjPJAAc7+7Il1tbRuDEUeJqAU7YieKDTBCiFSFxTblvLRrHwPDLglylPvrbRtinxwci6V0WO87KTSAxiR14J9R/MxqkghM5iFwYFCAMAQNRo7GiYBpZ0488y3kZNbHmEk7EczinvmDD7nAJFyT6+rj5lgDQn/8J0g//rrr+OSSy5BRUUFAoEAnnvuuYjjSincddddKC8vR25uLqqqqrB9+/aIOvX19Vi6dCkKCgpQVFSE6667Ds3srzU2bdqEc889Fzk5ORg9ejTuu+8+/3fXT6CCKAk1DanQTz5yMQ+VaEY79qMJHQghBKADIXyKJjShHeejEvnIDVsk1GKn1gknWSYvFbVQ6MTDw4xuOSxelM8qaOrCjXhz+eAhaiA6nMiJkCTLenswcnEOKnEU7fgUTWjv0Y12hLAXTWhGO85CJfKQG9GWpAta3nkf+cTjFkK0xMvCCVw+JHmSZI8bA0W5bVhUWYOjx7Oxp2EIuroCgAK6ugI43DgUxcdLMbzyVRzO3YvOnqt0IYQ9aMLRnjljCHKjjGovqSKSHlvEBt9kq6WlBbNmzcKvf/1r8fh9992HBx54AA899BDWrFmDvLw8LFy4EG1tbeE6S5cuxQcffIAVK1bgL3/5C15//XXccMMN4eNNTU248MILMXbsWKxfvx73338/fvjDH+K3v/1tDLfoDX4FiVu7Thax08TDyddcTMZCzEAQGajBIWzFAWzHIWQgA5dhBs7H5DDJ0pMRJV20nLqEOeGSvF3aAyGRLLcJyOn5WKQGTJOBWx1aV5o4QoiWM06wpLAfD3Ocg8m4GDOQIejGIszAPExGR0/bktEREj4mr5ZEHJ3um8MaFqmJWI1Gr/IhGbGScUtJVxeAhZO34+oZ72NQMIQtB0dg8/4R2HJgBAYFQ7hlRi1unnwEmcjAFhzCZhzAFhxCJjJwJWbgAkyOaFOSdWXog9v9mJ6HEwbq3NCrnK1AIIBnn30Wl19+OYBur1ZFRQVuueUWfOc73wEANDY2orS0FI8++iiWLFmCLVu2YPr06Xj33XcxZ84cAMCLL76Iiy66CHv27EFFRQUefPBBfP/738f+/fuRldW9Yvrtt9+O5557Dh999JGnvvnN2QJiy9vSx3kei7Q4nY7jS6sD8xyWY2hFbc8K8kOQhWkoxVDkRuUHUHDipr8pweKTECdipjwWp4EBsDks6QKveVt6X5J5nrtFZZ/qgLRPz5PeVASAJrRiG+rQgnbkIguVKEUecqNChNyo4N4tTgB5iN3J4HALLVpZT13EmrsojfnSX+nQ/zTkuVr8T6WzEKkjja05+KCuBC3tWRiS1Y6TS7tXkO8CUI9WbEIdmtCOHGRhCkoxuMfT20E+xxHpOabHuVEeXoIFkfrBCSPgPRTphXDYnC0X1NbWYv/+/ajqeZUUAAoLCzF37lxUV1djyZIlqK6uRlFRUZhoAUBVVRWCwSDWrFmDK664AtXV1Zg/f36YaAHAwoUL8dOf/hRHjhzB0KFD49ltV7jF8p3OoQIZIOWShU1zWAYjF7MwLmJS6mDtgNSXQjWUMFESJV2bJwTTbQjfEpyO2cknNeAk6wqRMmw6l+ducZmlf4mTiRODuSY5GaQtaf2hHORiBsaF+6TQrRs8BEPztLwQLW7VS14tLseWaFlohCD/dZX+8PmAG7D6Td0MnHhDtxOROjIktw1njdsdYWhr2R6CXMwjL4eYXnaS+sC9WyBlIMcsYkdcydb+nvdOS0tLI8pLS0vDx/bv34+SkpLITmRmYtiwYRF1xo8fH9WGPiaRrePHj+P48ePh/aampvC2niTiBd0ebZeSHb6mFp94NLHS207X0edkIFIhOxFpZVEFodY5J1x635TLwhWQ90OClwnIInVh0h9J5kOI1AmTfFPCpVePVzgxIWnSRT0HQPSK1ZLHVXqr0FQmea9M1ji34CXYCSn14cXg0Nu6vmnZH2ndOT2GaxnX+9yDS3WEvzDC13nkhoYUqTDl7tK5whRS5M+Hl3v1ag1kpM3biPfeey/uvvvuXrXhRMqcFNDN0ueDuLRwo+ktFj0BacWQJiB6DlUEPtFwsmXyaHkhXSZlk2CVLrXg17ulhH0g0kslgXq0goiUdf3NJyCpr1TeJbnnJIzKMCVcEumSJhv6LCwsAJmQUFImjaGUeNHw8gUslgAAFwdJREFUuyZZAVKPG9ucbFF5lsZ5Trqk0DntH78fU2TD79juRWfSVa/i+nc9ZWVlAIC6urqI8rq6uvCxsrIyHDhwIOJ4Z2cn6uvrI+pIbdBrcNxxxx1obGwMfz755JPe35AHcMIhDdKSu5h/S28O8vwqGndvR2Tsna+XIpVJruUQIpXQNNlYr9bAgtPv6kayJQtZ8jpxa5vKPJddvj5Qu3Bc0hk3b5dEuLiRQZ+HNSoGBtzGNafx0OR55WFsbgRLukBf9pDWxZL2O4VtPtbTkCInX9IcID0DC3+IK9kaP348ysrKsGrVqnBZU1MT1qxZg3nz5gEA5s2bh4aGBqxfvz5cZ/Xq1QiFQpg7d264zuuvv46OjvDyuFixYgWmTJlizNfKzs5GQUFBxCcWeB1M3ZQvRL4l8sLfjuIuXuk1eEnBjpOPdNz0GjzP35IsHa/Wvp2ABgZMMu8kJ5xscU+rFPbgb9BK8m/6OOWquBEtU/jQbZs+C4v0h8nA5mVcD/R5kk6YxmiewE6T3LnBbUp8dxr7uVeL95ffnw0hxg7fYcTm5mbU1NSE92tra7Fx40YMGzYMY8aMwU033YQf/ehHmDRpEsaPH48777wTFRUV4TcWp02bhkWLFuH666/HQw89hI6ODixbtgxLlixBRUUFAOCLX/wi7r77blx33XW47bbbsHnzZvz7v/87fv7zn8fnruMEHVKRQit8MVMgMnxI3cISeFiF52pJYUTdJx4i6WJl1JoyhVe4sjlZ+/TacDhukVrobTiRhgOl82muFpVl6U0vIDp0QrclbxrXAU6unHK26IRivbcDE1z+uZxKx+m2DiXqbZ2rC7LtNIbT8CFPnDeFEbVcm/J0uffMj1erL0KI6QzfZGvdunX4zGc+E96/+eabAQDXXHMNHn30Udx6661oaWnBDTfcgIaGBpxzzjl48cUXkUOWX3j88cexbNkyLFiwAMFgEFdeeSUeeOCB8PHCwkK8/PLLuPHGG3HaaaehuLgYd911V8RaXH7BFSXWuk4TED2f19WKp493sfpameg+VTIa0wfkPBbJ08BJFveqmcop6ZLuz0tYxSL14TThcMJFJxYnwkXb0OcEhfaASONFApdRTrK4d40f80q0rPfWwgladig5ojLF/1UBcP8nBUrSaGK8qS4d56kXlxMtUwjTq1eLXxesvDf6kM7zyYD4b0RN9Py+kdjbdbd0mWkdIv7N/1tR+vDX4Cm4FcKtHU3wnAgWD/94nYSsVyu9YZJ5um2Se77N1ySS6nBZN72AAshhEMnDa5pI/BAtK+cDD71dZ1F/+FpydJ0509pzdN05ydsLyGM9NaRNnq0QIskWDbdLxgnXFX1t+g3IOuGVZOh6bXadrdSGH++WE0zWvmTp0/raQgGiLRxdL4N9Sws7msiW7ov+psqi23f7WKJlIcEtpMLLTR4uDR0W1/X5tmTFO3ly9bdJbp1IFgz7tG2+TetYpDe8RDMAs3eLe7Zou8CJtw+1fNHzJK+Wk2dL8m5RwsVTSHh0w6Q3XD9of+Pl1Up3DCiy5RdO5MykgFIohIcP6cJ3XaQe/VY4kdPiFlYxrT2k++lk6VOrRVIs7jruK6KVlu7WfkI8DAzAm5EBmEOK2sAIsvMo0aLng5VJsuWFcIHsux2X2uTbtE5vYeW8bxAvHQDcjWsqR6bUEQ09AdNlUHguoyZKNIWEe7Yk7xYlXjw3iy8DEUI04ZLChvq+/MCvVytdMeDIll/vllfCRevxbV0XOOHhoi5mOrhry4gqM52g6LmmPum26LVN3iov+Su0b/SeTNf2inRXrmSB23P2ow9OhEu3RScfTrhoG3zS4ut0uREt2h49T5JdkzxzoyERRMvKef/D9Bt4lX03Q4Neg88LQKSHikPPA5zoaENcejlK6h8/n7bDiZW0z41vaU6g9w9W1pvw4UDAgCNbsSBWwkUnFSDa4xUi5ZKV7xY+7E0eC9+OhWi5KZoEq3zJB/qbeJl8nEKK3Nqn54CUUaNBImy0LvUQOPVdIk+S98rknY2n19bKeWrAj+w7hRP5PKD3pfHerT9UJ6RcLadcXZN3ixIs6vHipEyxjyncTq/r9d6cMBD0ZUCSLb/eLX0ODOe5ES5AtuzptmTl020aswfM1r6JbOltJ5IFh33aJt92U7SBoEjpAq+Tjx/CJckKJ120jHsKTP0xyaPTxJBIb5aV89SG0xivIY31XN6l5HUn76wmWBmsLh/3Tfm6ToY1j2qYCJaUs8XDiDZ82DsMSLIFxEa4nM5zCymaQiuK1KHnx0K0aF90O/qbEy4++Uh1TG1J15IwUJQoXeE2+XDiROtz+TElt1OjghMvTvy4PJlk0SvxcjuH1zHBynl6wYvcuxEuXo+SJ8n7qr95ni6Pbpj6ZTKqeXjQy3pzIUTKvGRwu5EwS7SiMWDJFpB4wgWYFVHXB6KtfLfQSoiV8b7x9qmCOJEqN4vfEq2BCT+Tj64vGRuAmXTpY5R4abjpqJvH1ZIsi1jgND/0hnAB0W+ja++WbkOHELneuOXq6m3upaKkinu7pONgbeh9+s2vC+G4xQkMaLIF9I5wQTjXNPE4TTrxCq2YLH+JPHktk9q2JGtgwol0efFyOZEu2gY/7lWmuDUubUuyLe1bGbcA3GXeD+GiEQu6BBBd6iFIPnRu4i9ISX3U2yayJYUYTXVoW2Bl9P45/OjGQNOjAU+2gNgJl+lcaeIxvbFlOo9a+LR9fo4T2ZImHzevVaxEa6ApzkBGokgX1aUuUtdPn6R9EwmT9i3JspDgNZrhRLiASIPatAQKTTnhZEvDNO6bPFPSdjyJliVZ7rBkqwduoZJYznUKK+rjgJl06WN+JiEvk45EpGINqwxUxbHwbvXzuk6DNtcFN0PIJH9uk4ElWRZ+4XWcd0sdkQiYRLpA9nXeLkg57xfdloiTiWTBsA/WBtgx6fpuGMi6ZMkWQ7xJl5ulL1n5nFxJni0vQhtrWMXrRDSQFcfiBJwmIQ3TGlpUhqguUJjyVPg1pD65lVnZtvALUzTDD+ECTpAsGmak5+k6lGh5MTxMhMvk9aL7QGKIltUnS7aM4JOA33O9hBY5TDkrFF7CK24TjRcSxuu5tW9hEUt4kdaX5MpEwKTrej1mvVgWvYXJsDaljlBCxesrRM4ZNGRo0inT2l18PHfydEl1AG8kix93gtWpbliy5QGxeLvcrH2vOS20rtS+F8QrpGKVxsILnAwVJ28XhRMB83JdDvtmoUUiwA1rnncLRC73o49pSG+gO70gxa8j9YfXMZEs+m29WYlH2pItpbp/6uPHjyekfb/eLlN9p4T3WK8lwUtYJd4kyypb+qC3MujlfKdwYSxINMGy8p2+iEXeTd4nfsxEpHh5rP1x8ua6kSy+HQvRiode6Hlbz+PpgIBKp7sh+PjjjzFx4sT+7oaFhYWFhYVFDPjkk08watSo/u5GXJC2nq1hw4YBAHbv3o3CwsJ+7k3fo6mpCaNHj8Ynn3yCgoKC/u5Ov8A+A/sMBvr9A/YZDPT7B1LvGSilcPToUVRUVPR3V+KGtCVbwWC3M7awsDAlhCtRKCgoGND3D9hnANhnMNDvH7DPYKDfP5BazyDdnCTxTpOwsLCwsLCwsLAgsGTLwsLCwsLCwiKBSFuylZ2djR/84AfIzs7u7670Cwb6/QP2GQD2GQz0+wfsMxjo9w/YZ5AMSNu3ES0sLCwsLCwskgFp69mysLCwsLCwsEgGWLJlYWFhYWFhYZFAWLJlYWFhYWFhYZFAWLJlYWFhYWFhYZFApCXZ+vWvf41x48YhJycHc+fOxdq1a/u7S3HBvffei9NPPx1DhgxBSUkJLr/8cmzdujWizvnnn49AIBDx+frXvx5RZ/fu3bj44osxePBglJSU4Lvf/S46Ozv78lZixg9/+MOo+5s6dWr4eFtbG2688UYMHz4c+fn5uPLKK1FXVxfRRirfPwCMGzcu6hkEAgHceOONANJPBl5//XVccsklqKioQCAQwHPPPRdxXCmFu+66C+Xl5cjNzUVVVRW2b98eUae+vh5Lly5FQUEBioqKcN1116G5uTmizqZNm3DuueciJycHo0ePxn333ZfoW/MMp2fQ0dGB2267DTNmzEBeXh4qKirw5S9/GXv37o1oQ5Kb5cuXR9RJ1mfgJgNf+cpXou5t0aJFEXXSWQYAiGNCIBDA/fffH66TyjKQ8lBphqeeekplZWWphx9+WH3wwQfq+uuvV0VFRaqurq6/u9ZrLFy4UD3yyCNq8+bNauPGjeqiiy5SY8aMUc3NzeE65513nrr++uvVvn37wp/Gxsbw8c7OTnXyySerqqoqtWHDBvX888+r4uJidccdd/THLfnGD37wA3XSSSdF3N/BgwfDx7/+9a+r0aNHq1WrVql169apM888U5111lnh46l+/0opdeDAgYj7X7FihQKgXnnlFaVU+snA888/r77//e+rZ555RgFQzz77bMTx5cuXq8LCQvXcc8+p9957T1166aVq/PjxqrW1NVxn0aJFatasWeqdd95Rb7zxhqqsrFRXX311+HhjY6MqLS1VS5cuVZs3b1ZPPvmkys3NVb/5zW/66jYd4fQMGhoaVFVVlfrDH/6gPvroI1VdXa3OOOMMddppp0W0MXbsWHXPPfdEyAUdO5L5GbjJwDXXXKMWLVoUcW/19fURddJZBpRSEfe+b98+9fDDD6tAIKB27NgRrpPKMpDqSDuydcYZZ6gbb7wxvN/V1aUqKirUvffe24+9SgwOHDigAKjXXnstXHbeeeepb3/728Zznn/+eRUMBtX+/fvDZQ8++KAqKChQx48fT2R344If/OAHatasWeKxhoYGNWjQIPXHP/4xXLZlyxYFQFVXVyulUv/+JXz7299WEydOVKFQSCmV3jLAJ5lQKKTKysrU/fffHy5raGhQ2dnZ6sknn1RKKfXhhx8qAOrdd98N13nhhRdUIBBQn376qVJKqf/4j/9QQ4cOjbj/2267TU2ZMiXBd+Qf0kTLsXbtWgVA7dq1K1w2duxY9fOf/9x4Tqo8AxPZuuyyy4znDEQZuOyyy9QFF1wQUZYuMpCKSKswYnt7O9avX4+qqqpwWTAYRFVVFaqrq/uxZ4lBY2MjgBN/uq3x+OOPo7i4GCeffDLuuOMOHDt2LHysuroaM2bMQGlpabhs4cKFaGpqwgcffNA3He8ltm/fjoqKCkyYMAFLly7F7t27AQDr169HR0dHxO8/depUjBkzJvz7p8P9U7S3t+Oxxx7DV7/6VQQCgXB5usuARm1tLfbv3x/xmxcWFmLu3LkRv3lRURHmzJkTrlNVVYVgMIg1a9aE68yfPx9ZWVnhOgsXLsTWrVtx5MiRPrqb+KGxsRGBQABFRUUR5cuXL8fw4cMxe/Zs3H///RGh41R/Bq+++ipKSkowZcoUfOMb38Dhw4fDxwaaDNTV1eGvf/0rrrvuuqhj6SwDyYy0+iPqQ4cOoaurK2ISAYDS0lJ89NFH/dSrxCAUCuGmm27C2WefjZNPPjlc/sUvfhFjx45FRUUFNm3ahNtuuw1bt27FM888AwDYv3+/+Hz0sWTH3Llz8eijj2LKlCnYt28f7r77bpx77rnYvHkz9u/fj6ysrKgJprS0NHxvqX7/HM899xwaGhrwla98JVyW7jJAofsr3Q/9zUtKSiKOZ2ZmYtiwYRF1xo8fH9WGPjZ06NCE9D8RaGtrw2233Yarr7464k+Hv/Wtb+HUU0/FsGHD8Pbbb+OOO+7Avn378LOf/QxAaj+DRYsW4fOf/zzGjx+PHTt24Hvf+x4WL16M6upqZGRkDDgZ+P3vf48hQ4bg85//fER5OstAsiOtyNZAwo033ojNmzfjzTffjCi/4YYbwtszZsxAeXk5FixYgB07dmDixIl93c24Y/HixeHtmTNnYu7cuRg7diyefvpp5Obm9mPP+ge/+93vsHjxYlRUVITL0l0GLMzo6OjAF77wBSil8OCDD0Ycu/nmm8PbM2fORFZWFr72ta/h3nvvTfm/cVmyZEl4e8aMGZg5cyYmTpyIV199FQsWLOjHnvUPHn74YSxduhQ5OTkR5eksA8mOtAojFhcXIyMjI+rts7q6OpSVlfVTr+KPZcuW4S9/+QteeeUVjBo1yrHu3LlzAQA1NTUAgLKyMvH56GOphqKiIkyePBk1NTUoKytDe3s7GhoaIurQ3z+d7n/Xrl1YuXIl/uEf/sGxXjrLgO6vk86XlZXhwIEDEcc7OztRX1+fVnKhidauXbuwYsWKCK+WhLlz56KzsxM7d+4EkB7PQGPChAkoLi6OkPmBIAMA8MYbb2Dr1q2u4wKQ3jKQbEgrspWVlYXTTjsNq1atCpeFQiGsWrUK8+bN68eexQdKKSxbtgzPPvssVq9eHeXulbBx40YAQHl5OQBg3rx5eP/99yMGHj0wT58+PSH9TiSam5uxY8cOlJeX47TTTsOgQYMifv+tW7di9+7d4d8/ne7/kUceQUlJCS6++GLHeuksA+PHj0dZWVnEb97U1IQ1a9ZE/OYNDQ1Yv359uM7q1asRCoXCRHTevHl4/fXX0dHREa6zYsUKTJkyJSVCJ5pobd++HStXrsTw4cNdz9m4cSOCwWA4vJbqz4Biz549OHz4cITMp7sMaPzud7/DaaedhlmzZrnWTWcZSDr0d4Z+vPHUU0+p7Oxs9eijj6oPP/xQ3XDDDaqoqCjizatUxTe+8Q1VWFioXn311YhXd48dO6aUUqqmpkbdc889at26daq2tlb96U9/UhMmTFDz588Pt6Ff+7/wwgvVxo0b1YsvvqhGjBiRtK/9c9xyyy3q1VdfVbW1teqtt95SVVVVqri4WB04cEAp1b30w5gxY9Tq1avVunXr1Lx589S8efPC56f6/Wt0dXWpMWPGqNtuuy2iPB1l4OjRo2rDhg1qw4YNCoD62c9+pjZs2BB+02758uWqqKhI/elPf1KbNm1Sl112mbj0w+zZs9WaNWvUm2++qSZNmhTx2n9DQ4MqLS1Vf//3f682b96snnrqKTV48OCkeeXd6Rm0t7erSy+9VI0aNUpt3LgxYmzQb5W9/fbb6uc//7nauHGj2rFjh3rsscfUiBEj1Je//OXwNZL5GTjd/9GjR9V3vvMdVV1drWpra9XKlSvVqaeeqiZNmqTa2trCbaSzDGg0NjaqwYMHqwcffDDq/FSXgVRH2pEtpZT65S9/qcaMGaOysrLUGWecod55553+7lJcAED8PPLII0oppXbv3q3mz5+vhg0bprKzs1VlZaX67ne/G7HGklJK7dy5Uy1evFjl5uaq4uJidcstt6iOjo5+uCP/uOqqq1R5ebnKyspSI0eOVFdddZWqqakJH29tbVXf/OY31dChQ9XgwYPVFVdcofbt2xfRRirfv8ZLL72kAKitW7dGlKejDLzyyiui3F9zzTVKqe7lH+68805VWlqqsrOz1YIFC6Key+HDh9XVV1+t8vPzVUFBgbr22mvV0aNHI+q899576pxzzlHZ2dlq5MiRavny5X11i65wega1tbXGsUGvvbZ+/Xo1d+5cVVhYqHJyctS0adPUT37ykwgyolTyPgOn+z927Ji68MIL1YgRI9SgQYPU2LFj1fXXXx9lYKezDGj85je/Ubm5uaqhoSHq/FSXgVRHQCmlEuo6s7CwsLCwsLAYwEirnC0LCwsLCwsLi2SDJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgmEJVsWFhYWFhYWFgnE/wdhx1PfyijVQgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_heatmap(df, target_resX, target_resY):\n", "    # Generate heatmap for all gaze points\n", "    heatmap = makeHeat([target_resX, target_resY], df['user_pred_px_x'].values, df['user_pred_px_y'].values)\n", "    \n", "    # Calculate median gaze predictions and ground truth per dot\n", "    median_pred = df.groupby('unique_dot')[['user_pred_px_x', 'user_pred_px_y']].median()\n", "    median_pred.columns = ['median_pred_x', 'median_pred_y']\n", "    \n", "    true_pos = df.groupby('unique_dot')[['accprecX', 'accprecY']].mean()\n", "    \n", "    # Calculate offset between median gaze predictions and ground truth, then convert to cm\n", "    offset_px = np.linalg.norm(median_pred.values - true_pos.values, axis=1)\n", "    # new scaling factor is used to convert px to cm for scaled data\n", "    offset_cm = offset_px * df['scale_cm_in_px'].iloc[0]\n", "    \n", "    # Calculate Euclidean distance from each gaze point to the median prediction for each dot\n", "    df = df.merge(median_pred, on='unique_dot', how='left')\n", "\n", "    df['eucl_dist_gaze_to_median_px'] = np.linalg.norm(\n", "        df[['user_pred_px_x', 'user_pred_px_y']].values - df[['median_pred_x', 'median_pred_y']].values,\n", "        axis=1\n", "    )    \n", "    \n", "    # Calculate the mean Euclidean distance per dot and convert to cm for standard deviation (SD)\n", "    SD_px = df.groupby('unique_dot')['eucl_dist_gaze_to_median_px'].mean()\n", "    # new scaling factor is used to convert px to cm for scaled data\n", "    SD_cm = SD_px * df['scale_cm_in_px'].iloc[0]\n", "\n", "    # Plot heatmap and ground truth vs. predicted gaze points\n", "    plt.figure()\n", "    plt.imshow(heatmap, cmap=cm.hot, extent=[0, target_resX, target_resY, 0], alpha=0.5, aspect='equal')\n", "    \n", "    # Plot ground truth positions (green) and median predicted gaze (blue)\n", "    plt.scatter(true_pos['accprecX'], true_pos['accprecY'], c='g', s=40, alpha=0.5, label='Ground Truth')\n", "    plt.scatter(median_pred['median_pred_x'], median_pred['median_pred_y'], c='b', s=40, alpha=0.5, label='Predicted Gaze')\n", "    \n", "    # Draw lines between ground truth and predictions for visualizing offsets\n", "    plt.plot([median_pred['median_pred_x'], true_pos['accprecX']], [median_pred['median_pred_y'], true_pos['accprecY']], c='black')\n", "            \n", "    # Title and statistics in the legend\n", "    plt.title(f'N={df[\"pp_id\"].nunique()}\\n Offset: {offset_cm.mean():.1f}cm | SD: {SD_cm.mean():.1f}cm', fontsize=12)\n", "    \n", "    # Annotate offset values near each ground truth dot position\n", "    for (x, y, e) in zip(true_pos['accprecX'], true_pos['accprecY'], offset_cm.round(1)):\n", "        plt.text(x, y, str(e), fontsize=10, color='black')\n", "    \n", "    # Add legend and final touches\n", "    plt.suptitle('Calibration (All Participants)', fontsize=14)\n", "    plt.subplots_adjust(top=1.0)\n", "    plt.legend(loc='upper right', bbox_to_anchor=(1.1, 1.25))\n", "    \n", "    # Show the plot\n", "    plt.show()\n", "\n", "# Call the function with the dataframe\n", "plot_heatmap(all_df, target_resX, target_resY)\n"]}, {"cell_type": "markdown", "id": "ab7a35da", "metadata": {}, "source": ["## Calculate and plot offsets and SD per condition per subject\n", "\n", "1. Offset - euclidean distance from the median gaze prediction per each dot to the ground truth\n", "2. SD - mean of euclidean distances from each gaze prediction to the median gaze prediction for each dot\n", "3. RMS - sample to sample euclidean distance for each gaze positions for each dot"]}, {"cell_type": "code", "execution_count": 34, "id": "59946001", "metadata": {}, "outputs": [], "source": ["# Prepare summary data across subjects\n", "summary_data = []\n", "\n", "# Calculate metrics for each subject and unique dot\n", "for _, group in all_df.groupby(['pp_id', 'unique_dot']):\n", "    # Compute median gaze for each unique dot\n", "    median_x = group['user_pred_px_x'].median()\n", "    median_y = group['user_pred_px_y'].median()\n", "    \n", "    # Sort by timestamp to ensure temporal order\n", "    group.sort_values('frameNr', inplace=True)    \n", "\n", "    # Compute Euclidean distances\n", "    gaze_coords = group[['user_pred_px_x', 'user_pred_px_y']].to_numpy()\n", "    median_coords = np.array([median_x, median_y])\n", "    \n", "    # Distance from gaze samples to median gaze (precision)\n", "    group['eucl_dist_gaze_to_median_px'] = np.linalg.norm(gaze_coords - median_coords, axis=1)\n", "    group['eucl_dist_gaze_to_median_cm'] = group['eucl_dist_gaze_to_median_px'] * all_df['scale_cm_in_px'].iloc[0]\n", "    \n", "    # Offset from median gaze to ground truth (accuracy)\n", "    ground_truth_coords = group[['accprecX', 'accprecY']].to_numpy()\n", "    group['median_offset_px'] = np.linalg.norm(median_coords - ground_truth_coords, axis=1)\n", "    group['median_offset_cm'] = group['median_offset_px'] * all_df['scale_cm_in_px'].iloc[0]\n", "\n", "    # RMS error sample-to-sample (temproal precision)\n", "    group['rms_error_px'] = compute_rms(gaze_coords)\n", "    group['rms_error_cm'] = group['rms_error_px'] * all_df['scale_cm_in_px'].iloc[0]\n", "    \n", "    summary_data.append(group)\n", "\n", "# Combine data across all subjects and calculate mean precision (SD) and accuracy (offset)\n", "summary_df = pd.concat(summary_data)"]}, {"cell_type": "code", "execution_count": 35, "id": "af724cd0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_measurement_per_participant(df, measurement='median_offset_cm', name='', color='b'):\n", "    # Compact figure for matching inset plot style\n", "    fig, ax = plt.subplots(figsize=(4, 6))  # Smaller size for side-by-side presentation\n", "\n", "    # Calculate measurement per participant and mean value\n", "    measurement_pp = df.groupby('pp_id', as_index=False)[measurement].mean()\n", "    mean_value = measurement_pp[measurement].mean()\n", "\n", "    # Simplified box plot to match inset style\n", "    sns.boxplot(y=measurement_pp[measurement], ax=ax, color=color)\n", "    # sns.rugplot(y=measurement_pp[measurement], ax=ax, height=0.05, color=color)\n", "    # ax.axhline(mean_value, color='k', linestyle='--', linewidth=1.5, label=f'Mean: {mean_value:.2f}')\n", "    \n", "    # Set y-axis limits and label\n", "    ax.set_ylim(-0.5, 5)\n", "    ax.set_ylabel(f'{name}', fontsize=16, labelpad=6)\n", "    ax.tick_params(axis='both', labelsize=10)\n", "    \n", "    # Simplify layout: remove x-axis label and set title within the plot\n", "    ax.set_xlabel('')\n", "    ax.set_title(f'Mean: {mean_value:.2f}', fontsize=18, weight='bold', pad=8)\n", "    \n", "    # Compact layout adjustments\n", "    fig.tight_layout()\n", "    plt.show()\n", "\n", "    # Uncomment the line below to save the plot\n", "    # fig.savefig(f'{name}_summary.jpg', dpi=1000)\n", "\n", "\n", "summary_df_per_pp= summary_df.groupby(['pp_id'])[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()\n", "\n", "plot_measurement_per_participant(summary_df_per_pp, measurement='median_offset_cm', name='Offset (cm)', color='b')\n", "plot_measurement_per_participant(summary_df_per_pp, measurement='eucl_dist_gaze_to_median_cm', name='SD (cm)', color='orange')\n", "plot_measurement_per_participant(summary_df_per_pp, measurement='rms_error_cm', name='RMS Error (cm)', color='g')"]}, {"cell_type": "code", "execution_count": 36, "id": "696946c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Descriptive Stats:\n", "    pp_id  unique_dot\n", "0  MLPP1         716\n"]}], "source": ["# Participant descriptive statistics (gaze samples per participant)\n", "descr_stats = summary_df.groupby(['pp_id'])['unique_dot'].count().reset_index()\n", "print(f'Descriptive Stats:\\n {descr_stats}')"]}, {"cell_type": "code", "execution_count": 37, "id": "48f90a70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean unique dots:\n", "    pp_id  unique_dot\n", "0  MLPP1          13\n"]}], "source": ["# Participant descriptive statistics (gaze samples per participant)\n", "descr_stats2 = summary_df.groupby(['pp_id','unique_dot'])['median_offset_cm'].count().reset_index()\n", "descr_stats3 = descr_stats2.groupby(['pp_id']).unique_dot.count().reset_index()\n", "print(f'Mean unique dots:\\n {descr_stats3}')"]}, {"cell_type": "markdown", "id": "5c1f6fd8", "metadata": {}, "source": ["## Plot offset per dot per subject"]}, {"cell_type": "code", "execution_count": 38, "id": "0c72a5e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Maximum value: 2.2974491723115014\n", "Index of maximum value: 3\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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*****************************/66qsYPnx4/PznP4+DDjoounbtGq1bt4769etHs2bNolOnTvHDH/4whg4dusl1SaZNm5ZxDvv16xcR/wmTfvrTn0aXLl2iUaNGpedx/XO9vunTp29ynYBNHWtj5s2bF7feemsMGDAgOnbsGE2bNo2mTZtGly5d4tBDD42bbropJkyYkLFfRdYr2NQ6Hm+//Xaceuqp0b59+2jYsGG0b98+zjjjjHKPtU5RUVE899xzce2118aAAQNir732ivbt20fjxo2jUaNGsd1220Xv3r3j0ksvjY8++miTr7u8BdGnTZsWxcXFce+990afPn2iVatW0bhx49htt93it7/9bSxevHij70N5Ieghhxyy2XUCPvnkk7j00kvjgAMOiNatW0eDBg2iadOm0alTp9hvv/3iJz/5SfzpT3/a7OsBAOqYNABAHVC/fv10RJR+tWzZMr1gwYIqj3fdddeVGS8i0n379t3kPv369cvYZ+edd65yDRuO1alTp/S7776b3m677TK2rfs6+uij0ytWrNjomG+88Ua6ffv2G91/3dduu+2WnjRpUrljjBo1KqP/WWedlf7jH/+YrlevXsa2yurbt2/GGNddd91G+y9evDjdvHnzjH3OO++8cvtPnTo1ve+++272PUilUulzzjknvWzZss3Wt6mvTdW+OatXr063atWqzHhNmjRJz507N924ceMy7W3btk2vXbt2s2Pef//96YKCgs3W3alTp4x9O3XqlNFv0qRJ5X6mhg8fXmbfF154YZOf3XVfjRs3Tg8dOrTc2lesWJE+5phjKvzeH3bYYRljvPjii+m8vLwKj/HWW29V6FxtqLyfk439PCxdurTcvo899lhpn5NOOqnCNTdv3jw9cuTIco/19ddfZ/Tv27dv+rHHHks3atSo3PNYmc/7+p+bjR2rPCUlJembb7653BoqMkZFPr/lvZbrrrsufdVVV6VTqVS5x6pfv376r3/9a7k1P/fcc5V6b84999z06tWryx2rvN8rY8aMSe+3334bHa9r167p2bNnb/J92NzXqFGjSve94YYbNvo+bPhVr169cl8HAFA3eRIAAKgT2rRpU+b7hQsXRo8ePeL666+Pt99+u0prAFTWxRdfnNH21VdfbXYql4pauHBhDBgwIGP+8PW99NJL5d6hHhHxj3/8I4444oj47rvvNnuszz//PHr37r3Ru+E39Prrr8evf/3rSk2vVF0KCgrizDPPzGh/7bXXMtqmTJkSBxxwQIXuYk2n0zF8+PA46qijavxu8I155ZVXMu7qHjBgQLRp0yb69+9fpn3WrFnxz3/+c5PjXXPNNXHuuedGYWFhtdXYv3//zX6mHnvssfjhD3+4yc/uOitWrIhLL700rrjiioxtN998c7zwwgtVrnXhwoVx+umnR1FRUZXHqAmLFi0qt71Ro0ZVGm/x4sVx2mmnxZgxYyrUf8qUKXHmmWfGypUrq3S86nD22WfHlVdeWes1DB8+PAYPHrzRp6jWrFkTF1xwQbz00ktbfKz7778/fvnLX1a4//HHHx8ffPDBRrdPnTq1UuNtyqhRo+K6666r0NNkAEDyWBMAAKgTDj300HjkkUfKtH333Xdxww03xA033BD16tWL7t27x8EHHxxHHXVU9O/fPxo0aFCtNfTt27fc9rFjx8ZOO+20xeMvXbo0IiJatWoVAwYMiAYNGsSrr74a3377bZl+jz/+eJx55plx9NFHl7Z98803ccYZZ0RJSUmZvl26dInevXtHbm5uvP322/Hll1+WbluyZEmcfPLJMXHixMjN3fR/Fq67CNyoUaM44ogjon379vHdd99t9qJ0denXr1/ccccdZdq++eabmDNnTmy//fYREVFcXBwnnXRSxhQaDRs2jP79+0fbtm3jk08+ybhw+vbbb8c111wTf/jDHyIi4uSTT47vfe97MXny5IygYd0Cxuvb8PvK2PAzve746/736aefLrPt0UcfjSOOOKLcsZ577rm46aabMtrr1asX/fr1i1133TVWr14dn3zySaWm+pgxY0ZERPTs2TN69uwZRUVFZRbp/vrrr+Pcc8/NuLjYunXrOProo6NRo0bxz3/+M7766qsy22+55Zbo27dvDBgwoMzrK6/2XXbZJSL+M5XM5MmT4/PPPy/3YuaLL76Ycf7bt28f/fr1i+bNm0dRUVH8+9//jgkTJlRrULI5//jHP8pt79KlS0Zb586dY5dddolWrVpFq1atIp1Ox6xZs+Kdd94pM5XSmjVr4qqrrqrQz+CcOXMiIiI3NzcOPfTQ6NKlS8ybNy9GjRoV3bp1K13M+Pbbby+zX7NmzeLcc88t09ayZcvNHm9Dd955Zzz00EMZ7Y0aNYpDDz00OnXqFMuWLYsPP/ywwsFkRX3zzTcREbHPPvtEr169Ys6cOfHiiy/GqlWrSvuk0+m44IIL4quvvir370ZeXl5873vfi9atW5dO2VNYWBiffPJJxnRCf/nLX+JXv/pVued2Q3Pnzo2cnJw46qijYscdd4xXX301I1R+8sknY+jQobHddttFxP9bePrVV1+Nzz77rEzfk046KTp06FCmbd335U0ntvfee0fPnj2jUaNGsXjx4vj8889j4sSJWQtFAYAalMWnEAAAKmzChAnpBg0aVHgKhNatW6dvu+22dHFxcbnjVWU6oHQ6nc7Pz8/Y784776zSayqv7j322CM9b9680j7Lly9PH3rooRn9jjjiiDJjDRo0KKPP5ZdfXub1r1mzJn3OOedk9HvwwQfLjLWxaU523nnn9L///e8yfZcuXVrp113Z6YDS6XT6o48+KremiRMnlvZ59NFHM7bn5+enP/744zJj/fnPf87o17hx4zLvezq98alFqsuSJUsypvxp1KhR6Xu6ZMmSdMOGDctsb9asWXr58uXljte9e/dypxOZMmVKRt+pU6emf/3rX2e0lzcdUCqVypj6J51Op4uKitLpdDp9/vnnZ+zzve99L71w4cLSvqtXr04PHDgwo9/+++9fZswNp/266667yn2t8+bNSz/yyCPpm266qUz74MGDy+y/4447Zkz3lE6n08XFxelx48alf/vb35b5DFVGRaYDmjlzZvquu+5KN23aNKNfhw4d0iUlJaV933zzzfSXX3650eMVFRWlDzzwwIxzs/77nE6XP0VPRKRbtWqV/vDDD8v0XbVqVZnpazbcp7wpdzZ3rA1/jy5fvjzdpk2bjH7f//730999913GmB9++GH6hhtuyGivSG0bm9roN7/5TcYxyjsnjz76aJl+06ZNS//rX//a6BQ/6XQ6/ac//SljnPKmuyrv9169evXSL774YmmfBQsWpHfdddeMfk8//XTGeGeddVZGv/Wn/tnQEUccUabvySefXG6/5cuXp1955ZX0+eefv9GxAIC6x5MAAECdsNdee8Vjjz0WZ5xxRixfvnyz/efPnx+XXXZZvP322zFy5MjIyameWRCbNWsWS5YsKdO24fdb4pZbbonWrVuXft+4ceO48847o3v37mX6jR49OlauXBmNGjWKdDodTz31VJntbdu2jZtvvrnM687NzY2bbrophg8fXqbvyJEjy51uZ0P3339/xhMPeXl5FX5tW6JZs2bltq//3pd3t/Vll10We++9d5m2Sy+9NB566KEYP358aduKFSvi1VdfjdNPP72aKt68p59+OmMaq/79+5e+p82aNYsjjzwynnvuudLtS5cujWeffTZOPfXUMvtNmTIl4w7qnJyc+Mc//hG77bZbxrF33XXXcherLc/ZZ59dujD0+po2bRrpdDr+7//+L2Pb7bffHi1atCj9vn79+vG///u/8cwzz5S5y3jcuHExc+bMaNeuXelrXrhwYen2WbNmRUlJScbPb+vWrePHP/5xxnE3/JysXLkyFixYEE2aNCnTnpOTE/vtt1/st99+G3/hVVTRxbKvvfbaMn379OkTEf9ZjPadd96JKVOmxIIFC2LZsmWl03BtOB1XOp2Ojz/+OA455JDNHu9Pf/pT7LvvvmXaqvtpqfK8+eabGYtBN2vWLJ555pnSu9vXt++++2bUuSXatm0bgwcPzjjGhRdemPEz8NJLL5X5HdCpU6fo1KlTpNPp+Oijj2LChAnxzTffRFFRUennuLwnSir6pM2PfvSjMk90tWzZMk455ZSMeqtjyrkNfzYWLVoUy5cvz/jZaNy4cRx55JFx5JFHbvExAYCthxAAAKgzTjzxxNhvv/1i8ODB8dhjj5VOn7MpTz/9dAwfPjzOO++8aqmhvGMWFBRUy9i5ubnlTvXSrVu36NixY5lpgdasWROffvpp9OzZM6ZNm5YxF/usWbOiXr16FTpuReYV32mnnUovUmbDxs71+u/9uHHjMrYfc8wx5e43YMCAMiHAuv1rMwQob3qOdVMBrXPSSSeVCQEi/jOF0IYhwPvvv58x1sEHH5wRHlXFWWedtdFt06ZNK/cC7w9+8IOMvm3atIn9998/3nnnnTLt48aNi+OPPz4iIg477LB48sknS7cNHjw47rnnnthzzz2ja9eusfvuu8f3vve96NWrV8bFy4j/TBu2vrlz58ZOO+0Ue+yxR+y2226x2267xR577BEHHnhg7Lzzzpt/8TVk0KBBcf7555dpmzt3blx55ZXx6KOPlpmqZnPmz5+/2T6NGjXK+MzUlvI+myeccEK5AUBNOOyww6J+/foZ7f37988IAT7++OOMfn/5y19i8ODBpdNiVURFzklElPv7pm3bthltFflbtzmHHXZYmaD0jTfeiDZt2sSee+5Z+rOx5557xkEHHVQmiAYAkkEIAADUKTvuuGPcc889cccdd8SYMWPiX//6V4wZMybGjBmz0Tm+H3jggWoJAebPn1/uXf8bLlpcVW3atImGDRuWu61Dhw4ZawOsu9C04UXYylqwYEGsXbt2k+sC7LXXXlt0jC214Xzy66z/3pf3PnTs2LHc/cprr8iittVl9uzZ8cYbb5Rpa9iwYRx77LFl2o4//vioX79+rFmzprRt3WLCrVq1Km1bN+f7+nbfffdqqbVHjx4b3Vbee96hQ4eN3g2/ufd98ODBMWrUqDIXUefPnx+jR4+O0aNHl7Y1btw4Bg4cGEOGDIn27duXtnfv3j0uueSSMnPbFxcXx6RJk2LSpElljtutW7e44oorKvQUTHXp2rVrXHvttRlPMSxatCgOOuigMmt2VFRFnozq2rXrRn+31LSa/GxWxMZ+B2w4d35E5sX7yy67LP70pz9V+pgVOScR/3nSYEPlLRa94VovVXHuuefGQw89VCaUWb58eYwdO7bMGh+pVCoOOeSQGDx48BatdwIAbF2EAABAndSwYcPo169f9OvXLyL+c6Hv1VdfjUsvvTSmTp1apu/EiROr5ZjrX4Rc3wEHHFAt42dLOp2OZcuWbfKJhubNm9deQeUo773v1KlTrd1NXN0ef/zxjKldmjdvHtdee21G3+bNm2csCPvEE0/EhRdeWON1rjt+bdl1111jwoQJMXjw4Pj73/++0YBrxYoV8dBDD8WoUaPi448/LrNY7dChQ6N3795x++23x3vvvbfRC6iTJ0+Os846K+bMmRO/+c1vqu01rFu4NeI/Cxs3a9Ys2rdvH/vvv39873vfK3efIUOGVCkAiIhyF0jeULZ/fuuiSZMmxZ///Ocq7VuRcxIRZYK8dSr6BFdlNWrUKEaPHh2333573HffffHFF1+U2y+dTsc///nP6Nu3b/zzn/+Mgw46qEbqAQBqlxAAAEiEevXqxdFHHx2tWrWKXr16ldm24bzrVXXHHXdktO2yyy7RuXPnahl/3rx5sWrVqnLv2C1vKop1UzaUdyG8Q4cOcdJJJ1X42JubG7yi85zXhEWLFsXDDz+c0b7h1Elt2rSJb775pkzbt99+W+77s+FTFRHlv481pbypgObMmVPmDvbN7b9+CFBe7VOmTKl6gevZ1Lkv7ymYGTNmRDqdLne/irzvbdu2jbvuuivuvPPOmDJlSkyePDm+/PLL+Oyzz+Lll18uc2f5t99+G/fcc09ceeWVZcY45ZRT4pRTTonFixfHJ598Ep9//nl8+eWXMW7cuHjzzTfLXKS98cYb45JLLqm2+fGHDh1a6X2eeeaZjLazzz47LrvssujSpUvp1EdXXnll/M///E+lx8/mz29NfjYrorzPXMSmf6dGRDz77LMZF/O7du0at99+exxwwAHRokWLSKVS8fnnn9fqkw1bolGjRnHFFVfEFVdcETNmzIiJEyfGF198EZ9//nm8+eabZdYVWb16ddx4443xyiuvZLFiAKC6CAEAgDrh7rvvjpNOOim23377TfYr74LT+ncJV9Wtt94ab731Vkb7+nf9bqm1a9fG66+/njGP/WeffZZxIat+/frRrVu3iPjPHfFt2rQpc9d0YWFh3HzzzdG4cePNHre4uLjG7j7dUmvXro2zzz47YxqmVCoVF198cZm2/fffPyMEePHFF6Nnz54Z47744osZbfvvv3+Z78t7Tza8e78qpk6dGh988MEWjfHuu+/G119/XbpQ84bBV0TE22+/HZMnTy79nNSEzp07Z3z2li5dGm+//XbGugDz588vd92GDd/3dVKpVOyxxx6xxx57lLYtXrw4OnToEMuWLSttK2/O+XWaN28effv2jb59+5a2XX311WUWXi0qKorJkydv9C792rDh5zY/Pz/uu+++jAWRN/Vaq0NOTk6ZJyeq4/Ne3mfz6aefjj/+8Y/VNpXaprzxxhuxZs2ajHUByru4vf5nYMNzEvGfJzb69+9fpq2mz8nGbOnvpw4dOkSHDh1KFyZOp9PRp0+fePvtt0v7ZOu1AQDVL2fzXQAAsu/uu++OnXbaKc4777x48803y73YsWbNmrjxxhsz2su7CFxRc+bMiQsvvDAuv/zyjG1du3bNWNxzS/3mN7+JBQsWlH6/cuXKjIvdERH9+vUrvcCfSqXihBNOKLN96dKlccopp8TChQvLPc78+fPjwQcfjL59+8YjjzxSja+g+kyYMCGOOOKIePbZZzO2nXHGGRnrFJx44okZ/W677baYMGFCmbbbb789Y1Hgxo0bx5FHHlmmLS8vL2O8yZMnV7j+jamu93v9pwn22GOPjEWAS0pK4oQTTojPP/88Y9/p06fHb3/72y2uIZVKxY9+9KOM9osvvjgWL15c+v2aNWviwgsvjNWrV5fpt//++0e7du1Kv7/sssti+PDhG50GaPHixRmL5q7//fvvvx8XX3xxvP/++xudBqi8OeorsxBvTdjwKYSioqIy62Ck0+m47bbbYtSoUTVax4af+Tlz5mz0d0hF9evXL2Oh2aVLl8bxxx8fs2bNyuj/6aefxk033bRFx1zfrFmz4uqrry7T9vHHH8fdd9+d0XfdBfGI8p+O+uSTT8p8P2nSpHL/NtSGyv5++utf/xo333zzRqcBWrVqVca6Otn+uQAAqo8nAQCAOmPFihVx//33x/333x95eXmx3377RceOHaNZs2Yxb968ePPNN8td3PW0006r0PhffvllXHrppZFOp6OwsDCmTp0a48aNi7Vr12b0bdasWfzf//1ftS+2+dlnn8Vuu+0WxxxzTDRo0CBeffXVcu9I3fAJhN/97nfx8MMPl5n66Pnnn48OHTpE3759o2PHjpFKpWLBggUxefLk+Pzzz0svkp5zzjnV+hqq4uWXX47FixdHcXFxLFy4MMaPHx+fffZZuX333nvvGDZsWEb7qaeeGoMHDy5zIaywsDAOOOCAOProo6Nt27bxySefxLvvvpux70UXXZRxoXLXXXfN6Pf000/HkUceGV27di1dSHnw4MHRtGnTCr/WESNGZLS98cYbceihh250nzfeeCMOP/zwMm2PPvpomYubQ4YMieOPP75Mn6lTp8aee+4Zffv2ja5du8bq1atj8uTJMXbs2OjQoUOVppbZ0JVXXhkPP/xwrFy5srTt448/jl133TUGDBgQDRs2jH/+85/lLu58/fXXl/n+/fffjz/96U+RSqVit912iz322CNat24d9evXj1mzZsXrr7+e8fPYtWvX0v+/dOnSuPPOO+POO++M5s2bx1577RU77bRTNGvWLFasWBEfffRRfPzxx2X2z8nJiZ133nmL34ct0bNnzzLrXpSUlMR+++0XAwYMiKZNm8bYsWOrbW2TTdl1113jww8/LP1+zZo10atXr+jXr1/pZ/z444+PQw45pMJjNm7cOK655pqM31ljxoyJLl26xGGHHRadOnWK5cuXxyeffBLjx4+Pvn37Zly43xK33HJLvPbaa9GrV6+YO3duvPDCCxkXuNu3bx8nn3xy6fflhce///3v46233orddtstvvnmm3jttdfK/ftQG8r7/XTFFVfE22+/HTvssEOkUqlo27ZtXHHFFRHxn+Bv8ODB8bvf/S46dOgQ3bt3j/bt20eTJk1i0aJF8eabb2ZMkbT+zxYAUMelAQDqgO7du6cjotJf/fr1SxcXF2eMd91111VpvIhI77TTTumPPvpoi1/ThuO2aNEi3bZt280ef+DAgeWON3LkyHROTk6lX8/w4cPLjDNq1KiMPmedddYWv951+vbtW+X3/ogjjkjPmzdvo2N/9tln6ebNm1dqzIMPPji9atWqjLGKi4vTnTp12uz+m6pnQ2PGjMnYv3Xr1um1a9ducr81a9akW7RokbHvBx98UKbflVdeWeHX3alTp4zjlPd6K2LEiBHpVCpVqff98ssvzxjnoIMOqtQY9evXT0+YMKF0/9dee63Sn6lTTz21Qq9xQ+X9nFT0/drQU089tdk6c3Nz04ceeuhmf36//vrrjD59+/atUB3XXHPNZuu49dZbK32skpKS9Omnn17hc1LeGBX5/A4fPjyjX7du3TZ7vFQqlX7++efLjFVUVJTeYYcdNrvvUUcdVaH6y/u99/XXX1foNVx33XUZ/b788svN/sx17969tP9VV11V6Z+NYcOGZRwXAKibTAcEANQJ++67b+md1xU1cODAeOaZZzLm1a6qZs2axcUXXxwffvhh7LPPPtUy5vry8/Pj5Zdfjvbt22+0z5FHHhkPPvhgudtOOumkeO2112LHHXes8DG7dOlS7h2lW5sdd9wx/vSnP8XLL7+cccf++nbfffcYO3Zs7LvvvpsdM5VKxTnnnBOvvPJKuVN/5OTkxB//+Mdq+/xElL8g8PHHH7/ZNRlyc3Pj2GOP3ex4Q4YMiXvvvTeaNWu2ZYVW0mmnnRbPP/98hRZXbty4cfz5z3+OP/zhDxnbKrOAbdOmTeOhhx4qMy1UZRfAPeyww+Ivf/lLpfapCSeeeGL8+te/3uj2Bg0axH333ZexzkJ1u/TSS6ttofP1pVKpePjhh+P3v/99tT89tTkDBw6MP/zhDxv9Oc7NzY1hw4ZlrMXStGnTGDlyZBQUFGx07OOOO67cBeNrw8477xwXXXRRhftX5mcjlUrFr371q/jpT39aldIAgK2Q6YAAgDrhoYceijvuuCNGjx4dY8aMiYkTJ8ZXX30Vc+bMiWXLlkVOTk4UFBTELrvsEr169YrTTz99owuObkpOTk7Ur18/mjRpEi1btoz27dtH165do3fv3nHSSSdFfn5+Dby6/6dHjx4xceLEuO222+Lpp5+OadOmRW5ubuy1115x3nnnxdlnn73JizmHHnpofPnll/HUU0/F888/H+PGjYs5c+ZEUVFRNGnSJLbbbrvYfffdo1evXnHkkUeWu2hnttSrVy/q168fzZo1i1atWkWnTp2iW7duccghh8SAAQMqvHjxrrvuGh988EG88MILMXLkyHjvvfdi1qxZsXz58mjevHl07tw5+vTpE+eee27GPPobOvnkk+PNN9+MoUOHxnvvvRdz586NNWvWVOn1rV27Np544omM9pNOOqlC+59wwgnx0EMPlWl7/PHH49Zbby3z3px//vlx6qmnxkMPPRSvvfZafPzxx7FgwYJYu3ZtbLfddtGxY8fo27dvxkXPLTVgwICYNm1aPPzww/Hiiy/G+PHjY/78+VFcXBwtW7aMPfbYIw477LA4//zzN7og7EsvvRT//Oc/46233orx48fHv//975g7d26sWLEiGjZsGK1atYrdd989Dj/88Dj77LMzFgo/7LDDYtKkSfH666/H2LFjSxfVLiwsjHQ6HXl5ebHjjjtGz54945RTTslY5DWbbr311ujbt2/ceeedMW7cuFi+fHnssMMO0a9fv/jlL38Ze++9d8b0SdWtZcuWMXbs2Lj11lvjpZdeiq+//rrMIsxbIicnJ66++uq44IILYvjw4TFq1KiYNGlS6ZoD22+/fXTu3DkOPfTQOO6446rlmOtcfvnl0adPnxg6dGi8/fbbMW/evGjVqlUccsghcfnll8fee+9d7n4HHXRQfPLJJ3HzzTfHyy+/HLNmzYr8/Pzo3r17nHPOOXHmmWfG9OnTq7XWyrj99tujZ8+e8cADD8Qnn3wSixcv3uhaGNddd10ceeSRMXr06Pjggw9i6tSpMWvWrCgqKop69epFQUFB7LzzznHQQQfFWWedlbHmCgBQt6XS6XQ620UAAGyLNryY36lTp5g2bVp2igGo4x544IGMNU6uu+66Gg9PAAC2dqYDAgAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIqFQ6nU5nuwgAAAAAAKD6eRIAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQELlZruArUlJSUnMnDkzmjVrFqlUKtvlAAAAAABARESk0+lYunRptGvXLnJyKn5/vxBgPTNnzoyOHTtmuwwAAAAAACjXt99+Gx06dKhwfyHAepo1axYR/3kT8/Pzs1wNAAAAAAD8x5IlS6Jjx46l17ErSgiwnnVTAOXn5wsBAAAAAADY6lR2KnsLAwMAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABIqN9sFAAAAAMDGFBYWVut4JSUlUVRUVK1j1qa8vLzIyan++3oLCgqqfUxg6yAEAAAAAGCrNWjQoGyXsE0YMWJEtksAaojpgAAAAAAAIKE8CQAAAADAVmvYsGHVOp7pgIBtjRAAAAAAgK1WTcxV36JFi2ofE2BrJTYEAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhMrNdgEAVVFYWFjtY5aUlERRUVG1j1tb8vLyIienerPdgoKCah0PAAAAgNolBADqpEGDBmW7hG3CiBEjsl0CAAAAAFvAdEAAAAAAAJBQngQA6qRhw4ZV+5imAwIAAAAgaYQAQJ1UU3PVt2jRokbGBQAAAIBscMsoAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQiQ0BBg8eHKlUKvbcc89slwIAAAAAAFmRyBBgxowZMWTIkGjatGm2SwEAAAAAgKzJzXYBNeHXv/51fP/734/i4uKYP39+tssBAAAAAICsSNyTAP/6179i5MiRMXTo0GyXAgAAAAAAWZWoJwGKi4vjoosuiv/+7/+Ovfbaa7P9V61aFatWrSr9fsmSJTVZHgAAAAAA1KpEhQB/+ctfYvr06fH6669XqP/NN98cN9xwQw1XBQAAAAAA2ZGY6YAWLFgQ1157bVxzzTXRpk2bCu1z5ZVXRmFhYenXt99+W8NVAgAAAABA7UnMkwBXX311tGzZMi666KIK79OwYcNo2LBhDVYFAAAAAADZk4gQ4Isvvoh77703hg4dGjNnzixtX7lyZaxZsyamTZsW+fn50bJlyyxWCQAAAAAAtSuVTqfT2S5iS40ePToOOeSQTfa55JJLYujQoZvss2TJkigoKIjCwsLIz8+vxgoBAAAAAKDqqnr9OhFPAuy5557x9NNPZ7RfffXVsXTp0rj99ttj5513zkJlAAAAAACQPYl4EmBj+vXrF/Pnz49JkyZVqL8nAQAAAAAA2BpV9fp1Tg3WBAAAAAAAZFEipgPamNGjR2e7BAAAAAAAyBpPAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACZWb7QIAAAAAANj6FBYWVut4JSUlUVRUVK1j1qa8vLzIyan+++oLCgqqfcz1CQEAAAAAAMgwaNCgbJewTRgxYkSNjm86IAAAAAAASChPAgAAAAAAkGHYsGHVOp7pgLJDCAAAAAAAQIaamKu+RYsW1T4mm1b3YgsAAAAAAKBChAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKFys11AXVZYWFjtY5aUlERRUVG1j1tb8vLyIienerOlgoKCah0PAAAAAGBbIQTYAoMGDcp2CduEESNGZLsEAAAAAIA6yXRAAAAAAACQUJ4E2ALDhg2r9jFNBwQAAAAAQHURAmyBmpqrvkWLFjUyLgAAAAAA2xa3bAMAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQuVmuwDIlsLCwmofs6SkJIqKiqp93NqSl5cXOTnVmw0WFBRU63gAAAAAQMUJAdhmDRo0KNslbBNGjBiR7RIAAAAAYJtlOiAAAAAAAEgoTwKwzRo2bFi1j2k6IAAAAABgayIEYJtVU3PVt2jRokbGBQAAAACoLLf8AgAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEys12AQAAAABAMhUWFlbreCUlJVFUVFStY9amvLy8yMmp/vuyCwoKqn1MkkMIAAAAAADUiEGDBmW7hG3CiBEjsl0CWzHTAQEAAAAAQEJ5EgAAAAAAqBHDhg2r1vFMBwSVJwQAAAAAAGpETcxV36JFi2ofE5JM7AQAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUIkIAT799NMYOHBgdOnSJZo0aRKtW7eOPn36xHPPPZft0gAAAAAAIGtys11AdZg+fXosXbo0zjrrrGjXrl0sX748nnrqqTjuuOPinnvuiQsuuCDbJQIAAAAAQK1LpdPpdLaLqAnFxcXRs2fPWLlyZUyZMqVC+yxZsiQKCgqisLAw8vPza7hCAAAAAAComKpev07EdEDlqVevXnTs2DEWL16c7VIAAAAAACArEjEd0DrLli2LFStWRGFhYTz77LPx0ksvxamnnrrR/qtWrYpVq1aVfr9kyZLaKBMAAAAAAGpFokKAyy67LO65556IiMjJyYkTTzwx7rrrro32v/nmm+OGG26orfIAAAAAAKBWJWpNgClTpsSMGTNi5syZ8fe//z0aNGgQw4YNi+23377c/uU9CdCxY0drAgAAAAAAsFWp6poAiQoBNnTkkUfG4sWL4/33349UKrXZ/hYGBgAAAABga2Rh4HKcfPLJMW7cuJg6dWq2SwEAAAAAgFqX6BBgxYoVERFRWFiY5UoAAAAAAKD2JSIEmDt3bkbbmjVr4qGHHorGjRtHt27dslAVAAAAAABkV262C6gOP/3pT2PJkiXRp0+faN++fcyePTseffTRmDJlStx2222Rl5eX7RIBAAAAAKDWJSIEOPXUU+O+++6LYcOGxYIFC6JZs2bRs2fP+MMf/hDHHXdctssDAAAAAICsSKXT6XS2i9haVHV1ZQAAAAAAqElVvX6diDUBAAAAAACATEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASKndLdl62bFm88847MWbMmJg5c2bMmzcvVq5cGa1atYo2bdrEHnvsEX379o2uXbtWV70AAAAAAEAFVToEWLNmTYwcOTLuvffeeOedd6K4uLh0WzqdjoiIVCpVZp/tt98+Tj/99LjgggsEAgAAAAAAUEtS6XVX7jdj5cqV8cc//jHuuOOOWLBgQekF/9zc3Nhtt92idevW0bJly2jcuHEsXLgwFi5cGF9//XXMmzfvPwf6/4OBQw89NIYMGRL7779/Db2kqluyZEkUFBREYWFh5OfnZ7scAAAAAACIiKpfv67QkwDDhw+Pa6+9NmbOnBnpdDq6d+8ep59+evTp0yd69uwZjRo12ui+06ZNi/fffz+eeeaZePbZZ+ONN96I73//+3HKKafELbfcEh07dqxwsQAAAAAAQMVV6EmAnJycaNCgQZxzzjnxs5/9LPbee+8qHWzZsmXx1FNPxS233BKTJ0+O66+/Pq699toqjVUTPAkAAAAAAMDWqEafBPjpT38av/vd77b4rv2mTZvGmWeeGWeccUY8+eSTsWbNmi0aDwAAAAAA2LgKrwmwLfAkAAAAAAAAW6OqXr/OqcGaAAAAAACALBICAAAAAABAQlVoTQAAgHUKCwurfcySkpIoKiqq9nFrQ15eXuTkVP99FQUFBdU+JgAAANueLQoB0ul0DB8+PB5//PGYMGFCLFq0KNauXbvR/qlUapPbAYCt36BBg7JdwjZhxIgR2S4BgPUIwcsSggNA3VHlEKCoqCgGDBgQ77zzTlhbGAAAgCQTgtcOITgAVL8qhwDXX399vP3221GvXr04/fTT46ijjortt98+cnPNMAQASTZs2LBqH9OdkAAAAFAzUukq3sbfqVOnmDFjRtx5551x4YUXVnddWbFkyZIoKCiIwsLCyM/Pz3Y5AAAAbCVMB1SW6YAAoPZV9fp1lUOAxo0bR0lJSSxdujQaNGhQlSG2OkIAAAAAAAC2RlW9fl3l2L5t27bRpEmTxAQAAAAAAACQNFUOAY466qhYsmRJTJkypTrrAQAAAAAAqkmVQ4Arr7wyWrZsGRdffHGsWbOmOmsCAAAAAACqQW5Vd9xxxx3jxRdfjFNOOSV69uwZl112Wey3337RrFmzze4HAAAAAADUvCqHABERu+22Wxx77LFx1113xbnnnrvZ/qlUKtauXbslhwQAAAAAACqoyiHA/Pnzo1+/fvHZZ59FREQ6nd7sPhXpAwAAAAAAVI8qhwA33HBDTJ48OZo0aRKXXXZZHHXUUbH99ttHbu4WPVwAAAAAAABUkypfsX/uuecilUrF/fffH6ecckp11gQAAAAAAFSDnKruOHfu3GjQoEGcdNJJ1VkPAAAAAABQTaocArRr1y7q168f9erVq856AAAAAACAalLlEOC4446LZcuWxQcffFCd9QAAAAAAANWkyiHA1VdfHe3atYuf/exnsXjx4mosCQAAAAAAqA5VXhh40qRJMWTIkLjkkkuiW7ducf7558cBBxwQzZo12+R+ffr0qeohAQAAAACASkil0+l0VXbMycmJVCpVuYOlUrF27dqqHK5WLFmyJAoKCqKwsDDy8/OzXQ4AAAAAAERE1a9fV/lJgIiIyuYHVcwbAAAAAACAKqhyCFBSUlKddQAAAAAAANWsygsDAwAAAAAAWzchAAAAAAAAJFSVQ4DVq1fHhAkTYsqUKZvtO2XKlJgwYUKsWbOmqocDAAAAAAAqqcohwBNPPBH77LNPDB06dLN9Bw8eHPvss0+MHDmyqocDAAAAAAAqqcohwFNPPRUREWeeeeZm+5533nmRTqeFAAAAAAAAUIuqHAJMmjQpcnNz44ADDths34MOOihyc3Nj4sSJVT0cAAAAAABQSVUOAWbOnBkFBQWRm5u72b7169ePgoKCmDVrVlUPBwAAAAAAVFKVQ4AGDRrE0qVLK9Q3nU5HUVFRpFKpqh4OAAAAAACopCqHADvttFOsXr06xowZs9m+7777bqxatSo6depU1cMBAAAAAACVVOUQ4Igjjoh0Oh2//e1vY+3atRvtt3bt2rjyyisjlUrFkUceWdXDAQAAAAAAlVTlEODiiy+ORo0axdtvvx2HH354jB8/PqPPRx99FIcddli8/fbb0bBhw7jkkku2qFgAAAAAAKDiNr+q70Z06NAh7rnnnjj77LPjrbfeiv322y922GGH0il/pk+fHrNnz450Oh2pVCruvffe2HHHHautcAAAAAAAYNOqHAJERJxxxhnRsmXLuOiii2LatGkxa9asmDVrVpk+Xbp0ibvuuiv69++/RYUCAAAAAACVk0qn0+ktHaS4uDhGjRoV7777bsyePTtSqVTssMMO0bt37zjkkEMiJ6fKsw7VqiVLlkRBQUEUFhZGfn5+tssBAAAAAICIqPr162oJAZJCCAAAAAAAwNaoqtev68Yt+gAAAAAAQKUJAQAAAAAAIKEqFAIcc8wx8eGHH1bbQVesWBF//OMfY9iwYdU2JgAAAAAAUFaFQoCXXnopDjjggDj++OPj5ZdfjpKSkiodbPr06XHTTTfFTjvtFFdccUXMnz+/SuMAAAAAAACbl1uRTq+99lpcdtll8dxzz8Xzzz8fbdq0iYEDB8YPfvCDOOCAA6Jz587l7rd8+fL44IMP4v33349nn3023n333YiIaNCgQVx22WVx8cUXV9sLAQAAAAAAykql0+l0RTqm0+l44IEH4pZbbonPP/88UqlU6bZmzZpF69ato2XLltGwYcNYtGhRLFy4MObNm1f61EA6nY5GjRrFaaedFtdee2106tSpZl7RFqjq6soAAAAAAFCTqnr9usIhwPrefPPNuPfee+PVV1+NBQsWbLJvvXr1omfPnnHaaafFWWedFc2bN6/s4WqNEAAAAAAAgK1RrYYA65s8eXK8//77MXPmzJg3b16sXLkyWrVqFW3atIlu3bpF7969Iy8vb0sOUWuEAAAAAAAAbI2qev26QmsCbEq3bt2iW7duWzoMAAAAAABQzXKyXQAAAAAAAFAzhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACTUFi8MDAAAALA1KywsrPYxS0pKoqioqNrHrQ15eXmRk1P994UWFBRU+5gAbDkhAAAAAJBogwYNynYJ24QRI0ZkuwQAymE6IAAAAAAASChPAgAAAACJNmzYsGof03RAANQVQgAAAAAg0WpqrvoWLVrUyLgAUJ3EvgAAAAAAkFBVfhKguLg43nrrrXjzzTfjq6++ioULF0ZERKtWrWLnnXeOfv36xcEHH+zxMgAAAAAAyJJUOp1OV3anxx9/PK6++ur4+uuvN9lv5513jiFDhsTJJ59c5QJr05IlS6KgoCAKCwsjPz8/2+UAAAAAAEBEVP36daVv07/66qvjxz/+cXz99deRTqcjnU5H/fr1Y7vttovtttsuGjRoUNr+5ZdfxqmnnhrXX399ZQ8DAAAAAABsoUqFAP/4xz9iyJAhkU6no127dnHrrbfG5MmTY+XKlTFr1qyYNWtWrFixIiZPnhy33HJLdOjQIdLpdPz+97+P5557rqZeAwAAAAAAUI4KTweUTqejc+fOMWPGjDjkkEPiqaeeioKCgk3uU1hYGCeeeGKMGjUqOnfuHP/+97+rpeiaYjogAAAAAAC2RjU+HdCrr74a3377bWy//fbx5JNPbjYAiIgoKCiIkSNHxvbbbx/Tp0+P1157rcKFAQAAAAAAW6bCIcArr7wSqVQqBg0aFC1atKjwAVq0aBGDBg2KdDodL7/8cpWKBAAAAAAAKq/CIcD48eMjIuKYY46p9EF++MMfRkTERx99VOl9AQAAAACAqqlwCLBuPv8ePXpU+iA9evSIVCq11a8JAAAAAAAASVLhEKCwsDDy8vIiNze30gfJzc2NZs2aRWFhYaX3BQAAAAAAqqbCIUBRUVE0bdq0ygdq3LhxFBUVVXl/AAAAAACgciocApSUlGzxwdLp9BaPAQAAAAAAVEyFQwAAAAAAAKBuqdQE//PmzYsuXbpU6UDz5s2r0n4AAAAAAEDVVCoEKC4ujmnTplX5YKlUqsr7AgAAAAAAlVPhEOCss86qyToAAAAAAIBqVuEQYPjw4TVZBwAAAAAAUM0sDAwAAAAAAAklBAAAAAAAgISq1MLAFbF69ep4+eWX4/PPP4+GDRvGvvvuGwcffHB1HwYAAAAAANiMCocAS5cujaeffjoiIk499dRo2LBhRp8PPvggTjrppJgxY0aZ9l69esU//vGP2GGHHbawXAAAAAAAoKIqPB3QG2+8EWeffXYMHTq03ABg7ty5MWDAgJgxY0ak0+kyX++//34cd9xx1Vo4AAAAAACwaRUOAd56662IiDj99NPL3f6HP/wh5s+fHxERZ511VrzzzjvxySefxC9/+ctIp9Px4YcfxsiRI6uhZAAAAAAAoCIqPB3Q2LFjI5VKRf/+/cvd/uijj0YqlYpjjz02hg8fXtp+2223xcKFC+PBBx+Mp556Kk4++eQtrxoAAAAAANisCj8JMGvWrMjNzY1u3bplbPv0009j7ty5ERFx8cUXZ2y/5JJLIiJi/PjxVa0TAAAAAACopAqHAHPmzIn8/PzIycncZezYsRER0aBBgzj44IMztu+5556RSqVi5syZW1AqAAAAAABQGRUOAYqLi2PJkiXlbvvwww8jImKPPfaIBg0aZGzPzc2NFi1axIoVK6pYJgAAAAAAUFkVDgG22267WLt2bXz11VcZ28aMGROpVCr233//je5fVFQUTZs2rVqVAAAAAABApVU4BNh3330jIuLee+8t0/7FF1/Exx9/HBERffv2LXff6dOnx+rVq6NDhw5VLBMAAAAAAKisCocAp512WqTT6fjzn/8ct956a3z++efxxhtvxMCBAyOdTkfTpk3j2GOPLXfff/3rXxHxn7UBAAAAAACA2lHhEGDgwIHRp0+fWLt2bfz2t7+Nbt26xZFHHhkTJ06MVCoVv/rVr6JZs2bl7vvEE09EKpUqd9FgAAAAAACgZuRWpvMzzzwTZ5xxRjz//POlbalUKv77v/87rr322nL3+eKLL+Lll1+OiIgBAwZsQakAAAB1V2FhYbWPWVJSEkVFRdU+bm3Iy8uLnJwK35dWYQUFBdU+JgBAXVapEKCgoCCeffbZ+PLLL0vXAdh///2jU6dOG92nfv368cwzz0T9+vWjS5cuW1QsAABAXTVo0KBsl7BNGDFiRLZLAADYqlQqBFhnl112iV122aVCfTt37hydO3euymEAAAAAAIAtUKUQAAAAgMoZNmxYtY9pOiAAADZHCAAAAFALamqu+hYtWtTIuAAAJIPbLgAAAAAAIKGEAAAAAAAAkFCmAwIA2IYUFhZW+5jmJM9UU9O+AAAAVJYQAABgGzJo0KBsl7BNGDFiRLZLAAAAiAjTAQEAAAAAQGJ5EgAAYBsybNiwah/TdEAAAABbLyEAAMA2pKbmqm/RokWNjAsAAMCWcdsTAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKFys10AANuewsLCah+zpKQkioqKqn3c2pKXlxc5OdWbzRcUFFTreAAAAEDdIwQAoNYNGjQo2yVsE0aMGJHtEgAAAIAsMx0QAAAAAAAklCcBAKh1w4YNq/YxTQcEAAAAkEkIAECtq6m56lu0aFEj4wIAAADUVW45BAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASKhEhwLhx4+IXv/hFdO/ePZo2bRo77rhjnHLKKTF16tRslwYAAAAAAFmTm+0CqsMf/vCHeOedd2LgwIHRo0ePmD17dtx1112x7777xnvvvRd77rlntksEAAAAAIBal0qn0+lsF7Gl3n333dhvv/2iQYMGpW1ffPFF7LXXXnHyySfHI488UqFxlixZEgUFBVFYWBj5+fk1VS4AAAAAAFRKVa9fJ+JJgN69e2e07brrrtG9e/f47LPPslARAAAAAABkXyJCgPKk0+mYM2dOdO/efaN9Vq1aFatWrSr9fsmSJbVRGgAAAAAA1IpELAxcnkcffTS+++67OPXUUzfa5+abb46CgoLSr44dO9ZihQAAAAAAULMSsSbAhqZMmRK9evWK7t27x1tvvRX16tUrt195TwJ07NjRmgAAAAAAAGxVtuk1AdY3e/bsOOaYY6KgoCBGjhy50QAgIqJhw4bRsGHDWqwOAAAAAABqT6JCgMLCwjj66KNj8eLF8dZbb0W7du2yXRIAAAAAAGRNYkKAlStXxrHHHhtTp06N119/Pbp165btkgAAAAAAIKsSEQIUFxfHqaeeGmPGjIlnnnkmDjzwwGyXBAAAAAAAWZeIEOCyyy6LZ599No499thYuHBhPPLII2W2/+QnP8lSZQAAAAAAkD2pdDqdznYRW6pfv37x5ptvbnR7RV9iVVdXBgAAAACAmlTV69eJeBJg9OjR2S4BAAAAAAC2OjnZLgAAAAAAAKgZQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAkVGJCgKKiorjuuuuif//+0bJly0ilUvHAAw9kuywAAAAAAMiaxIQA8+fPjxtvvDE+++yz2HvvvbNdDgAAAAAAZF1utguoLm3bto1Zs2bFDjvsEB988EHsv//+2S4JAAAAAACyKjFPAjRs2DB22GGHbJcBAAAAAABbjcQ8CVAVq1atilWrVpV+v2TJkixWAwAAAAAA1SsxTwJUxc033xwFBQWlXx07dsx2SQAAAAAAUG226RDgyiuvjMLCwtKvb7/9NtslAQAAAABAtdmmpwNq2LBhNGzYMNtlAAAAAABAjdimnwQAAAAAAIAkEwIAAAAAAEBCCQEAAAAAACChErUmwF133RWLFy+OmTNnRkTEc889FzNmzIiIiIsuuigKCgqyWR4AAAAAANSqVDqdTme7iOrSuXPnmD59ernbvv766+jcufMm91+yZEkUFBREYWFh5Ofn10CFAAAAAABQeVW9fp2oJwGmTZuW7RIAAAAAAGCrYU0AAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASKjcbBcAAABUTGFhYbWPWVJSEkVFRdU+bm3Iy8uLnJzqv6+poKCg2scEAIBsEQIAAEAdMWjQoGyXsE0YMWJEtksAAIBqYzogAAAAAABIKE8CAABAHTFs2LBqH9N0QAAAkGxCAAAAqCNqaq76Fi1a1Mi4AABA9rltBgAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEIAAAAAAABIKCEAAAAAAAAklBAAAAAAAAASSggAAAAAAAAJJQQAAAAAAICEEgIAAAAAAEBCCQEAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASKjcbBewNUmn0xERsWTJkixXAgAAAAAA/8+669brrmNXlBBgPUuXLo2IiI4dO2a5EgAAAAAAyLR06dIoKCiocP9UurKxQYKVlJTEzJkzo1mzZpFKpbJdTrVbsmRJdOzYMb799tvIz8/PdjlUkvNXtzl/dZvzV7c5f3Wb81e3OX91m/NXtzl/dZvzV7c5f3Wb81e3Jf38pdPpWLp0abRr1y5ycio+078nAdaTk5MTHTp0yHYZNS4/Pz+RPwTbCuevbnP+6jbnr25z/uo2569uc/7qNuevbnP+6jbnr25z/uo2569uS/L5q8wTAOtYGBgAAAAAABJKCAAAAAAAAAklBNiGNGzYMK677rpo2LBhtkuhCpy/us35q9ucv7rN+avbnL+6zfmr25y/us35q9ucv7rN+avbnL+6zfkrn4WBAQAAAAAgoTwJAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAAAAAgoYQAAAAAAACQUEKAbcCqVaviiiuuiHbt2kXjxo2jV69e8dprr2W7LCqoqKgorrvuuujfv3+0bNkyUqlUPPDAA9kuiwoYN25c/OIXv4ju3btH06ZNY8cdd4xTTjklpk6dmu3SqIBPP/00Bg4cGF26dIkmTZpE69ato0+fPvHcc89luzSqaPDgwZFKpWLPPffMdilsxujRoyOVSpX79d5772W7PCroo48+iuOOOy5atmwZTZo0iT333DPuuOOObJfFZpx99tkb/flLpVLx3XffZbtENuOLL76I//qv/4oOHTpEkyZNYvfdd48bb7wxli9fnu3SqIAPP/ww+vfvH/n5+dGsWbM48sgj4+OPP852WWygMv9O/+yzz6J///6Rl5cXLVu2jDPOOCPmzZtXuwVTRkXP39ixY+PCCy+Mnj17Rv369SOVStV+sWSoyPkrKSmJBx54II477rjo2LFjNG3aNPbcc8+46aabYuXKldkpPMtys10ANe/ss8+OkSNHxqWXXhq77rprPPDAAzFgwIAYNWpUHHzwwdkuj82YP39+3HjjjbHjjjvG3nvvHaNHj852SVTQH/7wh3jnnXdi4MCB0aNHj5g9e3bcddddse+++8Z7773nQuRWbvr06bF06dI466yzol27drF8+fJ46qmn4rjjjot77rknLrjggmyXSCXMmDEjhgwZEk2bNs12KVTCxRdfHPvvv3+Ztl122SVL1VAZr776ahx77LGxzz77xDXXXBN5eXnx1VdfxYwZM7JdGpvx05/+NA4//PAybel0On72s59F586do3379lmqjIr49ttv44ADDoiCgoL4xS9+ES1btowxY8bEddddFx9++GE888wz2S6RTfjoo4/i4IMPjo4dO8Z1110XJSUlcffdd0ffvn1j7Nixsdtuu2W7RP5/Ff13+owZM6JPnz5RUFAQQ4YMiaKiovjjH/8YEydOjLFjx0aDBg1qt3AiouLn78UXX4y//e1v0aNHj+jSpYsb+rYSFTl/y5cvj3POOSe+//3vx89+9rPYbrvtSv8evvHGG/HPf/5z2wt10iTa+++/n46I9K233lratmLFivTOO++cPvDAA7NYGRW1cuXK9KxZs9LpdDo9bty4dESkhw8fnt2iqJB33nknvWrVqjJtU6dOTTds2DD94x//OEtVsSXWrl2b3nvvvdO77bZbtkuhkk499dT0oYcemu7bt2+6e/fu2S6HzRg1alQ6ItJPPvlktkuhCgoLC9Pbb799+oQTTkgXFxdnuxyqwVtvvZWOiPTgwYOzXQqbMXjw4HREpCdNmlSm/cwzz0xHRHrhwoVZqoyKGDBgQLpFixbp+fPnl7bNnDkznZeXlz7xxBOzWBkbqui/0wcNGpRu3Lhxevr06aVtr732Wjoi0vfcc09tlcsGKnr+Zs+enV6+fHk6nU6nf/7zn6ddRt06VOT8rVq1Kv3OO+9k7HvDDTekIyL92muv1UapWxXTASXcyJEjo169emXuWG3UqFGcd955MWbMmPj222+zWB0V0bBhw9hhhx2yXQZV0Lt374w7O3bdddfo3r17fPbZZ1mqii1Rr1696NixYyxevDjbpVAJ//rXv2LkyJExdOjQbJdCFSxdujTWrl2b7TKohBEjRsScOXNi8ODBkZOTE8uWLYuSkpJsl8UWGDFiRKRSqTj99NOzXQqbsWTJkoiI2H777cu0t23bNnJyctx1vJV766234vDDD49WrVqVtrVt2zb69u0bzz//fBQVFWWxOtZX0X+nP/XUU/HDH/4wdtxxx9K2ww8/PLp27Rp///vfa7JENqGi52/77bePxo0b10JFVEZFzl+DBg2id+/eGe0nnHBCRMQ2eU1GCJBw48ePj65du0Z+fn6Z9gMOOCAiwtyCUMvS6XTMmTMnWrdune1SqKBly5bF/Pnz46uvvoo///nP8dJLL8Vhhx2W7bKooOLi4rjoooviv//7v2OvvfbKdjlU0jnnnBP5+fnRqFGjOOSQQ+KDDz7IdklUwOuvvx75+fnx3XffxW677RZ5eXmRn58fgwYN2mbnYK3L1qxZE3//+9+jd+/e0blz52yXw2b069cvIiLOO++8+Pjjj+Pbb7+NJ554IoYNGxYXX3yxafG2cqtWrSr3gmOTJk1i9erVMWnSpCxURVV99913MXfu3Nhvv/0yth1wwAExfvz4LFQF27bZs2dHRGyT12SsCZBws2bNirZt22a0r2ubOXNmbZcE27RHH300vvvuu7jxxhuzXQoVdNlll8U999wTERE5OTlx4oknxl133ZXlqqiov/zlLzF9+vR4/fXXs10KldCgQYM46aSTYsCAAdG6deuYPHly/PGPf4wf/OAH8e6778Y+++yT7RLZhC+++CLWrl0bxx9/fJx33nlx8803x+jRo+POO++MxYsXx2OPPZbtEqmEV155JRYsWBA//vGPs10KFdC/f//4/e9/H0OGDIlnn322tP2qq66Km266KYuVURG77bZbvPfee1FcXBz16tWLiIjVq1fH+++/HxFhYe46ZtasWRERG70ms3Dhwli1alU0bNiwtkuDbdYtt9wS+fn5cfTRR2e7lFonBEi4FStWlPsHpVGjRqXbgdoxZcqU+PnPfx4HHnhgnHXWWdkuhwq69NJL4+STT46ZM2fG3//+9yguLo7Vq1dnuywqYMGCBXHttdfGNddcE23atMl2OVRC7969yzy+e9xxx8XJJ58cPXr0iCuvvDJefvnlLFbH5hQVFcXy5cvjZz/7Wdxxxx0REXHiiSfG6tWr45577okbb7wxdt111yxXSUWNGDEi6tevH6ecckq2S6GCOnfuHH369ImTTjopWrVqFS+88EIMGTIkdthhh/jFL36R7fLYhAsvvDAGDRoU5513Xlx++eVRUlISN910U+nFZP9+r1vWna/NXZMRAkDtGDJkSLz++utx9913R/PmzbNdTq0zHVDCNW7cOFatWpXRvu5RbHObQe2YPXt2HHPMMVFQUFC6Vgd1w+677x6HH354nHnmmaVzsR577LGRTqezXRqbcfXVV0fLli3joosuynYpVINddtkljj/++Bg1alQUFxdnuxw2Yd1/X5522mll2tfNJz9mzJhar4mqKSoqimeeeSaOOuqoMnOUs/V6/PHH44ILLoi//e1vcf7558eJJ54Y9913X5x11llxxRVXxIIFC7JdIpvws5/9LH73u9/FiBEjonv37rHXXnvFV199FZdffnlEROTl5WW5Qipj3d9D12Qg+5544om4+uqr47zzzotBgwZlu5ysEAIkXNu2bUvvGljfurZ27drVdkmwzSksLIyjjz46Fi9eHC+//LKfuzru5JNPjnHjxsXUqVOzXQqb8MUXX8S9994bF198ccycOTOmTZsW06ZNi5UrV8aaNWti2rRpsXDhwmyXSSV17NgxVq9eHcuWLct2KWzCur9zGy5Mut1220VExKJFi2q9Jqrm//7v/2L58uWmAqpD7r777thnn32iQ4cOZdqPO+64WL58uTnI64DBgwfHnDlz4q233ooJEybEuHHjShdX79q1a5arozLWTQO0sWsyLVu29BQA1ILXXnstzjzzzDjmmGPiL3/5S7bLyRohQMJ973vfi6lTp8aSJUvKtK+bU/B73/teFqqCbcfKlSvj2GOPjalTp8bzzz8f3bp1y3ZJbKF1j/UWFhZmuRI25bvvvouSkpK4+OKLY6eddir9ev/992Pq1Kmx0047WZujDvr3v/8djRo1cifkVq5nz54RkTl39bq1qEzPVXc8+uijkZeXF8cdd1y2S6GC5syZU+7TUmvWrImIiLVr19Z2SVRBixYt4uCDD4699torIv6z4HqHDh1i9913z3JlVEb79u2jTZs28cEHH2RsGzt2rOsxUAvef//9OOGEE2K//faLv//975Gbu+3OjC8ESLiTTz45iouL49577y1tW7VqVQwfPjx69eoVHTt2zGJ1kGzFxcVx6qmnxpgxY+LJJ5+MAw88MNslUQlz587NaFuzZk089NBD0bhxY4HOVm7PPfeMp59+OuOre/fuseOOO8bTTz8d5513XrbLZCPmzZuX0fbJJ5/Es88+G0ceeWTk5PhP2K3Zurnj77vvvjLtf/vb3yI3Nzf69euXhaqorHnz5sXrr78eJ5xwQjRp0iTb5VBBXbt2jfHjx2c8sfjYY49FTk5O9OjRI0uVUVVPPPFEjBs3Li699FJ//+qgk046KZ5//vn49ttvS9veeOONmDp1agwcODCLlUHyffbZZ3HMMcdE586d4/nnn9/mp9/aduOPbUSvXr1i4MCBceWVV8bcuXNjl112iQcffDCmTZuW8Q8ztl533XVXLF68uPQOuueeey5mzJgREREXXXRRFBQUZLM8NuKyyy6LZ599No499thYuHBhPPLII2W2/+QnP8lSZVTET3/601iyZEn06dMn2rdvH7Nnz45HH300pkyZErfddps7kbdyrVu3jh/96EcZ7UOHDo2IKHcbW49TTz01GjduHL17947tttsuJk+eHPfee280adIk/ud//ifb5bEZ++yzT5x77rlx//33x9q1a6Nv374xevToePLJJ+PKK680LV4d8cQTT8TatWtNBVTH/OY3v4mXXnopfvCDH8QvfvGLaNWqVTz//PPx0ksvxX//93/7+dvK/etf/4obb7wxjjzyyGjVqlW89957MXz48Ojfv39ccskl2S6PDVTk3+m/+93v4sknn4xDDjkkLrnkkigqKopbb7019tprrzjnnHOyWf42ryLnb/r06fHwww9HRJQ+0XHTTTdFRESnTp3ijDPOyELlRGz+/OXk5MRRRx0VixYtit/85jfxwgsvlNl/55133uZu1EylrWyYeCtXroxrrrkmHnnkkVi0aFH06NEjfv/738dRRx2V7dKooM6dO8f06dPL3fb1119H586da7cgKqRfv37x5ptvbnS7X79bt8cffzzuu+++mDhxYixYsCCaNWsWPXv2jIsuusi0CHVYv379Yv78+TFp0qRsl8Im3HHHHfHoo4/Gl19+GUuWLIk2bdrEYYcdFtddd13ssssu2S6PClizZk0MGTIkhg8fHjNnzoxOnTrFz3/+87j00kuzXRoVdOCBB8a///3vmDlzZtSrVy/b5VAJY8eOjeuvvz7Gjx8fCxYsiJ122inOOuusuPzyy7fpaRDqgq+++iouvPDC+Oijj2Lp0qWl5+5Xv/pVNGjQINvlsYGK/jv9008/jV/96lfx9ttvR4MGDeKYY46J2267LWPtHGpXRc7f6NGj45BDDim3z7qbHMiOzZ2/iIiddtppo/ufddZZ8cADD9REaVstIQAAAAAAACSUCeUAAAAAACChhAAAAAAAAJBQQgAAAAAAAEgoIQAAAAAAACSUEAAAAAAAABJKCAAAAAAAAAklBAAAAAAAgIQSAgAAAAAAQEIJAQAAAAAAIKGEAAAAAAAAkFBCAAAAAAAASCghAAAAAAAAJJQQAAAAAAAAEkoIAAAAAAAACSUEAAAAAACAhBICAAAAAABAQgkBAAAgga6//vpIpVLRr1+/bJcCAABkkRAAAACqUefOnSOVSsXZZ5+92b4PPPBApFKpSKVSMW3atBqvbVswevTo0vd0/a8mTZrEDjvsEHvvvXeceeaZMWzYsFi4cGGN1jJ06NC4/vrr4+OPP67R4wAAwKYIAQAAIIFat24du+22W+y4447ZLiVrWrRoEdtvv31sv/320aRJk1iwYEFMmDAhHn744bjwwgujXbt2cfHFF8eyZctq5PhDhw6NG264QQgAAEBWCQEAACCBfvGLX8SUKVPioYceynYpWfOPf/wjZs+eHbNnz4758+fHmjVr4uuvv46HH344evfuHatWrYo777wzevXqFYsWLcp2uQAAUCOEAAAAwDajc+fO8ZOf/CTeeeed+NOf/hQREZ9++mn813/9V5YrAwCAmiEEAACArdCGC/u+8cYbccwxx0SbNm2iUaNGsccee8QNN9wQK1eurND+5XnppZfiiCOOiObNm0deXl7svffeccstt8SaNWs2uf+6dQ8eeOCBjY599tlnb3ZthEmTJsUFF1wQu+66azRp0iTy8vKiR48ecdVVV8X8+fM3ul91+eUvfxk///nPIyLi1VdfjTfeeKPcfitXroyhQ4dG7969o0WLFtGoUaPo1KlTnHnmmeVO9bPuvZs+fXpERJxzzjkZaxQAAEBtEQIAAMBW7tZbb40jjjgiXnrppVi7dm2sXr06pkyZEtdff30MGDAgiouLKz3mun1ff/31KCwsjPr168fkyZPjiiuuiMMPPzxWr15dA6/k/7nlllti7733jr/+9a/x5ZdfRiqVijVr1sTEiRNjyJAh0aNHjxg/fnyN1hARcdVVV0X9+vUjIuLBBx/M2P7dd9/F/vvvH7/85S9jzJgxsWzZsmjUqFF888038fDDD0fPnj3jzjvvLLNPXl5ebL/99pGT859/buXn55euTbDuCwAAaosQAAAAtmKffPJJ/Pa3v43f/va3MXfu3Fi0aFEsXrw4rr322oiIGDVqVLkXrzfl2WefjRtuuCEiIgYOHBjffPNNLFq0KJYsWRL/+7//G++9914MGzas2l/LOvfdd19cccUV0aRJkxg8eHDMmjUrli1bFsuXL48PPvggDj300Jg1a1Ycd9xxUVRUVGN1RES0bds29tlnn4iIePPNN8tsKy4ujpNOOikmTZoUBQUF8cgjj0RRUVEsXrw4vvrqq/jhD38YJSUlcckll8RLL71Uut+vf/3rmD17dnTs2DEiIm6//fbStQnWfQEAQG0RAgAAwFZs8eLFcc0118SQIUOidevWEfGfO8tvuOGGOPHEEyMi4rHHHqvUmFdeeWVERPTt2zcef/zx0ovVjRs3jgsvvDDuuOOOWLx4cfW9iPUsXbo0fv3rX0dExMiRI+N3v/td7LDDDhERUa9evejZs2e88sor0bNnz5gxY0b87W9/q5E61rf33ntHRMQ333wTa9euLW0fOXJkvP/++xER8fe//z1+/OMfR4MGDSIiokuXLvH0009Hr169Ip1Ox+WXX17jdQIAQFUIAQAAYCvWsGHD0ovmGzr++OMjImLChAkVHm/ChAkxefLkiIi4+uqrS6esWd/5558f7du3r0K1m/fUU0/F4sWLY5999omjjjqq3D65ublx2mmnRUTEK6+8UiN1rK9ly5al/3/hwoWl//+JJ56IiIgDDzwwjjzyyIz9cnNz47rrrouI/6xvMHHixBquFAAAKi832wUAAAAb171798jLyyt3W7t27SKi7IXrzfnggw8i4j8XsH/wgx+U2ycnJyf69esXjz76aCWr3bx33nknIiI+++yz0icAyrNixYqIiNLFdbNh3Xt1+OGHb7TPIYccEvXq1Yvi4uL44IMPYq+99qqt8gAAoEKEAAAAsBVr1qzZRrfl5v7nP+fXn8Jmc+bOnRsREa1bt46GDRtutF+HDh0qPGZlzJw5MyIiVq5cGf9fe/cXWudd/wH8fXaS1C41tGByAlJEL5Th3CS2qGz+qR6YIl5o0U2lOG9GQacYhAb1QsXaVDCrgriL+WeI0w1F0JWBZjjwzxDcGbuQ4ZV/qjSnUdYdSa1JTs7voiS/tmnT/vo7OcnznNcLepHnPE/5JF84vM95P3/Onz9/zf3PnTu3KXNc7OIS5eKrAlb/VhtdFfGSl7wkL3vZy9JsNtf2BwCA7cTtgAAAoIt27tyZ5H/PZN/IxV9wrx5Xdu12O0ly9913p9PpXPPfX/7yl02f6bnnnkuSvOIVr1grVgAAoCyUAAAA0EWrD+/9xz/+cc19V/e56aabLjkDfTONjY0lSf75z39mcXHxmrNdyeoX5Rudyf/iiy9ecfvqLYC28jY/Fzt9+nSeffbZJMnb3/72S15b/Vv9/e9/v+rx58+fz7/+9a9L9gcAgO1ECQAAAF30hje8IUny7LPPZmFhYcN9f/Ob3yRJbrvttgwODm76bEmyb9++JBduIfTrX//6ivusrKzkqaeeuur/sWfPniTJqVOnrnr86v30L3fHHXckSZ555pmcPn36esfeNEePHs3S0lKS5N57773ktdW/1ZNPPnnV45966qm12zHt37//ktdWH7rc6XS6NS4AAPyfKQEAAKCL7r777iQXbvXzjW9846r7/fa3v137Ev6DH/xgT2ZLLhQOt9xyS5ILX4CvrKys2+c73/nOhme/33777UmSn/70p1f8gvvhhx++6vEf+MAHsnv37iwtLWVycnLDL8hXVlZy9uzZjX6d/5cTJ07km9/8ZpLkXe9617orAe65554kydNPP51f/OIX645fXl7Ol770pSTJrbfemltvvfWS10dGRpJkU38HAAC4FiUAAAB00Zvf/OYcPHgwSfL5z38+n/3sZy85Y/7FF1/Mt7/97bz3ve9Np9PJq171qnz84x/v6YxHjx5NkvzqV7/Khz/84bUv7M+fP58HH3wwn/jEJ7J79+6rHv+hD30oSfL888/nvvvuW7sdTqvVygMPPJDDhw9f9fZGu3fvzokTJ5IkP/rRj/Ke97wnv//979fKiJWVlTz//PP52te+lte+9rV5/PHHu/Err/nb3/6WH/zgB7nzzjvz6U9/Oknyute9Lj/84Q/X7Xvw4MG88Y1vTHKhqHnkkUfWrhr485//nIMHD+bpp59Oknz1q19dd/xqKfDjH/84L7zwQld/DwAAuF6eegUAAF323e9+N+fPn8/Jkydz7NixHDt2LLt27crg4GDOnj27dvb7q1/96vzsZz9bO2O8V973vvflc5/7XI4ePZpHH300jz76aPbs2ZN///vfWV5ezlve8pbceeedOXbs2BWPf+c735lDhw7l+9//fh566KE89NBD2b17d1qtVlZWVnL//fen1Wrl4YcfvuLxH/3oR/Of//wnn/rUp/LEE0/kiSeeyI4dO7Jr1660Wq21L9qTpFKp3PDv+f73vz9DQ0NJLpy1f/n/vWPHjhw+fDhf+cpXcvPNN687vlqt5ic/+Unuuuuu/PGPf8xHPvKRfOxjH8vNN9+8dnb/TTfdlAceeCDvfve71x1/33335ZFHHsnvfve7jI6OZmxsbG2eXjzwGAAAElcCAABA1730pS/Nz3/+85w8eTL33HNPXvnKV2ZlZSULCwup1Wq566678uCDD+a5557La17zmi2Z8ctf/nIef/zxvOMd78jIyEj++9//5pZbbsn09HSefPLJtS+rr+Z73/tevv71r+f1r399du7cmZWVldxxxx157LHHNrwN0qrDhw/nT3/6Uz7zmc/k9ttvz44dO3L27Nns2rUr+/bty/33359f/vKXa1cd3IgXXnghzWYzzWYzCwsL2bNnT2677bYcOnQo3/rWt3L69OmcOHHiigXAqpe//OX5wx/+kJmZmbzpTW/Kzp07c+7cuezduzeHDh3KM888k09+8pNXPPatb31rTp48mXq9nt27d6fZbOavf/3rtnkoMgAA/aHS8ZQqAADgMl/4whfyxS9+MW9729s2fEgwAACwvbkSAAAAAAAASkoJAAAAAAAAJaUEAAAAAACAklICAAAAAABASXkwMAAAAAAAlJQrAQAAAAAAoKSUAAAAAAAAUFJKAAAAAAAAKCklAAAAAAAAlJQSAAAAAAAASkoJAAAAAAAAJaUEAAAAAACAklICAAAAAABASSkBAAAAAACgpJQAAAAAAABQUkoAAAAAAAAoKSUAAAAAAACUlBIAAAAAAABKSgkAAAAAAAAlpQQAAAAAAICSUgIAAAAAAEBJKQEAAAAAAKCklAAAAAAAAFBSSgAAAAAAACgpJQAAAAAAAJSUEgAAAAAAAEpKCQAAAAAAACWlBAAAAAAAgJIa2OoByuSxxx7LuXPntnoMLnLvvfde977Wb/uxfsX1f1m7xPptN9av2KxfsVm/YrN+xWb9is1nh2KzfsVm/YrN+hXb9a6fEqCLzp07l3379l19h3Y7w41GBubnszw6moWJiaRa7d2AbOia68e2Zv2KzfoVm/UrtsKvX5/nq8KvX5+zfsVm/YrL2hWb9euBTcxX1q/YrF9xKQF6ZGR2NuPT0xlqNte2LdZqmZuaSqte38LJAACKSb4CAOgu+QrKyTMBemBkdjZ7JyczeNEbaJIMnjmTvZOTGZmd3aLJAACKSb4CAOgu+QrKSwmw2drtjE9PJ51OKpe9VOl0kiTjx48n7XbvZwMAKCL5CgCgu+QrKDUlwCYbbjQy1GyuewNdVel0MjQ3l+FGo6dzAQAUlXwFANBd8hWUmxJgkw3Mz3d1PwCAfidfAQB0l3wF5aYE2GTLo6Nd3Q8AoN/JVwAA3SVfQbkpATbZwsREFmu1dCpXvqCqU6lkcXw8CxMTPZ4MAKCY5CsAgO6Sr6DclACbrVrN3NRUkqx7I139ee7IkaRa7floAACFJF8BAHSXfAWlpgTogVa9nlMzM1kaG7tk+1KtllMzM2nV61s0GQBAMclXAADdJV9BeQ1s9QD9olWvp3XgQIYbjQzMz2d5dPTCJVQaVACAGyJfAQB0l3wF5aQE6KVqNQv792/1FAAA5SFfAQB0l3wFpaMEAKD/tNvObAEA6Cb5CgC2LSUAAH1lZHY249PTGWo217Yt1mqZm5pyj0sAgBsgXwHA9ubBwAD0jZHZ2eydnMzgRR9Qk2TwzJnsnZzMyOzsFk0GAFBM8hUAbH9KAAD6Q7ud8enppNNJ5bKXKp1OkmT8+PGk3e79bAAARSRfAUAhKAEA6AvDjUaGms11H1BXVTqdDM3NZbjR6OlcAABFJV8BQDEoAQDoCwPz813dDwCg38lXAFAMSgAA+sLy6GhX9wMA6HfyFQAUgxIAgL6wMDGRxVotncqVL1jvVCpZHB/PwsREjycDACgm+QoAikEJAEB/qFYzNzWVJOs+qK7+PHfkSFKt9nw0AIBCkq8AoBCUAAD0jVa9nlMzM1kaG7tk+1KtllMzM2nV61s0GQBAMclXALD9DWz1AADQS616Pa0DBzLcaGRgfj7Lo6MXLlF3hhoAwA2RrwBge1MCANB/qtUs7N+/1VMAAJSHfAUA25YSAK5Xu+3MFgCAbpKvAAC6S77iCpQAcB1GZmczPj2doWZzbdtirZa5qSn3uAQAuAHyFQBAd8lXXI0HA8M1jMzOZu/kZAYvegNNksEzZ7J3cjIjs7NbNBkAQDHJVwAA3SVfsRElAGyk3c749HTS6aRy2UuVTidJMn78eNJu9342AIAikq8AALpLvuIalACwgeFGI0PN5ro30FWVTidDc3MZbjR6OhcAQFHJVwAA3SVfcS1KANjAwPx8V/cDAOh38hUAQHfJV1yLEgA2sDw62tX9AAD6nXwFANBd8hXXogSADSxMTGSxVkuncuULqjqVShbHx7MwMdHjyQAAikm+AgDoLvmKa1ECwEaq1cxNTSXJujfS1Z/njhxJqtWejwYAUEjyFQBAd8lXXIMSAK6hVa/n1MxMlsbGLtm+VKvl1MxMWvX6Fk0GAFBM8hUAQHfJV2xkYKsHgCJo1etpHTiQ4UYjA/PzWR4dvXAJlQYVAOCGyFcAAN0lX3E1SgC4XtVqFvbv3+opAADKQ74CAOgu+YorUAIAAMXSbjuzBQCgm+QrgFJTAgAAhTEyO5vx6ekMNZtr2xZrtcxNTbnHJQDADZCvAMrPg4EBgEIYmZ3N3snJDF70ATVJBs+cyd7JyYzMzm7RZAAAxSRfAfQHJQAAsP212xmfnk46nVQue6nS6SRJxo8fT9rt3s8GAFBE8hVA31ACAADb3nCjkaFmc90H1FWVTidDc3MZbjR6OhcAQFHJVwD9QwkAAGx7A/PzXd0PAKDfyVcA/UMJAABse8ujo13dDwCg38lXAP1DCQAAbHsLExNZrNXSqVz5gvVOpZLF8fEsTEz0eDIAgGKSrwD6hxIAANj+qtXMTU0lyboPqqs/zx05klSrPR8NAKCQ5CuAvqEEAAAKoVWv59TMTJbGxi7ZvlSr5dTMTFr1+hZNBgBQTPIVQH8Y2OoBAACuV6teT+vAgQw3GhmYn8/y6OiFS9SdoQYAcEPkK4DyUwIAAMVSrWZh//6tngIAoDzkK4BSUwIA/aHddmYLAEA3yVcAAN21SflKCQCU3sjsbManpzPUbK5tW6zVMjc15R6XAAA3QL4CAOiuzcxXHgwMlNrI7Gz2Tk5m8KI30CQZPHMmeycnMzI7u0WTAQAUk3wFANBdm52vlABAebXbGZ+eTjqdVC57qdLpJEnGjx9P2u3ezwYAUETyFQBAd/UgXykBgNIabjQy1GyuewNdVel0MjQ3l+FGo6dzAQAUlXwFANBdvchXSgCgtAbm57u6HwBAv5OvAAC6qxf5SgkAlNby6GhX9wMA6HfyFQBAd/UiXykBgNJamJjIYq2WTuXKF1R1KpUsjo9nYWKix5MBABSTfAUA0F29yFdKAKC8qtXMTU0lybo30tWf544cSarVno8GAFBI8hUAQHf1IF8pAYBSa9XrOTUzk6WxsUu2L9VqOTUzk1a9vkWTAQAUk3wFANBdm52vBv5fRwMUQKteT+vAgQw3GhmYn8/y6OiFS6icoQYAcEPkKwCA7trMfFXpdDqdLswIAAAAAABsM24HBAAAAAAAJaUEAAAAAACAklICAAAAAABASSkBAAAAAACgpJQAAAAAAABQUkoAAAAAAAAoKSUAAAAAAACUlBIAAAAAAABKSgkAAAAAAAAl9T9eXdosLuNefgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_offset_per_dot_with_positions(summary_df, measurement='Offset', screen_width=192, screen_height=1080):\n", "    \"\"\"\n", "    Plots offset across participants for each dot with a small illustration under each box plot\n", "    indicating each dot's position on the screen.\n", "    \n", "    Parameters:\n", "    - summary_df (pd.DataFrame): Dataframe containing offset data for each dot and participant.    \n", "    - screen_width (int): Width of the screen (for relative positioning).\n", "    - screen_height (int): Height of the screen (for relative positioning).\n", "    \"\"\"\n", "    \n", "    # Set up the main figure and the box plot\n", "    plt.figure(figsize=(16, 10))  # Larger figure for readability\n", "    main_ax = plt.gca()\n", "    \n", "    # Increase font sizes for labels, ticks, and title\n", "    main_ax.set_xlabel('Unique Dot', fontsize=18, labelpad=10)\n", "    main_ax.set_ylabel(f'{measurement} (cm)', fontsize=18, labelpad=10)\n", "    main_ax.set_title(f'{measurement} per Dot Across Participants', fontsize=20, weight='bold', pad=15)\n", "    main_ax.tick_params(axis='both', labelsize=12)\n", "    \n", "    # Set y-ticks for clearer reading\n", "    plt.yticks([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])\n", "    \n", "    # Box plot with enhanced appearance\n", "    palette = sns.color_palette(\"Set2\", len(summary_df['unique_dot'].unique()))\n", "    if measurement == 'Offset':\n", "        sns.boxplot(x='unique_dot', y='median_offset_cm', data=summary_df, hue='unique_dot', palette=palette, ax=main_ax, linewidth=1.5)\n", "    elif measurement == 'SD':\n", "        sns.boxplot(x='unique_dot', y='eucl_dist_gaze_to_median_cm', data=summary_df, hue='unique_dot', palette=palette, ax=main_ax, linewidth=1.5)\n", "    elif measurement == 'RMS':\n", "        sns.boxplot(x='unique_dot', y='rms_error_cm', data=summary_df, hue='unique_dot', palette=palette, ax=main_ax, linewidth=1.5)\n", "    \n", "    main_ax.get_legend().remove()  # Remove legend if unnecessary\n", "\n", "    # Draw small position illustrations below each unique dot box\n", "    unique_dots = summary_df['unique_dot'].unique()\n", "    for i, dot in enumerate(unique_dots):\n", "        dot_data = summary_df[summary_df['unique_dot'] == dot]\n", "        x_avg = dot_data['accprecX'].mean()\n", "        y_avg = dot_data['accprecY'].mean()\n", "        \n", "        # Define inset axes positions for more space\n", "        inset_ax = main_ax.inset_axes([i / len(unique_dots) + 0.01, -0.25, 0.9 / len(unique_dots), 0.1])\n", "        \n", "        # Draw rectangle and plot dot’s position\n", "        inset_ax.add_patch(patches.Rectangle((0, 0), screen_width, screen_height, edgecolor='gray', facecolor='lightgray', linewidth=0.5))\n", "        inset_ax.plot(x_avg / 1920 * screen_width, y_avg / 1080 * screen_height, 'ro', markersize=6)\n", "        \n", "        # Customize the inset plot appearance\n", "        inset_ax.set_xlim(0, screen_width)\n", "        inset_ax.set_ylim(0, screen_height)\n", "        inset_ax.invert_yaxis()\n", "        inset_ax.axis('off')  # Hide inset plot axes for clarity\n", "\n", "    # Layout adjustments for clarity\n", "    plt.tight_layout(pad=3)\n", "    plt.subplots_adjust(top=0.85, bottom=0.25)\n", "    plt.show()\n", "\n", "# Example usage:\n", "# plot_offset_per_dot_with_positions(summary_df, measurement='Offset', screen_width=192, screen_height=1080)\n", "\n", "\n", "summary_df_per_dot= summary_df.groupby(['pp_id','unique_dot', 'accprecX', 'accprecY'])[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()\n", "max_value = summary_df_per_dot['median_offset_cm'].max()\n", "max_index = summary_df_per_dot['median_offset_cm'].idxmax()\n", "print(\"Maximum value:\", max_value)\n", "print(\"Index of maximum value:\", max_index)\n", "\n", "plot_offset_per_dot_with_positions(summary_df_per_dot, measurement='Offset')\n", "plot_offset_per_dot_with_positions(summary_df_per_dot, measurement='SD')\n", "plot_offset_per_dot_with_positions(summary_df_per_dot, measurement='RMS')"]}, {"cell_type": "code", "execution_count": 39, "id": "286943d2", "metadata": {}, "outputs": [], "source": ["# Group dots by the column and row they are in\n", "_, col_indices = np.unique(summary_df_per_dot[['accprecX']], axis=0, return_inverse=True)\n", "summary_df_per_dot['unique_column'] = col_indices\n", "_, row_indices = np.unique(summary_df_per_dot[['accprecY']], axis=0, return_inverse=True)\n", "summary_df_per_dot['unique_row'] = row_indices\n"]}, {"cell_type": "code", "execution_count": 40, "id": "8ddb067b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_matrix(df, measurement='median_offset_cm', title='Average Median Offset Across Positions'):\n", "    # Calculate the average median offset for each dot across all subjects\n", "    average_per_dot = df.groupby('unique_dot', as_index=False)[measurement].mean()\n", "    # print(f'Average dot: {average_per_dot}')\n", "\n", "    # Create a 5x5 matrix with NaN values\n", "    dot_matrix = pd.DataFrame(np.nan, index=range(5), columns=range(5))\n", "\n", "    # Fill only 13 cells with values from the dataset\n", "    # Adjust this mapping as per the desired layout of filled cells\n", "    positions_to_fill = [\n", "        (0, 0), (0, 2), (0, 4),\n", "        (1, 1), (1, 3),\n", "        (2, 0), (2, 2), (2, 4),\n", "        (3, 1), (3, 3),\n", "        (4, 0), (4, 2), (4, 4)\n", "    ]\n", "\n", "    # Dot position data are numbered in columns, thus flip rows and columns here\n", "    for idx, (row, col) in enumerate(positions_to_fill):\n", "        if idx < len(average_per_dot):\n", "            dot_matrix.iloc[col, row] = average_per_dot[measurement].values[idx]\n", "\n", "    # Set up the plot\n", "    plt.figure(figsize=(8, 8))\n", "    heatmap = sns.heatmap(dot_matrix, annot=True, fmt=\".2f\", cmap=\"YlGnBu\",\n", "                          square=True, annot_kws={\"size\": 12}, linewidths=0.5,\n", "                          linecolor='gray', cbar_kws={'label': measurement})\n", "\n", "    # Customize title and labels for readability\n", "    plt.title(title, fontsize=16, weight='bold', pad=12)\n", "    plt.xlabel('Column', fontsize=14, labelpad=10)\n", "    plt.ylabel('Row', fontsize=14, labelpad=10)\n", "\n", "    # Set tick labels for rows and columns for clarity\n", "    heatmap.set_xticklabels(range(1, 6), fontsize=12)\n", "    heatmap.set_yticklabels(range(1, 6), fontsize=12, rotation=0)\n", "\n", "    # Customize colorbar label size\n", "    heatmap.collections[0].colorbar.ax.tick_params(labelsize=12)\n", "\n", "    # Adjust layout for a clean look\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Call the updated function\n", "plot_matrix(summary_df_per_dot, measurement='median_offset_cm', title='Offset (cm) Across Positions')\n", "plot_matrix(summary_df_per_dot, measurement='eucl_dist_gaze_to_median_cm', title='SD (cm) Across Positions')\n", "plot_matrix(summary_df_per_dot, measurement='rms_error_cm', title='RMS error (cm) Across Positions')"]}, {"cell_type": "code", "execution_count": null, "id": "8d0c87b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "deepeye_analyze", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}