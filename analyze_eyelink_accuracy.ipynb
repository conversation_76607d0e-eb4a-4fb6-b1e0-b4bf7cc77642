import os
import ast
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.image as mpimg
import astropy.convolution as krn
import pandas as pd
import numpy as np
from matplotlib import cm
import scipy.stats as stats
import seaborn as sns
import statsmodels.api as sm
from statsmodels.stats.anova import AnovaRM
import csv
from deepeye_analysis_package.plotting import makeHeat

folder_path = "/Users/<USER>/Documents/Bewegingswetenschappen/BOP/MullerLyer_eyelink/CollectedData/eyelink_data/CSV's"
file_names = [fn for fn in os.listdir(folder_path) if fn.endswith(".csv")]
pp_list = []


# Define target resolution, since datasets may have different screen resolutions
TARG_RES = True
if TARG_RES:
    target_resX, target_resY = 1920, 1080
else:
    target_resX, target_resY = None, None

scrW_cm = 50.37037037037037
required_dots = 13

def is_float(x):
    try:
        float(x)
        return True
    except:
        return False

for fn in file_names:
    path = os.path.join(folder_path, fn)
    try:
        # Detecteer delimiter automatisch
        with open(path, 'r') as f:
            sample = f.read(2048)
            delimiter = csv.Sniffer().sniff(sample).delimiter

        # Lees bestand in met correcte delimiter
        df = pd.read_csv(path, sep=delimiter, dtype=str)

        # Controleer op benodigde kolommen
        if 'CURRENT_FIX_X' not in df.columns or 'CURRENT_FIX_Y' not in df.columns:
            print(f"⚠️  {fn} mist fixatiekolommen")
            continue

        # Filter lege waarden en converteer
        df = df[df['CURRENT_FIX_X'].apply(is_float) & df['CURRENT_FIX_Y'].apply(is_float)]
        df['user_pred_px_x'] = df['CURRENT_FIX_X'].astype(float)
        df['user_pred_px_y'] = df['CURRENT_FIX_Y'].astype(float)

        # Voeg schermresolutie toe
        df['resX'] = target_resX
        df['resY'] = target_resY
        df['scrW_cm'] = scrW_cm
        df['scale_cm_in_px'] = scrW_cm / target_resX

        # Check en verwerk accprecX/Y
        if 'accprecX' not in df.columns or 'accprecY' not in df.columns:
            print(f"⚠️  {fn} mist accuratiedoelen")
            continue

        df = df[df['accprecX'].apply(is_float) & df['accprecY'].apply(is_float)]
        df['accprecX'] = df['accprecX'].astype(float)
        df['accprecY'] = df['accprecY'].astype(float)

        # Duplicaten verwijderen (vaak door webcam-‘stutter’)
        df = df.drop_duplicates(subset=['user_pred_px_x', 'user_pred_px_y'], ignore_index=True)

        # Bepaal unieke targets
        _, indices = np.unique(df[['accprecX', 'accprecY']], axis=0, return_inverse=True)
        df['unique_dot'] = indices

        # Sla deelnemer over als er te weinig unieke targets zijn
        if df[['accprecX', 'accprecY']].drop_duplicates().shape[0] < required_dots:
            print(f"⏭️  {fn} heeft minder dan {required_dots} unieke stippen")
            continue

        pp_list.append(df)

    except Exception as e:
        print(f"❌ Fout bij verwerken van {fn}: {e}")
        continue

# Combineer alle datasets
if pp_list:
    all_df = pd.concat(pp_list, ignore_index=True)
    print(f"✅ Verwerkt: {len(pp_list)} bestanden, totaal {len(all_df)} rijen")
else:
    print("❌ Geen valide data gevonden.")

all_df.to_csv('outputeyelink.csv', index=False)

def plot_heatmap(df, target_resX, target_resY):
    # Generate heatmap for all gaze points
    heatmap = makeHeat([target_resX, target_resY], df['user_pred_px_x'].values, df['user_pred_px_y'].values)
    
    # Calculate median gaze predictions and ground truth per dot
    median_pred = df.groupby('unique_dot')[['user_pred_px_x', 'user_pred_px_y']].median()
    median_pred.columns = ['median_pred_x', 'median_pred_y']
    
    true_pos = df.groupby('unique_dot')[['accprecX', 'accprecY']].mean()
    
    # Calculate offset between median gaze predictions and ground truth, then convert to cm
    offset_px = np.linalg.norm(median_pred.values - true_pos.values, axis=1)
    # new scaling factor is used to convert px to cm for scaled data
    offset_cm = offset_px * df['scale_cm_in_px'].iloc[0]
    
    # Calculate Euclidean distance from each gaze point to the median prediction for each dot
    df = df.merge(median_pred, on='unique_dot', how='left')

    df['eucl_dist_gaze_to_median_px'] = np.linalg.norm(
        df[['user_pred_px_x', 'user_pred_px_y']].values - df[['median_pred_x', 'median_pred_y']].values,
        axis=1
    )    
    
    # Calculate the mean Euclidean distance per dot and convert to cm for standard deviation (SD)
    SD_px = df.groupby('unique_dot')['eucl_dist_gaze_to_median_px'].mean()
    # new scaling factor is used to convert px to cm for scaled data
    SD_cm = SD_px * df['scale_cm_in_px'].iloc[0]

    # Plot heatmap and ground truth vs. predicted gaze points
    plt.figure()
    plt.imshow(heatmap, cmap=cm.hot, extent=[0, target_resX, target_resY, 0], alpha=0.5, aspect='equal')
    
    # Plot ground truth positions (green) and median predicted gaze (blue)
    plt.scatter(true_pos['accprecX'], true_pos['accprecY'], c='g', s=40, alpha=0.5, label='Ground Truth')
    plt.scatter(median_pred['median_pred_x'], median_pred['median_pred_y'], c='b', s=40, alpha=0.5, label='Predicted Gaze')
    
    # Draw lines between ground truth and predictions for visualizing offsets
    plt.plot([median_pred['median_pred_x'], true_pos['accprecX']], [median_pred['median_pred_y'], true_pos['accprecY']], c='black')
            
    # Title and statistics in the legend
    plt.title(f'N={df["RECORDING_SESSION_LABEL"].nunique()}\n Offset: {offset_cm.mean():.1f}cm | SD: {SD_cm.mean():.1f}cm', fontsize=12)
    
    # Annotate offset values near each ground truth dot position
    for (x, y, e) in zip(true_pos['accprecX'], true_pos['accprecY'], offset_cm.round(1)):
        plt.text(x, y, str(e), fontsize=10, color='black')
    
    # Add legend and final touches
    plt.suptitle('Calibration (All Participants, EyeLink)', fontsize=14)
    plt.subplots_adjust(top=0.97)
    plt.legend(loc='upper right', bbox_to_anchor=(1.1, 1.2))
    
    # Show the plot
    plt.show()

# Call the function with the dataframe
plot_heatmap(all_df, target_resX, target_resY)

def compute_rms(samples):
    # Assuming `samples` is a list of tuples, each containing (x, y) coordinates
    deltas = np.diff(samples, axis=0)
    distances = np.linalg.norm(deltas, axis=1)
    rms = np.sqrt(np.mean(distances**2))
    return rms

# Prepare summary data across subjects
summary_data = []
skip = []

# Calculate metrics for each subject and unique dot
for _, group in all_df.groupby(['RECORDING_SESSION_LABEL', 'unique_dot']):
    if _[0] in skip:
        continue
    if group.empty or len(group) < 2:
        skip.append(_[0])
        # print(_, "overslaan")
        continue  # Sla over als er geen of te weinig samples zijn

    # Compute median gaze for each unique dot
    median_x = group['user_pred_px_x'].median()
    median_y = group['user_pred_px_y'].median()
    
    # Sort by timestamp to ensure temporal order
    group.sort_values('IP_START_TIME', inplace=True)    

    # Compute Euclidean distances
    gaze_coords = group[['user_pred_px_x', 'user_pred_px_y']].to_numpy()
    median_coords = np.array([median_x, median_y])
    
    # Distance from gaze samples to median gaze (precision)
    group['eucl_dist_gaze_to_median_px'] = np.linalg.norm(gaze_coords - median_coords, axis=1)
    group['eucl_dist_gaze_to_median_cm'] = group['eucl_dist_gaze_to_median_px'] * all_df['scale_cm_in_px'].iloc[0]
    
    # Offset from median gaze to ground truth (accuracy)
    ground_truth_coords = group[['accprecX', 'accprecY']].to_numpy()
    # print(ground_truth_coords.shape)
    group['median_offset_px'] = np.linalg.norm(median_coords - ground_truth_coords, axis=1)
    group['median_offset_cm'] = group['median_offset_px'] * all_df['scale_cm_in_px'].iloc[0]

    # RMS error sample-to-sample (temproal precision)
    # print(gaze_coords.shape)
    group['rms_error_px'] = compute_rms(gaze_coords)
    group['rms_error_cm'] = group['rms_error_px'] * all_df['scale_cm_in_px'].iloc[0]
    
    summary_data.append(group)

# Combine data across all subjects and calculate mean precision (SD) and accuracy (offset)
summary_df = pd.concat(summary_data)
print(skip)

def plot_measurement_per_participant(df, measurement='median_offset_cm', name='', color='b'):
    # Compact figure for matching inset plot style
    fig, ax = plt.subplots(figsize=(4, 6))  # Smaller size for side-by-side presentation

    # Calculate measurement per participant and mean value
    measurement_pp = df.groupby('RECORDING_SESSION_LABEL', as_index=False)[measurement].mean()
    mean_value = measurement_pp[measurement].mean()

    # Simplified box plot to match inset style
    sns.boxplot(y=measurement_pp[measurement], ax=ax, color=color)
    # sns.rugplot(y=measurement_pp[measurement], ax=ax, height=0.05, color=color)
    # ax.axhline(mean_value, color='k', linestyle='--', linewidth=1.5, label=f'Mean: {mean_value:.2f}')
    
    # Set y-axis limits and label
    ax.set_ylim(-0.5, 5)
    ax.set_ylabel(f'{name}', fontsize=16, labelpad=6)
    ax.tick_params(axis='both', labelsize=10)
    
    # Simplify layout: remove x-axis label and set title within the plot
    ax.set_xlabel('')
    ax.set_title(f'Mean: {mean_value:.2f}', fontsize=18, weight='bold', pad=8)
    
    # Compact layout adjustments
    fig.tight_layout()
    plt.show()

    # Uncomment the line below to save the plot
    # fig.savefig(f'{name}_summary.jpg', dpi=1000)


summary_df_per_pp= summary_df.groupby(['RECORDING_SESSION_LABEL'])[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()

plot_measurement_per_participant(summary_df_per_pp, measurement='median_offset_cm', name='Offset (cm)', color='b')
plot_measurement_per_participant(summary_df_per_pp, measurement='eucl_dist_gaze_to_median_cm', name='SD (cm)', color='orange')
plot_measurement_per_participant(summary_df_per_pp, measurement='rms_error_cm', name='RMS Error (cm)', color='g')

# Screen Parameters (ASSUMING SAME PHYSICAL SCREEN AS WEBCAM STUDY)
PHYSICAL_SCREEN_WIDTH_CM = 34.4
PHYSICAL_SCREEN_HEIGHT_CM = 19.3
EYELINK_SCREEN_WIDTH_PX = 1920
EYELINK_SCREEN_HEIGHT_PX = 1080
# Kijkafstand = 60 cm (not directly used in these specific cm conversions, but good for context)

print(f"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm")
print(f"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm")
print(f"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px")

# File Path (pas dit aan indien nodig)
path = './CollectedData/complete/eyelink_data'
path_to_data = os.path.join(path, 'data')
path_to_analysis = os.path.join(path, 'analysis')
folder_names = [
    name for name in os.listdir(path_to_folders)
    if os.path.isdir(os.path.join(path_to_folders, name)) and not name.startswith('.')

try:
    for fn in folder_names
        # Specify engine='openpyxl' if pandas has trouble with .xls
        df_eyelink_raw = pd.read_excel(path_to_folders) # Add engine='openpyxl' if needed
        print(f"Successfully loaded data from {file_path}. Shape: {df_eyelink_raw.shape}")
        # Toon de eerste paar rijen om de kolomnamen te verifiëren
        print("First 5 rows of the loaded data:")
        print(df_eyelink_raw.head())
        print("\nColumn names:")
        print(df_eyelink_raw.columns.tolist())
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        df_eyelink_raw = None # Ensure it's defined for later checks
    except Exception as e:
        print(f"An error occurred while loading the Excel file: {e}")
        df_eyelink_raw = None

df_accprec = None # Initialize
if df_eyelink_raw is not None:
    # Ensure 'arrowLength' column exists
    if 'arrowLength' not in df_eyelink_raw.columns:
        print("Error: Column 'arrowLength' not found in the Excel file.")
        print("Please verify column names from the output above.")
    else:
        # Convert 'arrowLength' to string to handle potential mixed types or numbers read as float
        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)
        # Filter rows where 'arrowLength' is "UNDEFINED"
        # Also handle cases like "UNDEFINED.0" if it was read as float then string
        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith("UNDEFINED")].copy()

        if df_accprec.empty:
            print("No data found for accuracy/precision task (where 'arrowLength' starts with 'UNDEFINED').")
        else:
            print(f"Filtered for accuracy/precision task. Shape: {df_accprec.shape}")

            # Select and ensure necessary columns are present and not NaN
            required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']
            missing_cols = [col for col in required_cols if col not in df_accprec.columns]
            if missing_cols:
                print(f"Error: Required columns not found in the filtered data: {missing_cols}")
                df_accprec = pd.DataFrame() # Empty df if critical columns are missing
            else:
                df_accprec.dropna(subset=required_cols, inplace=True)
                if df_accprec.empty:
                    print(f"No valid data after dropping NaNs from required columns: {required_cols}")
                else:
                    print(f"Data shape after dropping NaNs in critical columns: {df_accprec.shape}")
else:
    print("Skipping filtering as data loading failed.")

if df_accprec is not None and not df_accprec.empty:
    df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
    df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
    df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
    df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
    print("Converted pixel coordinates to cm.")
    # print(df_accprec[['CURRENT_FIX_X', 'gaze_x_cm', 'accprecX', 'target_x_cm']].head())
else:
    print("Skipping coordinate conversion as no valid data is available.")

{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# EyeLink Accuracy/Precision Analysis (Batch Processing)\n",
    "\n",
    "Dit notebook analyseert de accuracy en precisie data verkregen van een EyeLink 1000 eyetracker voor meerdere participanten. Het itereert door een mappenstructuur, berekent mediaan offset, standaarddeviatie van gaze (als maat voor precisie), en RMS error per participant, en visualiseert deze in heatmaps."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Import Libraries"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import os\n",
    "import openpyxl # Nodig voor pd.read_excel om .xls bestanden te lezen"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Helper Functions\n",
    "Definities van functies voor pixel-naar-cm conversie, RMS berekening, statistieken per doelpunt, en het plotten van de heatmatrix."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):\n",
    "    \"\"\"Converts pixel values to centimeters.\"\"\"\n",
    "    return (pixels / screen_dimension_pixels) * physical_screen_dimension_cm"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def compute_rms(samples_cm):\n",
    "    \"\"\"Computes Root Mean Square of sequential differences in samples.\"\"\"\n",
    "    if not isinstance(samples_cm, np.ndarray):\n",
    "        samples_cm = np.array(samples_cm)\n",
    "    \n",
    "    if samples_cm.ndim == 1: # Handle case where samples might be passed incorrectly\n",
    "        if len(samples_cm) < 2 : # if it's a flat list of x,y,x,y...\n",
    "             return np.nan\n",
    "        # try to reshape if it's like [x1,y1,x2,y2,...]\n",
    "        if len(samples_cm) % 2 == 0:\n",
    "            samples_cm = samples_cm.reshape(-1,2)\n",
    "        else: # cannot form pairs\n",
    "            return np.nan\n",
    "\n",
    "    if samples_cm.shape[0] < 2: # Need at least two points to compute differences\n",
    "        return np.nan\n",
    "        \n",
    "    deltas = np.diff(samples_cm, axis=0)\n",
    "    if deltas.shape[0] == 0: # No differences computed\n",
    "        return np.nan\n",
    "        \n",
    "    distances_sq = np.sum(deltas**2, axis=1)\n",
    "    rms = np.sqrt(np.mean(distances_sq))\n",
    "    return rms"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):\n",
    "    \"\"\"\n",
    "    Calculates median offset, precision (mean Euclidean distance to median gaze),\n",
    "    and RMS error for a given target dot.\n",
    "    \"\"\"\n",
    "    if not gaze_points_for_dot_cm:\n",
    "        return {\n",
    "            'dot_id': dot_id,\n",
    "            'median_offset_cm': np.nan,\n",
    "            'eucl_dist_gaze_to_median_cm': np.nan,\n",
    "            'rms_error_cm': np.nan,\n",
    "            'num_fixations': 0\n",
    "        }\n",
    "\n",
    "    gaze_array_cm = np.array(gaze_points_for_dot_cm)\n",
    "    \n",
    "    median_gaze_x_cm = np.median(gaze_array_cm[:, 0])\n",
    "    median_gaze_y_cm = np.median(gaze_array_cm[:, 1])\n",
    "    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)\n",
    "\n",
    "    offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - np.array(target_coords_cm))\n",
    "\n",
    "    distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm]\n",
    "    precision_metric_cm = np.mean(distances_to_median_cm)\n",
    "    \n",
    "    rms_cm = compute_rms(gaze_array_cm)\n",
    "\n",
    "    return {\n",
    "        'dot_id': dot_id,\n",
    "        'median_offset_cm': offset_cm,\n",
    "        'eucl_dist_gaze_to_median_cm': precision_metric_cm,\n",
    "        'rms_error_cm': rms_cm,\n",
    "        'num_fixations': len(gaze_points_for_dot_cm)\n",
    "    }"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def plot_matrix(df_summary, measurement, title_prefix):\n",
    "    \"\"\"Plots a 5x5 heatmap for the given measurement.\"\"\"\n",
    "    dot_matrix_data = np.full((5, 5), np.nan)\n",
    "    # Maak een meer beschrijvende titel gebaseerd op de 'measurement' kolomnaam\n",
    "    if measurement == 'median_offset_cm':\n",
    "        m_title = 'Offset'\n",
    "    elif measurement == 'eucl_dist_gaze_to_median_cm':\n",
    "        m_title = 'SD (Dispersion around Median Gaze)'\n",
    "    elif measurement == 'rms_error_cm':\n",
    "        m_title = 'RMS Error (Sample-to-Sample Stability)'\n",
    "    else:\n",
    "        m_title = measurement.replace('_cm', '').replace('_', ' ').title()\n",
    "    \n",
    "    title = f\"{title_prefix}: {m_title} (cm)\"\n",
    "    \n",
    "    for i in range(5): # Rows\n",
    "        for j in range(5): # Columns\n",
    "            dot_index = i * 5 + j\n",
    "            if dot_index in df_summary['dot_id'].values:\n",
    "                value = df_summary.loc[df_summary['dot_id'] == dot_index, measurement].iloc[0]\n",
    "                dot_matrix_data[i, j] = value\n",
    "\n",
    "    plt.figure(figsize=(10, 8))\n",
    "    sns.heatmap(dot_matrix_data, annot=True, fmt=\".2f\", cmap=\"viridis\",\n",
    "                square=True, annot_kws={\"size\": 10}, linewidths=.5,\n",
    "                cbar_kws={'label': f\"{m_title} (cm)\"})\n",
    "    plt.title(title, fontsize=16)\n",
    "    plt.xlabel(\"Grid Column Index\", fontsize=12)\n",
    "    plt.ylabel(\"Grid Row Index\", fontsize=12)\n",
    "    plt.xticks(np.arange(5) + 0.5, labels=np.arange(1, 6))\n",
    "    plt.yticks(np.arange(5) + 0.5, labels=np.arange(1, 6), rotation=0)\n",
    "    plt.tight_layout()\n",
    "    plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Main Analysis Script (Batch Processing)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 3.1. Define Base Path and Parameters"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Screen Parameters (ASSUMING SAME PHYSICAL SCREEN AS WEBCAM STUDY)\n",
    "PHYSICAL_SCREEN_WIDTH_CM = 34.4\n",
    "PHYSICAL_SCREEN_HEIGHT_CM = 19.3\n",
    "EYELINK_SCREEN_WIDTH_PX = 1920\n",
    "EYELINK_SCREEN_HEIGHT_PX = 1080\n",
    "# Kijkafstand = 60 cm (not directly used in these specific cm conversions, but good for context)\n",
    "\n",
    "print(f\"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm\")\n",
    "print(f\"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm\")\n",
    "print(f\"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px\")\n",
    "\n",
    "# Basispad naar de map die de data van alle participanten bevat\n",
    "# Pas dit pad aan naar de locatie van je 'CollectedData/eyelink_data/' map\n",
    "base_data_path = './CollectedData/eyelink_data/' \n",
    "print(f\"Base data path: {base_data_path}\")\n",
    "\n",
    "# Verkrijg alle mapnamen (participant IDs) in de base_data_path\n",
    "try:\n",
    "    folder_names = [\n",
    "        name for name in os.listdir(base_data_path)\n",
    "        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.') # Negeer verborgen mappen\n",
    "    ]\n",
    "    if not folder_names:\n",
    "        print(f\"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'. Controleer het pad.\")\n",
    "    else:\n",
    "        print(f\"Gevonden participant mappen: {sorted(folder_names)}\") # Sorteer voor consistente volgorde\n",
    "except FileNotFoundError:\n",
    "    print(f\"FOUT: Basis data pad niet gevonden: '{base_data_path}'. Controleer het pad.\")\n",
    "    folder_names = [] # Zorg dat folder_names gedefinieerd is om latere fouten te voorkomen"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 3.2. Loop Through Participants and Analyze Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "all_participants_summaries = [] # Lijst om DataFrames van alle participanten op te slaan\n",
    "\n",
    "if not folder_names:\n",
    "    print(\"Geen participant mappen om te verwerken. Script stopt.\")\n",
    "else:\n",
    "    for participant_id in sorted(folder_names): # Verwerk in gesorteerde volgorde\n",
    "        print(f\"\\n--- Processing Participant: {participant_id} ---\")\n",
    "        \n",
    "        file_path = os.path.join(base_data_path, participant_id, \"Output\", f\"{participant_id}.xls\")\n",
    "        print(f\"Pogen data te laden van: {file_path}\")\n",
    "\n",
    "        # 3.2.1. Load Data\n",
    "        try:\n",
    "            df_eyelink_raw = pd.read_excel(file_path) \n",
    "            print(f\"Succesvol data geladen voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n",
    "        except FileNotFoundError:\n",
    "            print(f\"FOUT: Bestand niet gevonden voor participant {participant_id} op {file_path}. Sla deze participant over.\")\n",
    "            continue # Ga naar de volgende participant\n",
    "        except Exception as e:\n",
    "            print(f\"Een fout trad op bij het laden van het Excel-bestand voor {participant_id}: {e}. Sla deze participant over.\")\n",
    "            continue\n",
    "\n",
    "        # 3.2.2. Filter Data for Accuracy/Precision Task\n",
    "        if 'arrowLength' not in df_eyelink_raw.columns:\n",
    "            print(f\"FOUT: Kolom 'arrowLength' niet gevonden voor participant {participant_id}. Sla over.\")\n",
    "            continue\n",
    "        \n",
    "        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)\n",
    "        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith(\"UNDEFINED\")].copy()\n",
    "\n",
    "        if df_accprec.empty:\n",
    "            print(f\"Geen accuracy/precision data (met 'arrowLength'=\"UNDEFINED\") gevonden voor {participant_id}. Sla over.\")\n",
    "            continue\n",
    "        \n",
    "        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']\n",
    "        missing_cols = [col for col in required_cols if col not in df_accprec.columns]\n",
    "        if missing_cols:\n",
    "            print(f\"FOUT: Vereiste kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.\")\n",
    "            continue\n",
    "            \n",
    "        df_accprec.dropna(subset=required_cols, inplace=True)\n",
    "        if df_accprec.empty:\n",
    "            print(f\"Geen valide data na verwijderen van NaNs in vereiste kolommen voor {participant_id}. Sla over.\")\n",
    "            continue\n",
    "        print(f\"Gefilterde acc/prec data voor {participant_id}. Vorm: {df_accprec.shape}\")\n",
    "\n",
    "        # 3.2.3. Convert Pixel Coordinates to CM\n",
    "        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n",
    "        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n",
    "        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n",
    "        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n",
    "        print(f\"Pixelcoördinaten omgerekend naar cm voor {participant_id}.\")\n",
    "\n",
    "        # 3.2.4. Process Data per Target Dot\n",
    "        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)\n",
    "        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)\n",
    "\n",
    "        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]\n",
    "        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])\n",
    "        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)\n",
    "        unique_targets_cm['dot_id'] = unique_targets_cm.index\n",
    "\n",
    "        if len(unique_targets_cm) == 0:\n",
    "            print(f\"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.\")\n",
    "            continue\n",
    "        if len(unique_targets_cm) != 25:\n",
    "             print(f\"WAARSCHUWING voor {participant_id}: Verwachtte 25 unieke doelposities, gevonden {len(unique_targets_cm)}.\")\n",
    "        \n",
    "        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], \n",
    "                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')\n",
    "        \n",
    "        current_participant_dots_summary_list = []\n",
    "        for dot_id_val, group in df_accprec.groupby('dot_id'):\n",
    "            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]\n",
    "            if target_info.empty:\n",
    "                print(f\"WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val} voor pp {participant_id}\")\n",
    "                continue\n",
    "            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])\n",
    "            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))\n",
    "            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)\n",
    "            current_participant_dots_summary_list.append(summary)\n",
    "        \n",
    "        summary_df_per_dot = pd.DataFrame(current_participant_dots_summary_list)\n",
    "        \n",
    "        if summary_df_per_dot.empty:\n",
    "            print(f\"Kon geen samenvattende statistieken berekenen voor {participant_id}. Sla plots over.\")\n",
    "            continue\n",
    "        \n",
    "        summary_df_per_dot['participant_id'] = participant_id # Voeg participant_id toe\n",
    "        all_participants_summaries.append(summary_df_per_dot) # Voeg toe aan de overall lijst\n",
    "            \n",
    "        print(f\"\\nSamenvattende statistieken per doelpunt voor {participant_id}:\")\n",
    "        # print(summary_df_per_dot.head()) # Haal commentaar weg om de eerste rijen te zien\n",
    "\n",
    "        # 3.2.5. Generate Heatmaps per Participant\n",
    "        summary_df_per_dot_plot = summary_df_per_dot.copy()\n",
    "        # Vul aan met NaNs als er minder dan 25 unieke dots waren, voor een volledige 5x5 plot\n",
    "        if len(summary_df_per_dot_plot) < 25:\n",
    "            all_possible_dot_ids = pd.DataFrame({'dot_id': range(25)})\n",
    "            summary_df_per_dot_plot = pd.merge(all_possible_dot_ids, summary_df_per_dot_plot, on='dot_id', how='left')\n",
    "        \n",
    "        summary_df_per_dot_plot = summary_df_per_dot_plot.sort_values(by='dot_id').reset_index(drop=True)\n",
    "\n",
    "        plot_matrix(summary_df_per_dot_plot, measurement='median_offset_cm', title_prefix=f\"EyeLink ({participant_id})\")\n",
    "        plot_matrix(summary_df_per_dot_plot, measurement='eucl_dist_gaze_to_median_cm', title_prefix=f\"EyeLink ({participant_id})\")\n",
    "        plot_matrix(summary_df_per_dot_plot, measurement='rms_error_cm', title_prefix=f\"EyeLink ({participant_id})\")\n",
    "        print(f\"Heatmaps succesvol gegenereerd voor {participant_id}.\")\n",
    "\n",
    "# Optioneel: Na de lus, werk met `all_participants_summaries` voor een gecombineerde analyse\n",
    "if all_participants_summaries:\n",
    "    final_summary_all_pps_df = pd.concat(all_participants_summaries, ignore_index=True)\n",
    "    print(\"\\n\\n--- Optioneel: Gecombineerde Samenvatting voor Alle Verwerkte Participanten (eerste 5 rijen) ---\")\n",
    "    print(final_summary_all_pps_df.head())\n",
    "    \n",
    "    # Voorbeeld: Bereken gemiddelde metrieken per dot_id over alle participanten\n",
    "    # grouped_summary = final_summary_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()\n",
    "    # print(\"\\n--- Optioneel: Gemiddelde metrieken per doelpunt over alle participanten ---\")\n",
    "    # print(grouped_summary)\n",
    "    \n",
    "    # Plot gemiddelde heatmaps (als je 'grouped_summary' berekent)\n",
    "    # if not grouped_summary.empty:\n",
    "    #    plot_matrix(grouped_summary, measurement='median_offset_cm', title_prefix=\"EyeLink (All PPs Average)\")\n",
    "    #    plot_matrix(grouped_summary, measurement='eucl_dist_gaze_to_median_cm', title_prefix=\"EyeLink (All PPs Average)\")\n",
    "    #    plot_matrix(grouped_summary, measurement='rms_error_cm', title_prefix=\"EyeLink (All PPs Average)\")\n",
    "else:\n",
    "    print(\"\\nGeen data van participanten succesvol verwerkt om een gecombineerde samenvatting te maken.\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.12"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}

if not summary_df_per_dot.empty:
    # Pad with NaNs if fewer than 25 dots were found, to make a 5x5 grid for plotting
    # This ensures plot_matrix function receives data that can be shaped or indexed up to 24.
    if len(summary_df_per_dot) < 25:
        print(f"\nPadding summary data for plotting as only {len(summary_df_per_dot)} dots had data.")
        all_possible_dot_ids = pd.DataFrame({'dot_id': range(25)})
        summary_df_per_dot_plot = pd.merge(all_possible_dot_ids, summary_df_per_dot, on='dot_id', how='left')
    else:
        summary_df_per_dot_plot = summary_df_per_dot.copy()
    
    # Ensure dot_id is sorted for plot_matrix logic if it relies on implicit order
    summary_df_per_dot_plot = summary_df_per_dot_plot.sort_values(by='dot_id').reset_index(drop=True)

    plot_matrix(summary_df_per_dot_plot, measurement='median_offset_cm', title='EyeLink: Offset (cm) Across Positions')
    plot_matrix(summary_df_per_dot_plot, measurement='eucl_dist_gaze_to_median_cm', title='EyeLink: SD (cm) Across Positions')
    plot_matrix(summary_df_per_dot_plot, measurement='rms_error_cm', title='EyeLink: RMS error (cm) Across Positions')
    print("\nSuccessfully generated heatmaps (if any data was processed).")
else:
    print("No summary data to plot.")