{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# EyeLink Accuracy/Precision Analysis (Batch Processing - Geaggregeerde Resultaten)\n", "\n", "Dit notebook analyseert de accuracy en precisie data verkregen van een EyeLink 1000 eyetracker voor meerdere participanten. \n", "Het itereert door een mappenstructuur, verz<PERSON><PERSON> de metrie<PERSON> (mediaan offset, <PERSON> van gaze, RMS error) per participant voor 13 referentiepunten. \n", "Vervolgens genereert het **geaggregeerde resultaten over alle participanten**:\n", "1.  5x5 'checkerboard' heatmaps gebaseerd op de **gemiddelde** metrieken per punt over alle participanten.\n", "2.  <PERSON><PERSON> met be<PERSON><PERSON><PERSON><PERSON><PERSON> statistieken over **alle meetpunten van alle participanten gecombineerd**.\n", "3.  <PERSON><PERSON> van de **overall gemiddelde** prestaties.\n", "4.  <PERSON><PERSON><PERSON><PERSON> <PERSON> de distribut<PERSON> van de metrieken over **alle meetpunten van alle participanten**.\n", "5.  <PERSON><PERSON> scatter<PERSON><PERSON> van offset vs. precisie voor **alle meetpunten van alle participanten**.\n", "6.  **Nieuwe plots:** Boxplots per referentiepunt die de verdeling van Offset, SD, en RMS **over alle participanten** tonen, inclusief individuele datapunten."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import openpyxl # Nodig voor .xlsx, xlrd voor .xls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions\n", "Definities van functies voor pixel-naar-cm conversie, R<PERSON> berekening, en statistieken per doelpunt."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):\n", "    \"\"\"Converts pixel values to centimeters.\"\"\"\n", "    pixels_numeric = pd.to_numeric(pixels, errors='coerce')\n", "    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:\n", "        return np.nan\n", "    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_rms(samples_cm):\n", "    \"\"\"Computes Root Mean Square of sequential differences in samples.\"\"\"\n", "    if not isinstance(samples_cm, np.ndarray):\n", "        samples_cm = np.array(samples_cm)\n", "    \n", "    if samples_cm.ndim == 1: \n", "        if len(samples_cm) < 2 : \n", "             return np.nan\n", "        if len(samples_cm) % 2 == 0:\n", "            samples_cm = samples_cm.reshape(-1,2)\n", "        else: \n", "            return np.nan\n", "\n", "    if samples_cm.shape[0] < 2: \n", "        return np.nan\n", "        \n", "    deltas = np.diff(samples_cm, axis=0)\n", "    if deltas.shape[0] == 0: \n", "        return np.nan\n", "        \n", "    deltas_cleaned = deltas[~np.isnan(deltas).any(axis=1)]\n", "    if deltas_cleaned.shape[0] == 0:\n", "        return np.nan\n", "        \n", "    distances_sq = np.sum(deltas_cleaned**2, axis=1)\n", "    rms = np.sqrt(np.mean(distances_sq))\n", "    return rms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):\n", "    \"\"\"\n", "    Calculates median offset, precision (mean Euclidean distance to median gaze),\n", "    and RMS error for a given target dot.\n", "    \"\"\"\n", "    if not gaze_points_for_dot_cm:\n", "        return {\n", "            'dot_id': dot_id,\n", "            'median_offset_cm': np.nan,\n", "            'eucl_dist_gaze_to_median_cm': np.nan,\n", "            'rms_error_cm': np.nan,\n", "            'num_fixations': 0\n", "        }\n", "\n", "    gaze_array_cm = np.array(gaze_points_for_dot_cm)\n", "    gaze_array_cm_cleaned = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]\n", "    \n", "    if gaze_array_cm_cleaned.shape[0] == 0:\n", "         return {\n", "            'dot_id': dot_id,\n", "            'median_offset_cm': np.nan,\n", "            'eucl_dist_gaze_to_median_cm': np.nan,\n", "            'rms_error_cm': np.nan,\n", "            'num_fixations': len(gaze_points_for_dot_cm)\n", "        }\n", "\n", "    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])\n", "    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])\n", "    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)\n", "\n", "    if np.isnan(target_coords_cm).any():\n", "        offset_cm = np.nan\n", "    else:\n", "        offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - np.array(target_coords_cm))\n", "\n", "    distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]\n", "    precision_metric_cm = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan\n", "    \n", "    rms_cm = compute_rms(gaze_array_cm_cleaned)\n", "\n", "    return {\n", "        'dot_id': dot_id,\n", "        'median_offset_cm': offset_cm,\n", "        'eucl_dist_gaze_to_median_cm': precision_metric_cm,\n", "        'rms_error_cm': rms_cm,\n", "        'num_fixations': len(gaze_points_for_dot_cm)\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_checkerboard_heatmap(df_aggregated_per_dot, measurement, title_prefix, positions_to_fill):\n", "    \"\"\"Plots a 5x5 checkerboard heatmap for the given measurement using specified point positions.\"\"\"\n", "    \n", "    if measurement == 'median_offset_cm':\n", "        m_title_suffix = 'Offset'\n", "        heatmap_title = f\"{title_prefix}: Offset (cm) Across Positions\"\n", "    elif measurement == 'eucl_dist_gaze_to_median_cm':\n", "        m_title_suffix = 'SD (Dispersion)'\n", "        heatmap_title = f\"{title_prefix}: SD (cm) Across Positions\"\n", "    elif measurement == 'rms_error_cm':\n", "        m_title_suffix = 'RMS Error'\n", "        heatmap_title = f\"{title_prefix}: RMS error (cm) Across Positions\"\n", "    else:\n", "        m_title_suffix = measurement.replace('_cm', '').replace('_', ' ').title()\n", "        heatmap_title = f\"{title_prefix}: {m_title_suffix} (cm) Across Positions\"\n", "    \n", "    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan))\n", "    df_aggregated_sorted = df_aggregated_per_dot.sort_values(by='dot_id').reset_index(drop=True)\n", "\n", "    for idx, (row_idx, col_idx) in enumerate(positions_to_fill):\n", "        # Check if the current dot_id (idx, assuming 0-12 mapping) exists in the aggregated data\n", "        if idx < len(df_aggregated_sorted) and df_aggregated_sorted.loc[idx, 'dot_id'] == idx:\n", "            value = df_aggregated_sorted.loc[idx, measurement]\n", "            dot_matrix.iloc[row_idx, col_idx] = value\n", "        # If dot_id in df_aggregated_sorted does not match idx, it means some dots are missing\n", "        # from the expected 0-12 sequence for the 13 points. For simplicity, we fill based on idx if present.\n", "        # A more robust mapping might be needed if dot_ids are not guaranteed to be 0-12 sequential.\n", "            \n", "    plt.figure(figsize=(8, 8))\n", "    heatmap_ax = sns.heatmap(dot_matrix, annot=True, fmt=\".2f\", cmap=\"YlGnBu\", \n", "                             square=True, annot_kws={\"size\": 12}, linewidths=0.5,\n", "                             linecolor='gray', cbar_kws={'label': f\"{m_title_suffix} (cm)\"})\n", "    plt.title(heatmap_title, fontsize=16, weight='bold', pad=12)\n", "    plt.xlabel('Column', fontsize=14, labelpad=10)\n", "    plt.ylabel('Row', fontsize=14, labelpad=10)\n", "    heatmap_ax.set_xticklabels(range(1, 6), fontsize=12)\n", "    heatmap_ax.set_yticklabels(range(1, 6), fontsize=12, rotation=0)\n", "    if heatmap_ax.collections:\n", "        heatmap_ax.collections[0].colorbar.ax.tick_params(labelsize=12)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Main Analysis Script (Batch Processing)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1. Define Base Path and Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PHYSICAL_SCREEN_WIDTH_CM = 34.4\n", "PHYSICAL_SCREEN_HEIGHT_CM = 19.3\n", "EYELINK_SCREEN_WIDTH_PX = 1920\n", "EYELINK_SCREEN_HEIGHT_PX = 1080\n", "\n", "print(f\"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm\")\n", "print(f\"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm\")\n", "print(f\"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px\")\n", "\n", "base_data_path = './CollectedData/eyelink_data/' \n", "print(f\"Base data path: {base_data_path}\")\n", "\n", "try:\n", "    folder_names = [\n", "        name for name in os.listdir(base_data_path)\n", "        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')\n", "    ]\n", "    if not folder_names:\n", "        print(f\"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'. Controleer het pad.\")\n", "    else:\n", "        print(f\"Gevonden participant mappen: {sorted(folder_names)}\")\n", "except FileNotFoundError:\n", "    print(f\"FOUT: Ba<PERSON> data pad niet gevonden: '{base_data_path}'. Controleer het pad.\")\n", "    folder_names = []\n", "\n", "# De<PERSON>ieer de posities voor de 13 punten in een 5x5 grid (0-indexed)\n", "# Dit is de AANGENOMEN checkerboard mapping. PAS AAN INDIEN DEZE NIET EXACT KLOPT MET DE WEBCAM ANALYSE!\n", "positions_to_fill_13_dots = [\n", "    (0,0), (0,2), (0,4),  # <PERSON>venste rij\n", "    (1,1), (1,3),          # Tweede rij\n", "    (2,0), (2,2), (2,4),  # <PERSON><PERSON><PERSON> rij\n", "    (3,1), (3,3),          # <PERSON><PERSON><PERSON> rij\n", "    (4,0), (4,2), (4,4)   # Onderste rij\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2. Loop Through Participants: Data Loading and Metric Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_participants_dot_summaries_list = [] # Lijst om DataFrames met dot summaries per participant op te slaan\n", "\n", "if not folder_names:\n", "    print(\"Geen participant mappen om te verwerken. Script stopt.\")\n", "else:\n", "    for participant_id in sorted(folder_names): \n", "        print(f\"\\n--- Processing Participant: {participant_id} ---\")\n", "        \n", "        file_path = os.path.join(base_data_path, participant_id, \"Output\", f\"{participant_id}.xls\")\n", "        print(f\"Pogen data te laden van: {file_path}\")\n", "\n", "        df_eyelink_raw = None\n", "        loaded_successfully = False\n", "        try:\n", "            print(f\"Poging 1: Laden als TSV... {file_path}\")\n", "            df_eyelink_raw = pd.read_csv(file_path, sep='\\t', low_memory=False)\n", "            print(f\"Succesvol geladen als TSV. Vorm: {df_eyelink_raw.shape}\")\n", "            loaded_successfully = True\n", "        except Exception:\n", "            try:\n", "                print(f\"Poging 2: Laden als CSV... {file_path}\")\n", "                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)\n", "                print(f\"Succesvol geladen als CSV. Vorm: {df_eyelink_raw.shape}\")\n", "                loaded_successfully = True\n", "            except Exception:\n", "                try:\n", "                    print(f\"Poging 3: <PERSON>den als Excel (.xls) met xlrd... {file_path}\")\n", "                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')\n", "                    print(f\"Succesvol geladen als Excel (.xls) met xlrd. Vorm: {df_eyelink_raw.shape}\")\n", "                    loaded_successfully = True\n", "                except Exception:\n", "                    try:\n", "                        print(f\"Poging 4: <PERSON>den als Excel (.xlsx style) met openpyxl... {file_path}\")\n", "                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')\n", "                        print(f\"Succesvol geladen als Excel (.xlsx style) met openpyxl. Vorm: {df_eyelink_raw.shape}\")\n", "                        loaded_successfully = True\n", "                    except Exception as e_final:\n", "                        print(f\"KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} ({e_final}). Sla over.\")\n", "                        continue \n", "\n", "        if not loaded_successfully or df_eyelink_raw is None:\n", "            continue\n", "\n", "        if 'arrowLength' not in df_eyelink_raw.columns:\n", "            print(f\"FOUT: <PERSON><PERSON><PERSON> 'arrowLength' niet gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)\n", "        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith(\"UNDEFINED\")].copy()\n", "\n", "        if df_accprec.empty:\n", "            print(f\"Geen accuracy/precision data gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']\n", "        missing_cols = [col for col in required_cols if col not in df_accprec.columns]\n", "        if missing_cols:\n", "            print(f\"FOUT: <PERSON>ere<PERSON><PERSON> kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        for col in required_cols:\n", "            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')\n", "            \n", "        df_accprec.dropna(subset=required_cols, inplace=True)\n", "        if df_accprec.empty:\n", "            print(f\"Geen valide data na numerieke conversie voor {participant_id}. Sla over.\")\n", "            continue\n", "\n", "        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "        \n", "        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']\n", "        df_accprec.dropna(subset=cm_cols_to_check, inplace=True)\n", "        if df_accprec.empty:\n", "            print(f\"Geen valide data na cm-conversie voor {participant_id}. Sla over.\")\n", "            continue\n", "\n", "        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)\n", "        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)\n", "\n", "        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]\n", "        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])\n", "        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)\n", "        unique_targets_cm['dot_id'] = unique_targets_cm.index\n", "\n", "        num_unique_dots_found = len(unique_targets_cm)\n", "        print(f\"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots_found}\")\n", "        \n", "        if num_unique_dots_found == 0:\n", "            print(f\"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        if num_unique_dots_found != 13:\n", "             print(f\"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities, gevonden {num_unique_dots_found}. Dit kan de checkerboard heatmap beïnvloeden.\")\n", "        \n", "        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], \n", "                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')\n", "        \n", "        current_participant_dots_summary_list = []\n", "        for dot_id_val, group in df_accprec.groupby('dot_id'):\n", "            # Geb<PERSON><PERSON> de originele, niet-afgeronde target_x_cm en target_y_cm van unique_targets_cm voor precisie\n", "            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]\n", "            if target_info.empty:\n", "                print(f\"WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val} voor pp {participant_id}\")\n", "                continue\n", "            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])\n", "            \n", "            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))\n", "            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)\n", "            current_participant_dots_summary_list.append(summary)\n", "        \n", "        summary_df_per_dot_current_pp = pd.DataFrame(current_participant_dots_summary_list)\n", "        \n", "        if not summary_df_per_dot_current_pp.empty:\n", "            summary_df_per_dot_current_pp['participant_id'] = participant_id \n", "            all_participants_dot_summaries_list.append(summary_df_per_dot_current_pp)\n", "            print(f\"Statistieken per doelpunt berekend voor {participant_id}.\")\n", "        else:\n", "            print(f\"Kon geen statistieken per doelpunt berekenen voor {participant_id}.\")\n", "\n", "print(\"\\nAlle participanten verwerkt.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3. <PERSON><PERSON><PERSON><PERSON><PERSON> van Resultaten en Genereren van Gecombineerde Plots"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not all_participants_dot_summaries_list:\n", "    print(\"Geen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.\")\n", "else:\n", "    # Comb<PERSON>er alle per-dot summaries van alle participanten\n", "    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries_list, ignore_index=True)\n", "    print(f\"\\nGecombineerde per-dot data van alle participanten. Totale rijen: {len(final_dot_summaries_all_pps_df)}\")\n", "    # print(final_dot_summaries_all_pps_df.head().to_string())\n", "\n", "    # --- Gemiddelde Checkerboard Heatmaps (over alle participanten) ---\n", "    print(\"\\nGenereren van gemiddelde checkerboard heatmaps over alle participanten...\")\n", "    # Be<PERSON>en gemiddelde per dot_id over alle participanten\n", "    # Zorg ervoor dat dot_id numeriek is voor correct groeperen en plotten\n", "    final_dot_summaries_all_pps_df['dot_id'] = pd.to_numeric(final_dot_summaries_all_pps_df['dot_id'], errors='coerce')\n", "    final_dot_summaries_all_pps_df.dropna(subset=['dot_id'], inplace=True) # Verwijder als dot_id niet numeriek kon worden\n", "    final_dot_summaries_all_pps_df['dot_id'] = final_dot_summaries_all_pps_df['dot_id'].astype(int)\n", "    \n", "    averaged_dot_metrics = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()\n", "\n", "    if not averaged_dot_metrics.empty:\n", "        # Zorg ervoor dat er (idealiter) 13 unieke dot_ids zijn voor de checkerboard\n", "        if len(averaged_dot_metrics) > 0:\n", "            # <PERSON><PERSON> <PERSON><PERSON> met Na<PERSON>s als niet alle 13 dots data hadden over alle participanten\n", "            if len(averaged_dot_metrics) < len(positions_to_fill_13_dots):\n", "                print(f\"  WAARSCHUWING: Gemiddelde data beschikbaar voor {len(averaged_dot_metrics)} van de {len(positions_to_fill_13_dots)} verwachte unieke dot posities.\")\n", "            \n", "            # Maak een dataframe met alle mogelijke dot_ids (0-12 voor 13 punten) voor het plotten\n", "            plot_df_avg_checkerboard = pd.DataFrame({'dot_id': range(len(positions_to_fill_13_dots))})\n", "            plot_df_avg_checkerboard = pd.merge(plot_df_avg_checkerboard, averaged_dot_metrics, on='dot_id', how='left')\n", "            \n", "            plot_checkerboard_heatmap(plot_df_avg_checkerboard, measurement='median_offset_cm', title_prefix=\"EyeLink (Gem. Alle PPs)\", positions_to_fill=positions_to_fill_13_dots)\n", "            plot_checkerboard_heatmap(plot_df_avg_checkerboard, measurement='eucl_dist_gaze_to_median_cm', title_prefix=\"EyeLink (Gem. Alle PPs)\", positions_to_fill=positions_to_fill_13_dots)\n", "            plot_checkerboard_heatmap(plot_df_avg_checkerboard, measurement='rms_error_cm', title_prefix=\"EyeLink (Gem. Alle PPs)\", positions_to_fill=positions_to_fill_13_dots)\n", "        else:\n", "            print(\"Geen data om gemiddelde checkerboard heatmaps te plotten.\")\n", "    else:\n", "        print(\"Kon geen gemiddelde metrieken per dot berekenen over participanten.\")\n", "\n", "    # --- <PERSON><PERSON>e P<PERSON> ---\n", "    print(\"\\nGenereren van gecombineerde beschrijvende statistieken en plots...\")\n", "\n", "    # <PERSON><PERSON> met <PERSON>sch<PERSON><PERSON><PERSON><PERSON> Statistieken (over alle dots, alle participanten)\n", "    desc_stats_data_all = {\n", "        'Metric': ['Offset (cm)', '<PERSON><PERSON><PERSON> (SD, cm)', '<PERSON><PERSON>rror (cm)'],\n", "        'Mean': [\n", "            final_dot_summaries_all_pps_df['median_offset_cm'].mean(),\n", "            final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].mean(),\n", "            final_dot_summaries_all_pps_df['rms_error_cm'].mean()\n", "        ],\n", "        'Std Dev': [\n", "            final_dot_summaries_all_pps_df['median_offset_cm'].std(),\n", "            final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].std(),\n", "            final_dot_summaries_all_pps_df['rms_error_cm'].std()\n", "        ],\n", "        'Min': [\n", "            final_dot_summaries_all_pps_df['median_offset_cm'].min(),\n", "            final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].min(),\n", "            final_dot_summaries_all_pps_df['rms_error_cm'].min()\n", "        ],\n", "        'Median': [\n", "            final_dot_summaries_all_pps_df['median_offset_cm'].median(),\n", "            final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].median(),\n", "            final_dot_summaries_all_pps_df['rms_error_cm'].median()\n", "        ],\n", "        'Max': [\n", "            final_dot_summaries_all_pps_df['median_offset_cm'].max(),\n", "            final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].max(),\n", "            final_dot_summaries_all_pps_df['rms_error_cm'].max()\n", "        ]\n", "    }\n", "    overall_desc_stats_df = pd.DataFrame(desc_stats_data_all)\n", "    print(f\"\\nOverall Descriptive Statistics (All Participants, All Dots):\")\n", "    print(overall_desc_stats_df.to_string(index=False))\n", "\n", "    # <PERSON><PERSON> Staafdiagram van Gemiddelde Prestaties (overall)\n", "    plt.figure(figsize=(8, 6))\n", "    barplot_data_overall = overall_desc_stats_df.set_index('Metric')\n", "    sns.barplot(x=barplot_data_overall.index, y='Mean', data=barplot_data_overall, palette=['skyblue', 'lightcoral', 'lightgreen'])\n", "    plt.title('EyeLink (All PPs): Average System Performance Metrics', fontsize=16)\n", "    plt.ylabel('Value (cm)', fontsize=12)\n", "    plt.xlabel('')\n", "    plt.xticks(ticks=range(len(barplot_data_overall.index)), labels=barplot_data_overall.index, rotation=0, ha='center', fontsize=10)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # <PERSON><PERSON><PERSON> van Offset Distributie (overall)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.boxplot(y=final_dot_summaries_all_pps_df['median_offset_cm'], color='skyblue')\n", "    plt.title('EyeLink (All PPs): Distribution of Offset Values Across All Dots', fontsize=16)\n", "    plt.ylabel('Offset (cm)', fontsize=12)\n", "    plt.xlabel(f'{len(final_dot_summaries_all_pps_df)} Total Measurements (Dots x PPs)', fontsize=10)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # <PERSON><PERSON> <PERSON><PERSON><PERSON> (SD) Distributie (overall)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.boxplot(y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], color='lightcoral')\n", "    plt.title('EyeLink (All PPs): Distribution of Precision (SD) Values Across All Dots', fontsize=16)\n", "    plt.ylabel('Precision (SD, cm)', fontsize=12)\n", "    plt.xlabel(f'{len(final_dot_summaries_all_pps_df)} Total Measurements (Dots x PPs)', fontsize=10)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # <PERSON><PERSON><PERSON> van RMS Error Distributie (overall)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.boxplot(y=final_dot_summaries_all_pps_df['rms_error_cm'], color='lightgreen')\n", "    plt.title('EyeLink (All PPs): Distribution of RMS Error Values Across All Dots', fontsize=16)\n", "    plt.ylabel('R<PERSON> Error (cm)', fontsize=12)\n", "    plt.xlabel(f'{len(final_dot_summaries_all_pps_df)} Total Measurements (Dots x PPs)', fontsize=10)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # <PERSON><PERSON> vs. <PERSON><PERSON><PERSON> (SD) (overall)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.scatterplot(x=final_dot_summaries_all_pps_df['median_offset_cm'], y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], alpha=0.5)\n", "    plt.title('EyeLink (All PPs): Accuracy (Offset) vs. Precision (SD) - All Dots', fontsize=16)\n", "    plt.xlabel('Offset (cm)', fontsize=12)\n", "    plt.ylabel('Precision (SD, cm)', fontsize=12)\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # --- Nieuwe Plots: Metriek per dot across participants ---\n", "    print(\"\\nGenereren van plots: metriek per doelpunt over participanten...\")\n", "    metrics_to_plot_per_dot = {\n", "        'median_offset_cm': 'Offset (cm)',\n", "        'eucl_dist_gaze_to_median_cm': 'Precision (SD, cm)',\n", "        'rms_error_cm': 'RMS Error (cm)'\n", "    }\n", "    \n", "    # Bepaal het werkelijke aantal unieke dot_ids in de geaggregeerde data\n", "    # Dit is belang<PERSON>jk als niet alle participanten data hadden voor alle 13 dots\n", "    actual_dot_ids_present = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())\n", "    # Als je alleen wilt plotten voor de dot_ids die in positions_to_fill_13_dots zitten:\n", "    # max_dot_id_for_plot = len(positions_to_fill_13_dots) - 1 \n", "    # plot_data_for_dots = final_dot_summaries_all_pps_df[final_dot_summaries_all_pps_df['dot_id'] <= max_dot_id_for_plot]\n", "    plot_data_for_dots = final_dot_summaries_all_pps_df # Gebruik alle aanwezige dot data\n", "\n", "    for metric_col, metric_label in metrics_to_plot_per_dot.items():\n", "        plt.figure(figsize=(12, 7))\n", "        sns.boxplot(x='dot_id', y=metric_col, data=plot_data_for_dots, color='lightgray', showfliers=False)\n", "        sns.stripplot(x='dot_id', y=metric_col, data=plot_data_for_dots, jitter=True, alpha=0.7, size=5)\n", "        plt.title(f'EyeLink (All PPs): {metric_label} per Dot Across Participants', fontsize=16)\n", "        plt.xlabel('Dot ID (0-12, corresponding to 13 reference points)', fontsize=12)\n", "        plt.ylabel(metric_label, fontsize=12)\n", "        # Zorg ervoor dat x-ticks over<PERSON><PERSON><PERSON> met de aanwezige dot_ids\n", "        if actual_dot_ids_present:\n", "             plt.xticks(ticks=range(len(actual_dot_ids_present)), labels=actual_dot_ids_present, rotation=45, ha='right', fontsize=10)\n", "        else: # Fallback als er geen dot_ids zijn (zou niet moeten gebeuren als all_participants_summaries data heeft)\n", "             plt.xticks(fontsize=10)\n", "        plt.grid(axis='y', linestyle='--')\n", "        plt.tight_layout()\n", "        plt.show()\n", "else:\n", "    print(\"\\nGeen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}