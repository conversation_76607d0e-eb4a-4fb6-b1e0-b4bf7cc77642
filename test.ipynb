{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# EyeLink Accuracy/Precision Analysis (Batch Processing)\n", "\n", "Dit notebook analyseert de accuracy en precisie data verkregen van een EyeLink 1000 eyetracker voor meerdere participanten. Het itereert door een mappenstructuur, berekent mediaan offset, standaarddeviatie van gaze (als maat voor precisie), en RMS error per participant, en visualiseert deze in heatmaps."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import openpyxl # Nodig voor .xlsx, xlrd voor .xls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions\n", "Definities van functies voor pixel-naar-cm conversie, R<PERSON> berekening, statistieken per doelpunt, en het plotten van de heatmatrix."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):\n", "    \"\"\"Converts pixel values to centimeters.\"\"\"\n", "    # Zorg ervoor dat inputs numeriek zijn voor de berekening\n", "    pixels_numeric = pd.to_numeric(pixels, errors='coerce')\n", "    # Als screen_dimension_pixels of physical_screen_dimension_cm 0 is, return NaN om ZeroDivisionError te voorkomen\n", "    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:\n", "        return np.nan\n", "    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_rms(samples_cm):\n", "    \"\"\"Computes Root Mean Square of sequential differences in samples.\"\"\"\n", "    if not isinstance(samples_cm, np.ndarray):\n", "        samples_cm = np.array(samples_cm)\n", "    \n", "    if samples_cm.ndim == 1: \n", "        if len(samples_cm) < 2 : \n", "             return np.nan\n", "        if len(samples_cm) % 2 == 0:\n", "            samples_cm = samples_cm.reshape(-1,2)\n", "        else: \n", "            return np.nan\n", "\n", "    if samples_cm.shape[0] < 2: \n", "        return np.nan\n", "        \n", "    deltas = np.diff(samples_cm, axis=0)\n", "    if deltas.shape[0] == 0: \n", "        return np.nan\n", "        \n", "    distances_sq = np.sum(deltas**2, axis=1)\n", "    rms = np.sqrt(np.mean(distances_sq))\n", "    return rms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):\n", "    \"\"\"\n", "    Calculates median offset, precision (mean Euclidean distance to median gaze),\n", "    and RMS error for a given target dot.\n", "    \"\"\"\n", "    if not gaze_points_for_dot_cm:\n", "        return {\n", "            'dot_id': dot_id,\n", "            'median_offset_cm': np.nan,\n", "            'eucl_dist_gaze_to_median_cm': np.nan,\n", "            'rms_error_cm': np.nan,\n", "            'num_fixations': 0\n", "        }\n", "\n", "    gaze_array_cm = np.array(gaze_points_for_dot_cm)\n", "    \n", "    # Check for NaNs that might have resulted from pd.to_numeric(errors='coerce') in main script\n", "    if np.isnan(gaze_array_cm).any():\n", "        # Filter out rows with NaNs if any individual coordinate is NaN for a point\n", "        gaze_array_cm = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]\n", "        if gaze_array_cm.shape[0] == 0:\n", "             return {\n", "                'dot_id': dot_id,\n", "                'median_offset_cm': np.nan,\n", "                'eucl_dist_gaze_to_median_cm': np.nan,\n", "                'rms_error_cm': np.nan,\n", "                'num_fixations': 0 # Or original len(gaze_points_for_dot_cm) before filtering NaNs\n", "            }\n", "\n", "    median_gaze_x_cm = np.median(gaze_array_cm[:, 0])\n", "    median_gaze_y_cm = np.median(gaze_array_cm[:, 1])\n", "    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)\n", "\n", "    offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - np.array(target_coords_cm))\n", "\n", "    distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm]\n", "    precision_metric_cm = np.mean(distances_to_median_cm)\n", "    \n", "    rms_cm = compute_rms(gaze_array_cm)\n", "\n", "    return {\n", "        'dot_id': dot_id,\n", "        'median_offset_cm': offset_cm,\n", "        'eucl_dist_gaze_to_median_cm': precision_metric_cm,\n", "        'rms_error_cm': rms_cm,\n", "        'num_fixations': len(gaze_points_for_dot_cm) # Number of original fixations for this dot\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_matrix(df_summary, measurement, title_prefix):\n", "    \"\"\"Plots a 5x5 heatmap for the given measurement.\"\"\"\n", "    dot_matrix_data = np.full((5, 5), np.nan)\n", "    if measurement == 'median_offset_cm':\n", "        m_title = 'Offset'\n", "    elif measurement == 'eucl_dist_gaze_to_median_cm':\n", "        m_title = 'SD (Dispersion around Median Gaze)'\n", "    elif measurement == 'rms_error_cm':\n", "        m_title = 'R<PERSON>rror (Sample-to-Sample Stability)'\n", "    else:\n", "        m_title = measurement.replace('_cm', '').replace('_', ' ').title()\n", "    \n", "    title = f\"{title_prefix}: {m_title} (cm)\"\n", "    \n", "    for i in range(5): \n", "        for j in range(5): \n", "            dot_index = i * 5 + j\n", "            if dot_index in df_summary['dot_id'].values:\n", "                value = df_summary.loc[df_summary['dot_id'] == dot_index, measurement].iloc[0]\n", "                dot_matrix_data[i, j] = value\n", "\n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(dot_matrix_data, annot=True, fmt=\".2f\", cmap=\"viridis\",\n", "                square=True, annot_kws={\"size\": 10}, linewidths=.5,\n", "                cbar_kws={'label': f\"{m_title} (cm)\"})\n", "    plt.title(title, fontsize=16)\n", "    plt.xlabel(\"Grid Column Index\", fontsize=12)\n", "    plt.ylabel(\"Grid Row Index\", fontsize=12)\n", "    plt.xticks(np.arange(5) + 0.5, labels=np.arange(1, 6))\n", "    plt.yticks(np.arange(5) + 0.5, labels=np.arange(1, 6), rotation=0)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Main Analysis Script (Batch Processing)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1. Define Base Path and Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PHYSICAL_SCREEN_WIDTH_CM = 34.4\n", "PHYSICAL_SCREEN_HEIGHT_CM = 19.3\n", "EYELINK_SCREEN_WIDTH_PX = 1920\n", "EYELINK_SCREEN_HEIGHT_PX = 1080\n", "\n", "print(f\"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm\")\n", "print(f\"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm\")\n", "print(f\"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px\")\n", "\n", "base_data_path = './CollectedData/eyelink_data/' \n", "print(f\"Base data path: {base_data_path}\")\n", "\n", "try:\n", "    folder_names = [\n", "        name for name in os.listdir(base_data_path)\n", "        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')\n", "    ]\n", "    if not folder_names:\n", "        print(f\"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'. Controleer het pad.\")\n", "    else:\n", "        print(f\"Gevonden participant mappen: {sorted(folder_names)}\")\n", "except FileNotFoundError:\n", "    print(f\"FOUT: Ba<PERSON> data pad niet gevonden: '{base_data_path}'. Controleer het pad.\")\n", "    folder_names = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2. <PERSON> Through Participants and Analyze Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_participants_summaries = [] \n", "\n", "if not folder_names:\n", "    print(\"Geen participant mappen om te verwerken. Script stopt.\")\n", "else:\n", "    for participant_id in sorted(folder_names): \n", "        print(f\"\\n--- Processing Participant: {participant_id} ---\")\n", "        \n", "        file_path = os.path.join(base_data_path, participant_id, \"Output\", f\"{participant_id}.xls\")\n", "        print(f\"Pogen data te laden van: {file_path}\")\n", "\n", "        df_eyelink_raw = None\n", "        loaded_successfully = False\n", "        try:\n", "            print(f\"Poging 1: Laden als TSV (tab-separated text)... {file_path}\")\n", "            df_eyelink_raw = pd.read_csv(file_path, sep='\\t', low_memory=False)\n", "            print(f\"Succesvol geladen als TSV voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "            loaded_successfully = True\n", "        except Exception as e_tsv:\n", "            print(f\"  Mislukt als TSV: {e_tsv}\")\n", "            try:\n", "                print(f\"Poging 2: Laden als CSV (comma-separated text)... {file_path}\")\n", "                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)\n", "                print(f\"Succesvol geladen als CSV voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "                loaded_successfully = True\n", "            except Exception as e_csv:\n", "                print(f\"  Mislukt als CSV: {e_csv}\")\n", "                try:\n", "                    print(f\"Poging 3: Laden als Excel (.xls) met xlrd engine... {file_path}\")\n", "                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')\n", "                    print(f\"Succesvol geladen als Excel (.xls) met xlrd voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "                    loaded_successfully = True\n", "                except Exception as e_xls:\n", "                    print(f\"  Mis<PERSON>t als Excel (.xls) met xlrd: {e_xls}\")\n", "                    try:\n", "                        print(f\"Poging 4: Laden als Excel (.xlsx style) met openpyxl engine... {file_path}\")\n", "                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')\n", "                        print(f\"Succesvol geladen als Excel (.xlsx style) met openpyxl voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "                        loaded_successfully = True\n", "                    except Exception as e_xlsx:\n", "                        print(f\"  Mislukt als Excel (.xlsx style) met openpyxl: {e_xlsx}\")\n", "                        print(f\"KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} met geen enkele methode. Sla deze participant over.\")\n", "                        continue \n", "\n", "        if not loaded_successfully or df_eyelink_raw is None:\n", "            continue\n", "\n", "        if 'arrowLength' not in df_eyelink_raw.columns:\n", "            print(f\"FOUT: <PERSON><PERSON><PERSON> 'arrow<PERSON>ength' niet gevonden voor participant {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)\n", "        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith(\"UNDEFINED\")].copy()\n", "\n", "        if df_accprec.empty:\n", "            print(f\"Geen accuracy/precision data (met 'arrowLength'=\"UNDEFINED\") gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']\n", "        pixel_columns_to_convert = required_cols # Alias for clarity\n", "        missing_cols = [col for col in required_cols if col not in df_accprec.columns]\n", "        if missing_cols:\n", "            print(f\"FOUT: <PERSON>ere<PERSON><PERSON> kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        # Converteer pixel kolommen naar numeriek *voordat* NaNs worden gedropt op basis van deze kolommen alleen\n", "        for col in pixel_columns_to_convert:\n", "            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')\n", "            \n", "        df_accprec.dropna(subset=required_cols, inplace=True) # Drop rows if any of the required cols became NaN after coercion or were already NaN\n", "        if df_accprec.empty:\n", "            print(f\"Geen valide data na converteren naar numeriek en verwijderen van NaNs in vereiste kolommen voor {participant_id}. Sla over.\")\n", "            continue\n", "        print(f\"Gefilterde acc/prec data voor {participant_id}. Vorm: {df_accprec.shape}\")\n", "\n", "        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "        print(f\"Pixelcoördinaten omgerekend naar cm voor {participant_id}.\")\n", "\n", "        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)\n", "        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)\n", "\n", "        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]\n", "        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])\n", "        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)\n", "        unique_targets_cm['dot_id'] = unique_targets_cm.index\n", "\n", "        if len(unique_targets_cm) == 0:\n", "            print(f\"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        if len(unique_targets_cm) != 25:\n", "             print(f\"WAARSCHUWING voor {participant_id}: Verwachtte 25 unieke doelposities, gevonden {len(unique_targets_cm)}.\")\n", "        \n", "        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], \n", "                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')\n", "        \n", "        current_participant_dots_summary_list = []\n", "        for dot_id_val, group in df_accprec.groupby('dot_id'):\n", "            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]\n", "            if target_info.empty:\n", "                print(f\"WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val} voor pp {participant_id}\")\n", "                continue\n", "            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])\n", "            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))\n", "            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)\n", "            current_participant_dots_summary_list.append(summary)\n", "        \n", "        summary_df_per_dot = pd.DataFrame(current_participant_dots_summary_list)\n", "        \n", "        if summary_df_per_dot.empty:\n", "            print(f\"Kon geen samenvattende statistieken berekenen voor {participant_id}. Sla plots over.\")\n", "            continue\n", "        \n", "        summary_df_per_dot['participant_id'] = participant_id \n", "        all_participants_summaries.append(summary_df_per_dot) \n", "            \n", "        print(f\"\\nSamenvattende statistieken per doelpunt voor {participant_id}:\")\n", "\n", "        summary_df_per_dot_plot = summary_df_per_dot.copy()\n", "        if len(summary_df_per_dot_plot) < 25:\n", "            all_possible_dot_ids = pd.DataFrame({'dot_id': range(25)})\n", "            summary_df_per_dot_plot = pd.merge(all_possible_dot_ids, summary_df_per_dot_plot, on='dot_id', how='left')\n", "        \n", "        summary_df_per_dot_plot = summary_df_per_dot_plot.sort_values(by='dot_id').reset_index(drop=True)\n", "\n", "        plot_matrix(summary_df_per_dot_plot, measurement='median_offset_cm', title_prefix=f\"EyeLink ({participant_id})\")\n", "        plot_matrix(summary_df_per_dot_plot, measurement='eucl_dist_gaze_to_median_cm', title_prefix=f\"EyeLink ({participant_id})\")\n", "        plot_matrix(summary_df_per_dot_plot, measurement='rms_error_cm', title_prefix=f\"EyeLink ({participant_id})\")\n", "        print(f\"Heatmaps succesvol gegenereerd voor {participant_id}.\")\n", "\n", "if all_participants_summaries:\n", "    final_summary_all_pps_df = pd.concat(all_participants_summaries, ignore_index=True)\n", "    print(\"\\n\\n--- Optioneel: Gecombineerde Samenvatting voor Alle Verwerkte Participanten (eerste 5 rijen) ---\")\n", "    print(final_summary_all_pps_df.head())\n", "else:\n", "    print(\"\\nGeen data van participanten succesvol verwerkt om een gecombineerde samenvatting te maken.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}