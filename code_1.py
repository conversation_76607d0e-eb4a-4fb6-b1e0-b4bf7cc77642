import os
import pandas as pd
import numpy as np

# Pad naar je map met CSV-bestanden
folder_path = "/Users/<USER>/Documents/Bewegingswetenschappen/BOP/MullerLyer_eyelink/CollectedData/eyelink_data/CSV's"

# Vind alle .csv bestanden in de map
file_names = [fn for fn in os.listdir(folder_path) if fn.endswith(".csv")]

# Lijst om alle deelnemersdata op te slaan
pp_list = []

# Functie om te checken of iets een float is
def is_float(val):
    try:
        float(val)
        return True
    except:
        return False

# Verwerk elk bestand
for fn in file_names:
    path = os.path.join(folder_path, fn)

    try:
        # Lees bestand in als strings
        df = pd.read_csv(path, dtype=str)

        # Fixatiegegevens moeten aanwezig zijn
        df = df[(df['CURRENT_FIX_X'].notna()) & (df['CURRENT_FIX_Y'].notna())]

        # Her<PERSON><PERSON><PERSON> van kolommen
        df['user_pred_px_x'] = df['CURRENT_FIX_X'].astype(float)
        df['user_pred_px_y'] = df['CURRENT_FIX_Y'].astype(float)

        # Toevoegen van vaste scherm- en resolutiewaarden
        df['resX'] = 1920.0
        df['resY'] = 1080.0
        df['scrW_cm'] = 50.37037037037037

        # Verwijder dubbele blikposities (webcam-stilstand)
        df = df.drop_duplicates(subset=['user_pred_px_x', 'user_pred_px_y'], ignore_index=True)

        # Schaal toevoegen
        df['scale_cm_in_px'] = df['scrW_cm'] / df['resX']

        # Zorg dat target-positie geldig is
        df = df[df['accprecX'].apply(is_float) & df['accprecY'].apply(is_float)]
        df['accprecX'] = df['accprecX'].astype(float)
        df['accprecY'] = df['accprecY'].astype(float)

        # Genereer unieke dot-index
        _, indices = np.unique(df[['accprecX', 'accprecY']], axis=0, return_inverse=True)
        df['unique_dot'] = indices

        # Check of deelnemer voldoende unieke dots heeft
        unique_dots = df[['accprecX', 'accprecY']].drop_duplicates()
        required_dots = 13
        if len(unique_dots) < required_dots:
            print(f"⏭️  Overslaan: {fn} heeft slechts {len(unique_dots)} stippen (minimaal {required_dots})")
            continue

        # Voeg toe aan lijst
        pp_list.append(df)

    except Exception as e:
        print(f"⚠️  Fout bij verwerken van {fn}: {e}")
        continue

# Combineer alles in één dataframe
if pp_list:
    all_df = pd.concat(pp_list, ignore_index=True)
    print(f"✅ Gelukt! Gecombineerde dataset bevat {len(all_df)} rijen van {len(pp_list)} deelnemers.")
else:
    print("❌ Geen geldige bestanden gevonden.")

# all_df bevat nu alle samengevoegde en opgeschoonde gegevens
