{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# EyeLink Accuracy/Precision Analysis (Batch Processing)\n", "\n", "Dit notebook analyseert de accuracy en precisie data verkregen van een EyeLink 1000 eyetracker voor meerdere participanten. Het itereert door een mappenstructuur, berekent mediaan offset, standaarddeviatie van gaze (als maat voor precisie), en RMS error per participant voor **13 referentiepunten**. \n", "Vervolgens genereert het:\n", "1.  5x5 'checkerboard' heatmaps voor de ruimtelijke verdeling van de metrieken.\n", "2.  <PERSON><PERSON> met besch<PERSON>j<PERSON>de statistieken.\n", "3.  <PERSON><PERSON> s<PERSON> van gemiddelde prestaties.\n", "4.  <PERSON><PERSON><PERSON><PERSON> van de distributies van de metrieken.\n", "5.  <PERSON><PERSON> scatter<PERSON>lot van offset vs. precisie.\n", "<PERSON><PERSON> all<PERSON> met als doel een directe vergelijking met e<PERSON><PERSON> webcam analyses mogelijk te maken."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import openpyxl # Nodig voor .xlsx, xlrd voor .xls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions\n", "Definities van functies voor pixel-naar-cm conversie, R<PERSON> berekening, statistieken per doelpunt en het plotten."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):\n", "    \"\"\"Converts pixel values to centimeters.\"\"\"\n", "    pixels_numeric = pd.to_numeric(pixels, errors='coerce')\n", "    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:\n", "        return np.nan\n", "    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_rms(samples_cm):\n", "    \"\"\"Computes Root Mean Square of sequential differences in samples.\"\"\"\n", "    if not isinstance(samples_cm, np.ndarray):\n", "        samples_cm = np.array(samples_cm)\n", "    \n", "    if samples_cm.ndim == 1: \n", "        if len(samples_cm) < 2 : \n", "             return np.nan\n", "        if len(samples_cm) % 2 == 0:\n", "            samples_cm = samples_cm.reshape(-1,2)\n", "        else: \n", "            return np.nan\n", "\n", "    if samples_cm.shape[0] < 2: \n", "        return np.nan\n", "        \n", "    deltas = np.diff(samples_cm, axis=0)\n", "    if deltas.shape[0] == 0: \n", "        return np.nan\n", "        \n", "    deltas_cleaned = deltas[~np.isnan(deltas).any(axis=1)]\n", "    if deltas_cleaned.shape[0] == 0:\n", "        return np.nan\n", "        \n", "    distances_sq = np.sum(deltas_cleaned**2, axis=1)\n", "    rms = np.sqrt(np.mean(distances_sq))\n", "    return rms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):\n", "    \"\"\"\n", "    Calculates median offset, precision (mean Euclidean distance to median gaze),\n", "    and RMS error for a given target dot.\n", "    \"\"\"\n", "    if not gaze_points_for_dot_cm:\n", "        return {\n", "            'dot_id': dot_id,\n", "            'median_offset_cm': np.nan,\n", "            'eucl_dist_gaze_to_median_cm': np.nan,\n", "            'rms_error_cm': np.nan,\n", "            'num_fixations': 0\n", "        }\n", "\n", "    gaze_array_cm = np.array(gaze_points_for_dot_cm)\n", "    gaze_array_cm_cleaned = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]\n", "    \n", "    if gaze_array_cm_cleaned.shape[0] == 0:\n", "         return {\n", "            'dot_id': dot_id,\n", "            'median_offset_cm': np.nan,\n", "            'eucl_dist_gaze_to_median_cm': np.nan,\n", "            'rms_error_cm': np.nan,\n", "            'num_fixations': len(gaze_points_for_dot_cm)\n", "        }\n", "\n", "    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])\n", "    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])\n", "    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)\n", "\n", "    if np.isnan(target_coords_cm).any():\n", "        offset_cm = np.nan\n", "    else:\n", "        offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - np.array(target_coords_cm))\n", "\n", "    distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]\n", "    precision_metric_cm = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan # Avoid mean of empty list error\n", "    \n", "    rms_cm = compute_rms(gaze_array_cm_cleaned)\n", "\n", "    return {\n", "        'dot_id': dot_id,\n", "        'median_offset_cm': offset_cm,\n", "        'eucl_dist_gaze_to_median_cm': precision_metric_cm,\n", "        'rms_error_cm': rms_cm,\n", "        'num_fixations': len(gaze_points_for_dot_cm)\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_matrix_checkerboard(df_summary_per_dot, measurement, title_prefix, positions_to_fill):\n", "    \"\"\"Plots a 5x5 checkerboard heatmap for the given measurement using 13 points.\"\"\"\n", "    \n", "    # Maak een meer beschrijvende titel gebaseerd op de 'measurement' kolomnaam\n", "    if measurement == 'median_offset_cm':\n", "        m_title_suffix = 'Offset'\n", "    elif measurement == 'eucl_dist_gaze_to_median_cm':\n", "        m_title_suffix = 'SD (Dispersion)' # Kortere titel voor consistentie met webcam plots\n", "    elif measurement == 'rms_error_cm':\n", "        m_title_suffix = 'RMS Error'\n", "    else:\n", "        m_title_suffix = measurement.replace('_cm', '').replace('_', ' ').title()\n", "    \n", "    # Gebruik exact dezelfde titels als de webcam heatmaps als basis\n", "    if measurement == 'median_offset_cm':\n", "        plot_title = f\"{title_prefix}: Offset (cm) Across Positions\"\n", "    elif measurement == 'eucl_dist_gaze_to_median_cm':\n", "        plot_title = f\"{title_prefix}: SD (cm) Across Positions\"\n", "    elif measurement == 'rms_error_cm':\n", "        plot_title = f\"{title_prefix}: RMS error (cm) Across Positions\"\n", "    else:\n", "        plot_title = f\"{title_prefix}: {m_title_suffix} (cm) Across Positions\"\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON> een lege 5x5 DataFram<PERSON> g<PERSON><PERSON> met Na<PERSON>s\n", "    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan))\n", "\n", "    # Sorteer df_summary_per_dot op 'dot_id' om ervoor te zorgen dat het overeenkomt met de volgorde van positions_to_fill\n", "    # Dit is cruciaal als de dot_ids (0-12) de impliciete volgorde van positions_to_fill representeren.\n", "    df_summary_sorted = df_summary_per_dot.sort_values(by='dot_id').reset_index(drop=True)\n", "\n", "    for idx, (row_idx, col_idx) in enumerate(positions_to_fill):\n", "        if idx < len(df_summary_sorted):\n", "            # <PERSON><PERSON> de corresponderende waarde in df_summary_sorted.\n", "            # Aanname: de idx-de rij in df_summary_sorted (na sorteren op dot_id) \n", "            # hoort bij de idx-de positie in positions_to_fill.\n", "            # We gebruiken hier de 'dot_id' van df_summary_sorted om de juiste meting te vinden.\n", "            # Dit vereist dat dot_id's 0-12 zijn en overeenkomen met de impliciete index van positions_to_fill.\n", "            current_dot_id = df_summary_sorted.loc[idx, 'dot_id'] # Krijg de dot_id van de gesorteerde data\n", "            value_series = df_summary_sorted.loc[df_summary_sorted['dot_id'] == current_dot_id, measurement]\n", "            if not value_series.empty:\n", "                 dot_matrix.iloc[row_idx, col_idx] = value_series.iloc[0]\n", "        else:\n", "            # <PERSON><PERSON> dan 13 datapunten in summary, sommige posities blijven leeg\n", "            pass \n", "\n", "    plt.figure(figsize=(8, 8)) # Aangepast naar de webcam heatmap grootte\n", "    heatmap = sns.heatmap(dot_matrix, annot=True, fmt=\".2f\", cmap=\"YlGnBu\", # cmap van webcam voorbeeld\n", "                          square=True, annot_kws={\"size\": 12}, linewidths=0.5,\n", "                          linecolor='gray', cbar_kws={'label': f\"{m_title_suffix} (cm)\"})\n", "\n", "    plt.title(plot_title, fontsize=16, weight='bold', pad=12)\n", "    plt.xlabel('Column', fontsize=14, labelpad=10)\n", "    plt.ylabel('Row', fontsize=14, labelpad=10)\n", "    heatmap.set_xticklabels(range(1, 6), fontsize=12)\n", "    heatmap.set_yticklabels(range(1, 6), fontsize=12, rotation=0)\n", "    heatmap.collections[0].colorbar.ax.tick_params(labelsize=12)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Main Analysis Script (Batch Processing)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1. Define Base Path and Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PHYSICAL_SCREEN_WIDTH_CM = 34.4\n", "PHYSICAL_SCREEN_HEIGHT_CM = 19.3\n", "EYELINK_SCREEN_WIDTH_PX = 1920\n", "EYELINK_SCREEN_HEIGHT_PX = 1080\n", "\n", "print(f\"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm\")\n", "print(f\"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm\")\n", "print(f\"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px\")\n", "\n", "base_data_path = './CollectedData/eyelink_data/' \n", "print(f\"Base data path: {base_data_path}\")\n", "\n", "try:\n", "    folder_names = [\n", "        name for name in os.listdir(base_data_path)\n", "        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')\n", "    ]\n", "    if not folder_names:\n", "        print(f\"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'. Controleer het pad.\")\n", "    else:\n", "        print(f\"Gevonden participant mappen: {sorted(folder_names)}\")\n", "except FileNotFoundError:\n", "    print(f\"FOUT: Ba<PERSON> data pad niet gevonden: '{base_data_path}'. Controleer het pad.\")\n", "    folder_names = []\n", "\n", "# De<PERSON>ieer de posities voor de 13 punten in een 5x5 grid (0-indexed)\n", "# Dit is de AANGENOMEN checkerboard mapping. PAS AAN INDIEN NODIG!\n", "positions_to_fill_13_dots = [\n", "    (0,0), (0,2), (0,4),  # <PERSON>venste rij\n", "    (1,1), (1,3),          # Tweede rij\n", "    (2,0), (2,2), (2,4),  # <PERSON><PERSON><PERSON> rij\n", "    (3,1), (3,3),          # <PERSON><PERSON><PERSON> rij\n", "    (4,0), (4,2), (4,4)   # Onderste rij\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2. <PERSON> Through Participants and Analyze Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_participants_desc_stats = [] \n", "all_participants_dot_summaries = [] \n", "\n", "if not folder_names:\n", "    print(\"Geen participant mappen om te verwerken. Script stopt.\")\n", "else:\n", "    for participant_id in sorted(folder_names): \n", "        print(f\"\\n--- Processing Participant: {participant_id} ---\")\n", "        \n", "        file_path = os.path.join(base_data_path, participant_id, \"Output\", f\"{participant_id}.xls\")\n", "        print(f\"Pogen data te laden van: {file_path}\")\n", "\n", "        df_eyelink_raw = None\n", "        loaded_successfully = False\n", "        try:\n", "            print(f\"Poging 1: Laden als TSV (tab-separated text)... {file_path}\")\n", "            df_eyelink_raw = pd.read_csv(file_path, sep='\\t', low_memory=False)\n", "            print(f\"Succesvol geladen als TSV voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "            loaded_successfully = True\n", "        except Exception as e_tsv:\n", "            print(f\"  Mislukt als TSV: {e_tsv}\")\n", "            try:\n", "                print(f\"Poging 2: Laden als CSV (comma-separated text)... {file_path}\")\n", "                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)\n", "                print(f\"Succesvol geladen als CSV voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "                loaded_successfully = True\n", "            except Exception as e_csv:\n", "                print(f\"  Mislukt als CSV: {e_csv}\")\n", "                try:\n", "                    print(f\"Poging 3: Laden als Excel (.xls) met xlrd engine... {file_path}\")\n", "                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')\n", "                    print(f\"Succesvol geladen als Excel (.xls) met xlrd voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "                    loaded_successfully = True\n", "                except Exception as e_xls:\n", "                    print(f\"  Mis<PERSON>t als Excel (.xls) met xlrd: {e_xls}\")\n", "                    try:\n", "                        print(f\"Poging 4: Laden als Excel (.xlsx style) met openpyxl engine... {file_path}\")\n", "                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')\n", "                        print(f\"Succesvol geladen als Excel (.xlsx style) met openpyxl voor {participant_id}. Vorm: {df_eyelink_raw.shape}\")\n", "                        loaded_successfully = True\n", "                    except Exception as e_xlsx:\n", "                        print(f\"  Mislukt als Excel (.xlsx style) met openpyxl: {e_xlsx}\")\n", "                        print(f\"KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} met geen enkele methode. Sla deze participant over.\")\n", "                        continue \n", "\n", "        if not loaded_successfully or df_eyelink_raw is None:\n", "            continue\n", "\n", "        if 'arrowLength' not in df_eyelink_raw.columns:\n", "            print(f\"FOUT: <PERSON><PERSON><PERSON> 'arrow<PERSON>ength' niet gevonden voor participant {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)\n", "        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith(\"UNDEFINED\")].copy()\n", "\n", "        if df_accprec.empty:\n", "            print(f\"Geen accuracy/precision data (met 'arrowLength'=\"UNDEFINED\") gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']\n", "        pixel_columns_to_convert = required_cols \n", "        missing_cols = [col for col in required_cols if col not in df_accprec.columns]\n", "        if missing_cols:\n", "            print(f\"FOUT: <PERSON>ere<PERSON><PERSON> kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        \n", "        for col in pixel_columns_to_convert:\n", "            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')\n", "            \n", "        df_accprec.dropna(subset=required_cols, inplace=True)\n", "        if df_accprec.empty:\n", "            print(f\"Geen valide data na converteren naar numeriek en verwijderen van NaNs in vereiste kolommen voor {participant_id}. Sla over.\")\n", "            continue\n", "        print(f\"Gefilterde acc/prec data voor {participant_id}. Vorm: {df_accprec.shape}\")\n", "\n", "        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "        \n", "        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']\n", "        df_accprec.dropna(subset=cm_cols_to_check, inplace=True)\n", "        if df_accprec.empty:\n", "            print(f\"Geen valide data na cm-conversie voor {participant_id}. Sla over.\")\n", "            continue\n", "        print(f\"Pixelcoördinaten omgerekend naar cm voor {participant_id}.\")\n", "\n", "        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)\n", "        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)\n", "\n", "        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]\n", "        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])\n", "        # Sorteer unieke targets om een consistente dot_id volgorde te krijgen (belangrijk voor checkerboard mapping)\n", "        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)\n", "        unique_targets_cm['dot_id'] = unique_targets_cm.index # dot_id 0 tot N-1 (N is aantal unieke dots)\n", "\n", "        num_unique_dots = len(unique_targets_cm)\n", "        print(f\"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots}\")\n", "        \n", "        if num_unique_dots == 0:\n", "            print(f\"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.\")\n", "            continue\n", "        # Waar<PERSON><PERSON>wing als het aantal niet 13 is, maar ga wel door.\n", "        if num_unique_dots != 13:\n", "             print(f\"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities voor checkerboard, gevonden {num_unique_dots}. Heatmap kan incompleet zijn.\")\n", "        \n", "        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], \n", "                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')\n", "        \n", "        current_participant_dots_summary_list = []\n", "        for dot_id_val, group in df_accprec.groupby('dot_id'):\n", "            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]\n", "            if target_info.empty:\n", "                print(f\"WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val} voor pp {participant_id}\")\n", "                continue\n", "            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])\n", "            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))\n", "            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)\n", "            current_participant_dots_summary_list.append(summary)\n", "        \n", "        summary_df_per_dot = pd.DataFrame(current_participant_dots_summary_list)\n", "        \n", "        if summary_df_per_dot.empty or summary_df_per_dot[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].isnull().all().all():\n", "            print(f\"Kon geen valide samenvattende statistieken berekenen voor {participant_id}. Sla plots over.\")\n", "            continue\n", "        \n", "        summary_df_per_dot['participant_id'] = participant_id \n", "        all_participants_dot_summaries.append(summary_df_per_dot) # Sla de per-dot summaries op\n", "            \n", "        print(f\"\\nSamenvattende statistieken per doelpunt voor {participant_id} (eerste 5 rijen):\")\n", "        print(summary_df_per_dot.head().to_string())\n", "\n", "        # --- Checkerboard Heatmaps (13 punten op 5x5 grid) ---\n", "        # <PERSON>een plotten als we (idealiter) 13 punten hebben voor de checkerboard, \n", "        # of tenminste genoeg data om iets zinnigs te plotten.\n", "        if num_unique_dots > 0 and not summary_df_per_dot.empty:\n", "            # Zorg ervoor dat summary_df_per_dot gesorteerd is op dot_id voor correcte mapping\n", "            # De dot_ids (0..N-1) m<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> met de volgor<PERSON> van positions_to_fill_13_dots\n", "            # als N == 13. Als N < 13, vult het de eerste N posities.\n", "            plot_df_checkerboard = summary_df_per_dot.sort_values(by='dot_id').reset_index(drop=True)\n", "            plot_matrix_checkerboard(plot_df_checkerboard, measurement='median_offset_cm', title_prefix=f\"EyeLink ({participant_id})\", positions_to_fill=positions_to_fill_13_dots)\n", "            plot_matrix_checkerboard(plot_df_checkerboard, measurement='eucl_dist_gaze_to_median_cm', title_prefix=f\"EyeLink ({participant_id})\", positions_to_fill=positions_to_fill_13_dots)\n", "            plot_matrix_checkerboard(plot_df_checkerboard, measurement='rms_error_cm', title_prefix=f\"EyeLink ({participant_id})\", positions_to_fill=positions_to_fill_13_dots)\n", "            print(f\"Checkerboard heatmaps gegenereerd voor {participant_id}.\")\n", "        else:\n", "            print(f\"Slaan checkerboard heatmaps over voor {participant_id} vanwege onvoldoende data.\")\n", "\n", "        # --- <PERSON>ere Plots (Tabel, Staafdiagram, Boxplots, Scatterplot) ---\n", "        print(f\"\\nGenereren van beschrijvende statistieken en andere plots voor {participant_id}...\")\n", "\n", "        # <PERSON><PERSON> met Beschrijvende Statistieken\n", "        desc_stats_data = {\n", "            'Metric': ['Offset (cm)', '<PERSON><PERSON><PERSON> (SD, cm)', '<PERSON><PERSON>rror (cm)'],\n", "            'Mean': [\n", "                summary_df_per_dot['median_offset_cm'].mean(),\n", "                summary_df_per_dot['eucl_dist_gaze_to_median_cm'].mean(),\n", "                summary_df_per_dot['rms_error_cm'].mean()\n", "            ],\n", "            'Std Dev': [\n", "                summary_df_per_dot['median_offset_cm'].std(),\n", "                summary_df_per_dot['eucl_dist_gaze_to_median_cm'].std(),\n", "                summary_df_per_dot['rms_error_cm'].std()\n", "            ],\n", "            'Min': [\n", "                summary_df_per_dot['median_offset_cm'].min(),\n", "                summary_df_per_dot['eucl_dist_gaze_to_median_cm'].min(),\n", "                summary_df_per_dot['rms_error_cm'].min()\n", "            ],\n", "            'Median': [\n", "                summary_df_per_dot['median_offset_cm'].median(),\n", "                summary_df_per_dot['eucl_dist_gaze_to_median_cm'].median(),\n", "                summary_df_per_dot['rms_error_cm'].median()\n", "            ],\n", "            'Max': [\n", "                summary_df_per_dot['median_offset_cm'].max(),\n", "                summary_df_per_dot['eucl_dist_gaze_to_median_cm'].max(),\n", "                summary_df_per_dot['rms_error_cm'].max()\n", "            ]\n", "        }\n", "        desc_stats_df = pd.DataFrame(desc_stats_data)\n", "        desc_stats_df['participant_id'] = participant_id\n", "        all_participants_desc_stats.append(desc_stats_df) \n", "\n", "        print(f\"\\nDescriptive Statistics for {participant_id}:\")\n", "        print(desc_stats_df.to_string(index=False))\n", "\n", "        # <PERSON><PERSON> Staafdiagram van Gemiddelde Prestaties\n", "        plt.figure(figsize=(8, 6))\n", "        # Gebruik de exacte metrische namen zoals in de webcam plot voor de x-as labels\n", "        barplot_data = desc_stats_df.set_index('Metric')\n", "        sns.barplot(x=barplot_data.index, y='Mean', data=barplot_data, palette=['skyblue', 'lightcoral', 'lightgreen'])\n", "        plt.title(f'EyeLink ({participant_id}): Average System Performance Metrics', fontsize=16)\n", "        plt.ylabel('Value (cm)', fontsize=12)\n", "        plt.xlabel('') # X-as label is impliciet door de tick labels\n", "        plt.xticks(ticks=range(len(barplot_data.index)), labels=barplot_data.index, rotation=0, ha='center', fontsize=10)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # <PERSON><PERSON> van Offset Distributie\n", "        plt.figure(figsize=(8, 6))\n", "        sns.boxplot(y=summary_df_per_dot['median_offset_cm'], color='skyblue')\n", "        plt.title(f'EyeLink ({participant_id}): Distribution of Offset Values Across Calibration Points', fontsize=16)\n", "        plt.ylabel('Offset (cm)', fontsize=12)\n", "        plt.xlabel(f'{num_unique_dots} Calibration Points', fontsize=10)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # <PERSON><PERSON><PERSON> (SD) Distributie\n", "        plt.figure(figsize=(8, 6))\n", "        sns.boxplot(y=summary_df_per_dot['eucl_dist_gaze_to_median_cm'], color='lightcoral')\n", "        plt.title(f'EyeLink ({participant_id}): Distribution of Precision (SD) Values Across Calibration Points', fontsize=16)\n", "        plt.ylabel('Precision (SD, cm)', fontsize=12)\n", "        plt.xlabel(f'{num_unique_dots} Calibration Points', fontsize=10)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # <PERSON><PERSON><PERSON> van RMS Error Distributie\n", "        plt.figure(figsize=(8, 6))\n", "        sns.boxplot(y=summary_df_per_dot['rms_error_cm'], color='lightgreen')\n", "        plt.title(f'EyeLink ({participant_id}): Distribution of RMS Error Values Across Calibration Points', fontsize=16)\n", "        plt.ylabel('R<PERSON> Error (cm)', fontsize=12)\n", "        plt.xlabel(f'{num_unique_dots} Calibration Points', fontsize=10)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # <PERSON><PERSON> vs. <PERSON><PERSON><PERSON> (SD)\n", "        plt.figure(figsize=(8, 6))\n", "        sns.scatterplot(x=summary_df_per_dot['median_offset_cm'], y=summary_df_per_dot['eucl_dist_gaze_to_median_cm'])\n", "        plt.title(f'EyeLink ({participant_id}): Accuracy (Offset) vs. Precision (SD) per Point', fontsize=16)\n", "        plt.xlabel('Offset (cm)', fontsize=12)\n", "        plt.ylabel('Precision (SD, cm)', fontsize=12)\n", "        plt.grid(True)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        print(f\"Alle plots succesvol gegenereerd voor {participant_id}.\")\n", "\n", "if all_participants_desc_stats:\n", "    final_desc_stats_all_pps_df = pd.concat(all_participants_desc_stats, ignore_index=True)\n", "    print(\"\\n\\n--- Optioneel: Gecombineerde Beschrijvende Statistieken voor Alle Verwerkte Participanten ---\")\n", "    print(final_desc_stats_all_pps_df.to_string())\n", "    \n", "    # Optioneel: <PERSON><PERSON> de gecombineerde beschrijvende statistieken op\n", "    # combined_desc_stats_path = os.path.join(base_data_path, '..', 'analysis_results', 'eyelink_accuracy_descriptives_all.csv')\n", "    # os.makedirs(os.path.dirname(combined_desc_stats_path), exist_ok=True)\n", "    # final_desc_stats_all_pps_df.to_csv(combined_desc_stats_path, index=False)\n", "    # print(f\"\\nGecombineerde beschrijvende statistieken opgeslagen in: {combined_desc_stats_path}\")\n", "\n", "if all_participants_dot_summaries:\n", "    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries, ignore_index=True)\n", "    # Optioneel: <PERSON><PERSON> de gecombineerde per-dot summaries op\n", "    # combined_dot_summaries_path = os.path.join(base_data_path, '..', 'analysis_results', 'eyelink_accuracy_per_dot_all.csv')\n", "    # os.makedirs(os.path.dirname(combined_dot_summaries_path), exist_ok=True)\n", "    # final_dot_summaries_all_pps_df.to_csv(combined_dot_summaries_path, index=False)\n", "    # print(f\"\\nGecombineerde per-dot resultaten opgeslagen in: {combined_dot_summaries_path}\")\n", "\n", "    # Voorbeeld: Berek<PERSON> gemiddelde metrieken per dot_id over alle participanten\n", "    # grouped_summary = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()\n", "    # print(\"\\n--- Optioneel: Gemiddelde metrieken per doelpunt over alle participanten ---\")\n", "    # print(grouped_summary)\n", "    \n", "    # Plot gemiddelde checkerboard heatmaps (als je 'grouped_summary' berekent en het (idealiter) 13 dots bevat)\n", "    # if not grouped_summary.empty:\n", "    #    plot_matrix_checkerboard(grouped_summary, measurement='median_offset_cm', title_prefix=\"EyeLink (All PPs Average)\", positions_to_fill=positions_to_fill_13_dots)\n", "    #    plot_matrix_checkerboard(grouped_summary, measurement='eucl_dist_gaze_to_median_cm', title_prefix=\"EyeLink (All PPs Average)\", positions_to_fill=positions_to_fill_13_dots)\n", "    #    plot_matrix_checkerboard(grouped_summary, measurement='rms_error_cm', title_prefix=\"EyeLink (All PPs Average)\", positions_to_fill=positions_to_fill_13_dots)\n", "else:\n", "    print(\"\\nGeen data van participanten succesvol verwerkt om een gecombineerde samenvatting te maken.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}