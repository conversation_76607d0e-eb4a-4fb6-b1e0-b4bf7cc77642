import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches # Voor achtergrondkleuren
import seaborn as sns
import os
import openpyxl # Nodig voor .xlsx, xlrd voor .xls

def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):
    """Converts pixel values to centimeters."""
    pixels_numeric = pd.to_numeric(pixels, errors='coerce')
    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:
        return np.nan
    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm

def compute_rms(samples_cm):
    """Computes Root Mean Square of sequential differences in samples."""
    if not isinstance(samples_cm, np.ndarray):
        samples_cm = np.array(samples_cm)
    
    if samples_cm.ndim == 1: 
        if len(samples_cm) < 2 : 
             return np.nan
        if len(samples_cm) % 2 == 0:
            samples_cm = samples_cm.reshape(-1,2)
        else: 
            return np.nan

    if samples_cm.shape[0] < 2: 
        return np.nan
        
    deltas = np.diff(samples_cm, axis=0)
    if deltas.shape[0] == 0: 
        return np.nan
        
    deltas_cleaned = deltas[~np.isnan(deltas).any(axis=1)]
    if deltas_cleaned.shape[0] == 0:
        return np.nan
        
    distances_sq = np.sum(deltas_cleaned**2, axis=1)
    rms = np.sqrt(np.mean(distances_sq))
    return rms

def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):
    """
    Calculates median offset, precision (mean Euclidean distance to median gaze),
    and RMS error for a given target dot.
    """
    if not gaze_points_for_dot_cm:
        return {
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            'eucl_dist_gaze_to_median_cm': np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': 0
        }

    gaze_array_cm = np.array(gaze_points_for_dot_cm)
    gaze_array_cm_cleaned = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]
    
    if gaze_array_cm_cleaned.shape[0] == 0:
         return {
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            'eucl_dist_gaze_to_median_cm': np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': len(gaze_points_for_dot_cm)
        }

    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])
    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])
    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)

    target_coords_cm_arr = np.array(target_coords_cm)
    if np.isnan(target_coords_cm_arr).any() or np.isnan(median_gaze_coords_cm).any():
        offset_cm = np.nan
    else:
        offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - target_coords_cm_arr)

    if np.isnan(median_gaze_coords_cm).any():
        distances_to_median_cm = [] 
    else:
        distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]
    
    precision_metric_cm = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan
    
    rms_cm = compute_rms(gaze_array_cm_cleaned)

    return {
        'dot_id': dot_id,
        'median_offset_cm': offset_cm,
        'eucl_dist_gaze_to_median_cm': precision_metric_cm,
        'rms_error_cm': rms_cm,
        'num_fixations': len(gaze_points_for_dot_cm)
    }

def plot_checkerboard_heatmap(df_aggregated_per_dot, measurement, title_prefix, positions_to_fill):
    """Plots a 5x5 checkerboard heatmap for the given measurement using specified point positions."""
    
    if measurement == 'median_offset_cm':
        m_title_suffix = 'Offset'
        heatmap_title = f"{title_prefix}: Offset (cm) Across Positions"
    elif measurement == 'eucl_dist_gaze_to_median_cm':
        m_title_suffix = 'SD (Dispersion)'
        heatmap_title = f"{title_prefix}: SD (cm) Across Positions"
    elif measurement == 'rms_error_cm':
        m_title_suffix = 'RMS Error'
        heatmap_title = f"{title_prefix}: RMS error (cm) Across Positions"
    else:
        m_title_suffix = measurement.replace('_cm', '').replace('_', ' ').title()
        heatmap_title = f"{title_prefix}: {m_title_suffix} (cm) Across Positions"
    
    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan))
    df_aggregated_sorted = df_aggregated_per_dot.sort_values(by='dot_id').reset_index(drop=True)

    for idx, (row_idx, col_idx) in enumerate(positions_to_fill):
        if idx < len(df_aggregated_sorted) and df_aggregated_sorted.loc[idx, 'dot_id'] == idx:
            value = df_aggregated_sorted.loc[idx, measurement]
            dot_matrix.iloc[row_idx, col_idx] = value
            
    plt.figure(figsize=(8, 8))
    heatmap_ax = sns.heatmap(dot_matrix, annot=True, fmt=".2f", cmap="YlGnBu", 
                             square=True, annot_kws={"size": 12}, linewidths=0.5,
                             linecolor='gray', cbar_kws={'label': f"{m_title_suffix} (cm)"})
    plt.title(heatmap_title, fontsize=16, weight='bold', pad=12)
    plt.xlabel('Column', fontsize=14, labelpad=10)
    plt.ylabel('Row', fontsize=14, labelpad=10)
    heatmap_ax.set_xticklabels(range(1, 6), fontsize=12)
    heatmap_ax.set_yticklabels(range(1, 6), fontsize=12, rotation=0)
    if heatmap_ax.collections:
        heatmap_ax.collections[0].colorbar.ax.tick_params(labelsize=12)
    plt.tight_layout()
    plt.show()

def plot_metric_per_dot_custom_style(data_df, metric_col, y_label, base_title, dot_id_order):
    """Genereert een boxplot per dot_id met individuele rode punten en alternerende achtergrondkleur,
       gebaseerd op de stijl van image_73b5a0.png.
    """
    
    # Bepaal de volledige titel, consistent met webcam output (bijv. "Distribution of Offset Values Across Calibration Points")
    if 'Offset' in y_label:
        full_title = 'Distribution of Offset Values Across Calibration Points (EyeLink - All PPs)'
    elif 'Precision (SD' in y_label:
        full_title = 'Distribution of Precision (SD) Values Across Calibration Points (EyeLink - All PPs)'
    elif 'RMS Error' in y_label:
        full_title = 'Distribution of RMS Error Values Across Calibration Points (EyeLink - All PPs)'
    else:
        full_title = base_title + ' (EyeLink - All PPs)'

    plt.style.use('seaborn-v0_8-whitegrid') 
    fig, ax = plt.subplots(figsize=(14, 7))
    
    plot_data = data_df[data_df['dot_id'].isin(dot_id_order)]
    
    # Alternerende achtergrondkleuren
    for i, dot_id_val in enumerate(dot_id_order):
        if i % 2 == 1: # Voor oneven indices (tweede, vierde, etc. kolom in de plot)
            # ax.axvspan(x_coord - width/2, x_coord + width/2, ...)
            # De x-coördinaten voor axvspan zijn de grenzen van de categorieën
            ax.axvspan(i - 0.5, i + 0.5, facecolor='whitesmoke', alpha=1, zorder=0) 
            
    # Boxplot - stijl aangepast aan image_73b5a0.png
    sns.boxplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order,
                color='white', width=0.7, showfliers=False, 
                medianprops={'color': 'black', 'linewidth': 2},
                boxprops={'edgecolor': 'black', 'linewidth': 1.5, 'facecolor':'white', 'zorder': 3},
                whiskerprops={'color': 'black', 'linewidth': 1.5, 'zorder': 3},
                capprops={'color': 'black', 'linewidth': 1.5, 'zorder': 3},
                ax=ax, zorder=2)
    
    # Individuele datapunten (rood)
    sns.stripplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order, 
                  color='red', jitter=0.15, alpha=0.8, size=6, ax=ax, zorder=1)
    
    ax.set_title(full_title, fontsize=16, weight='bold', pad=15)
    ax.set_xlabel('Dot ID', fontsize=14, labelpad=10)
    ax.set_ylabel(y_label, fontsize=14, labelpad=10)
    
    ax.set_xticks(ticks=range(len(dot_id_order)))
    ax.set_xticklabels(dot_id_order, fontsize=12)
    ax.tick_params(axis='y', labelsize=12)
    
    ax.grid(True, axis='y', linestyle=':', color='lightgray', zorder=-1) 
    ax.set_facecolor('white') 
    fig.patch.set_facecolor('white') 
    
    # Verwijder bovenste en rechter spines voor een clean look, zoals vaak in publicaties
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    plt.tight_layout()
    plt.show()

PHYSICAL_SCREEN_WIDTH_CM = 34.4
PHYSICAL_SCREEN_HEIGHT_CM = 19.3
EYELINK_SCREEN_WIDTH_PX = 1920
EYELINK_SCREEN_HEIGHT_PX = 1080

print(f"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm")
print(f"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm")
print(f"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px")

base_data_path = './CollectedData/eyelink_data/' 
print(f"Base data path: {base_data_path}")

try:
    folder_names = [
        name for name in os.listdir(base_data_path)
        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')
    ]
    if not folder_names:
        print(f"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'. Controleer het pad.")
    else:
        print(f"Gevonden participant mappen: {sorted(folder_names)}")
except FileNotFoundError:
    print(f"FOUT: Basis data pad niet gevonden: '{base_data_path}'. Controleer het pad.")
    folder_names = []

positions_to_fill_13_dots = [
    (0,0), (0,2), (0,4),  
    (1,1), (1,3),          
    (2,0), (2,2), (2,4),  
    (3,1), (3,3),          
    (4,0), (4,2), (4,4)   
]

all_participants_dot_summaries_list = []

if not folder_names:
    print("Geen participant mappen om te verwerken. Script stopt.")
else:
    for participant_id in sorted(folder_names): 
        print(f"\n--- Processing Participant: {participant_id} ---")
        
        file_path = os.path.join(base_data_path, participant_id, "Output", f"{participant_id}.xls")
        print(f"Pogen data te laden van: {file_path}")

        df_eyelink_raw = None
        loaded_successfully = False
        try:
            print(f"  Poging 1: Laden als TSV... {file_path}")
            df_eyelink_raw = pd.read_csv(file_path, sep='\t', low_memory=False)
            print(f"    Succesvol geladen als TSV. Vorm: {df_eyelink_raw.shape}")
            loaded_successfully = True
        except Exception:
            try:
                print(f"  Poging 2: Laden als CSV... {file_path}")
                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)
                print(f"    Succesvol geladen als CSV. Vorm: {df_eyelink_raw.shape}")
                loaded_successfully = True
            except Exception:
                try:
                    print(f"  Poging 3: Laden als Excel (.xls) met xlrd... {file_path}")
                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')
                    print(f"    Succesvol geladen als Excel (.xls) met xlrd. Vorm: {df_eyelink_raw.shape}")
                    loaded_successfully = True
                except Exception:
                    try:
                        print(f"  Poging 4: Laden als Excel (.xlsx style) met openpyxl... {file_path}")
                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')
                        print(f"    Succesvol geladen als Excel (.xlsx style) met openpyxl. Vorm: {df_eyelink_raw.shape}")
                        loaded_successfully = True
                    except Exception as e_final:
                        print(f"  KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} ({e_final}). Sla over.")
                        continue 

        if not loaded_successfully or df_eyelink_raw is None:
            continue

        if 'arrowLength' not in df_eyelink_raw.columns:
            print(f"FOUT: Kolom 'arrowLength' niet gevonden voor {participant_id}. Sla over.")
            continue
        
        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)
        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith("UNDEFINED")].copy()

        if df_accprec.empty:
            print(f"Geen accuracy/precision data gevonden voor {participant_id}. Sla over.")
            continue
        
        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']
        missing_cols = [col for col in required_cols if col not in df_accprec.columns]
        if missing_cols:
            print(f"FOUT: Vereiste kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.")
            continue
        for col in required_cols:
            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')
        df_accprec.dropna(subset=required_cols, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na numerieke conversie voor {participant_id}. Sla over.")
            continue

        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']
        df_accprec.dropna(subset=cm_cols_to_check, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na cm-conversie voor {participant_id}. Sla over.")
            continue

        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)
        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)
        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]
        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])
        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)
        unique_targets_cm['dot_id'] = unique_targets_cm.index 
        num_unique_dots_found = len(unique_targets_cm)
        print(f"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots_found}")
        if num_unique_dots_found == 0:
            print(f"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.")
            continue
        if num_unique_dots_found != 13:
             print(f"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities, gevonden {num_unique_dots_found}.")
        
        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], 
                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')
        
        current_participant_dots_summary_list = []
        for dot_id_val in sorted(df_accprec['dot_id'].unique()):
            if dot_id_val >= len(positions_to_fill_13_dots):
                print(f"  WAARSCHUWING: Dot ID {dot_id_val} is groter dan verwacht aantal checkerboard posities ({len(positions_to_fill_13_dots)}), wordt genegeerd.")
                continue
            group = df_accprec[df_accprec['dot_id'] == dot_id_val]
            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]
            if target_info.empty: 
                print(f"  WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val} voor pp {participant_id}")
                continue
            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])
            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))
            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)
            current_participant_dots_summary_list.append(summary)
        
        summary_df_per_dot_current_pp = pd.DataFrame(current_participant_dots_summary_list)
        
        if not summary_df_per_dot_current_pp.empty and not summary_df_per_dot_current_pp[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].isnull().all().all():
            summary_df_per_dot_current_pp['participant_id'] = participant_id 
            all_participants_dot_summaries_list.append(summary_df_per_dot_current_pp)
            print(f"Statistieken per doelpunt berekend voor {participant_id}.")
        else:
            print(f"Kon geen (valide) statistieken per doelpunt berekenen voor {participant_id}.")

print("\nAlle participanten verwerkt.")

if not all_participants_dot_summaries_list:
    print("Geen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.")
else:
    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries_list, ignore_index=True)
    print(f"\nGecombineerde per-dot data van alle participanten. Totaal aantal observaties (dots x PPs): {len(final_dot_summaries_all_pps_df)}")

    # --- A. Gemiddelde Checkerboard Heatmaps (over alle participanten) ---
    print("\nGenereren van gemiddelde checkerboard heatmaps over alle participanten...")
    if not final_dot_summaries_all_pps_df.empty:
        final_dot_summaries_all_pps_df['dot_id'] = pd.to_numeric(final_dot_summaries_all_pps_df['dot_id'], errors='coerce')
        final_dot_summaries_all_pps_df.dropna(subset=['dot_id'], inplace=True)
        if not final_dot_summaries_all_pps_df.empty:
             final_dot_summaries_all_pps_df['dot_id'] = final_dot_summaries_all_pps_df['dot_id'].astype(int)
        
        averaged_dot_metrics = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()

        if not averaged_dot_metrics.empty:
            plot_df_avg_checkerboard = pd.DataFrame({'dot_id': range(len(positions_to_fill_13_dots))})
            plot_df_avg_checkerboard = pd.merge(plot_df_avg_checkerboard, averaged_dot_metrics, on='dot_id', how='left')
            
            if len(averaged_dot_metrics) < len(positions_to_fill_13_dots):
                 print(f"  WAARSCHUWING: Gemiddelde data voor checkerboard heatmap beschikbaar voor {len(averaged_dot_metrics)} van de {len(positions_to_fill_13_dots)} verwachte unieke dot posities.")
            
            # Titels zoals in de originele webcam_AccPrec13.md (output_10_X.png)
            # De title_prefix wordt gebruikt om aan te geven dat het om EyeLink data gaat.
            plot_checkerboard_heatmap(plot_df_avg_checkerboard, measurement='median_offset_cm', title_prefix='EyeLink (Gem. Alle PPs)', positions_to_fill=positions_to_fill_13_dots)
            plot_checkerboard_heatmap(plot_df_avg_checkerboard, measurement='eucl_dist_gaze_to_median_cm', title_prefix='EyeLink (Gem. Alle PPs)', positions_to_fill=positions_to_fill_13_dots)
            plot_checkerboard_heatmap(plot_df_avg_checkerboard, measurement='rms_error_cm', title_prefix='EyeLink (Gem. Alle PPs)', positions_to_fill=positions_to_fill_13_dots)
        else:
            print("Geen data om gemiddelde checkerboard heatmaps te plotten na groeperen.")
    else:
        print("Geen gecombineerde data beschikbaar voor checkerboard heatmaps.")

    # --- B. Overige Gecombineerde Plots (identiek aan webcam output_14_X en output_16_X) ---
    print("\nGenereren van gecombineerde beschrijvende statistieken en andere plots...")
    if not final_dot_summaries_all_pps_df.empty:
        # B.1. Tabel met Beschrijvende Statistieken
        desc_stats_data_all = {
            'Metric': ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)'],
            'Mean': [
                final_dot_summaries_all_pps_df['median_offset_cm'].mean(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].mean(),
                final_dot_summaries_all_pps_df['rms_error_cm'].mean()
            ],
            'Std Dev': [
                final_dot_summaries_all_pps_df['median_offset_cm'].std(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].std(),
                final_dot_summaries_all_pps_df['rms_error_cm'].std()
            ],
            'Min': [
                final_dot_summaries_all_pps_df['median_offset_cm'].min(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].min(),
                final_dot_summaries_all_pps_df['rms_error_cm'].min()
            ],
            'Median': [
                final_dot_summaries_all_pps_df['median_offset_cm'].median(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].median(),
                final_dot_summaries_all_pps_df['rms_error_cm'].median()
            ],
            'Max': [
                final_dot_summaries_all_pps_df['median_offset_cm'].max(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].max(),
                final_dot_summaries_all_pps_df['rms_error_cm'].max()
            ]
        }
        overall_desc_stats_df = pd.DataFrame(desc_stats_data_all)
        # Tabel titel zoals in output_14_1.png van webcam analyse
        print(f"\nDescriptive Statistics for Offset, Precision (SD), and RMS Error (EyeLink - All PPs):")
        print(overall_desc_stats_df.to_string(index=False))

        # B.2. Staafdiagram van Gemiddelde Prestaties
        # Titel: "Average System Performance Metrics"
        plt.figure(figsize=(8, 6))
        barplot_data_overall = overall_desc_stats_df.set_index('Metric')
        metric_order = ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)']
        barplot_data_overall = barplot_data_overall.reindex(metric_order)
        sns.barplot(x=barplot_data_overall.index, y='Mean', data=barplot_data_overall, palette=['#1f77b4', '#ff7f0e', '#2ca02c'])
        plt.title('Average System Performance Metrics (EyeLink - All PPs)', fontsize=16)
        plt.ylabel('Value (cm)', fontsize=12)
        plt.xlabel('') 
        plt.xticks(ticks=range(len(barplot_data_overall.index)), labels=barplot_data_overall.index, rotation=0, ha='center', fontsize=10)
        plt.tight_layout()
        plt.show()

        # B.3. Overall Boxplot van Distributies (Offset, SD, RMS) - Stijl zoals image_741efa.png
        # Titels: "Distribution of [Metric] Values Across Calibration Points"
        metrics_for_overall_boxplot = {
            'median_offset_cm': ('Offset (cm)', 'skyblue', 'Distribution of Offset Values Across Calibration Points'),
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'lightcoral', 'Distribution of Precision (SD) Values Across Calibration Points'),
            'rms_error_cm': ('RMS Error (cm)', 'lightgreen', 'Distribution of RMS Error Values Across Calibration Points')
        }
        for metric_col, (ylabel_text, color, plot_title_text) in metrics_for_overall_boxplot.items():
            plt.figure(figsize=(5, 6))
            sns.boxplot(y=final_dot_summaries_all_pps_df[metric_col], color=color, width=0.5)
            plt.title(plot_title_text + ' (EyeLink - All PPs)', fontsize=14)
            plt.ylabel(ylabel_text, fontsize=12)
            plt.xlabel(f'{len(positions_to_fill_13_dots)} Calibration Point Types\n(All Participants & Dots Pooled)', fontsize=10) # Consistent met webcam plot stijl
            plt.xticks([]) 
            plt.tight_layout()
            plt.show()

        # B.4. Scatterplot van Offset vs. Precisie (SD) (overall)
        # Titel: "Accuracy (Offset) vs. Precision (SD) per Point"
        plt.figure(figsize=(8, 6))
        sns.scatterplot(x=final_dot_summaries_all_pps_df['median_offset_cm'], y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], alpha=0.5)
        plt.title('Accuracy (Offset) vs. Precision (SD) per Point (EyeLink - All PPs)', fontsize=16)
        plt.xlabel('Offset (cm)', fontsize=12)
        plt.ylabel('Precision (SD, cm)', fontsize=12)
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        # --- B.5. Plots: Metriek per dot across participants (Custom Stijl) ---
        print("\nGenereren van plots: metriek per doelpunt over participanten (custom stijl)...")
        metrics_to_plot_per_dot = {
            'median_offset_cm': ('Offset (cm)', 'Distribution of Offset Values per Dot ID'), # Base title, (EyeLink - All PPs) wordt toegevoegd
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'Distribution of Precision (SD) Values per Dot ID'),
            'rms_error_cm': ('RMS Error (cm)', 'Distribution of RMS Error Values per Dot ID')
        }
        
        # Bepaal de dot_ids die daadwerkelijk in de data aanwezig zijn voor de x-as
        actual_dot_ids_in_data = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())
        # We willen de x-as labelen met de dot_ids die we verwachten (0-12) als ze data hebben.
        # De 'order' parameter in boxplot/stripplot zal alleen de aanwezige hiervan tonen.
        dot_id_order_for_x_axis = [dot_id for dot_id in range(len(positions_to_fill_13_dots)) if dot_id in actual_dot_ids_in_data]

        if not final_dot_summaries_all_pps_df.empty and dot_id_order_for_x_axis:
            for metric_col, (ylabel_text, plot_title_base) in metrics_to_plot_per_dot.items():
                # Filter de data voor de zekerheid nogmaals op de dot_ids die we willen plotten
                plot_data_filtered = final_dot_summaries_all_pps_df[final_dot_summaries_all_pps_df['dot_id'].isin(dot_id_order_for_x_axis)]
                if not plot_data_filtered.empty:
                    plot_metric_per_dot_custom_style(plot_data_filtered, 
                                                     metric_col=metric_col, 
                                                     y_label=ylabel_text, 
                                                     base_title=plot_title_base, # De functie voegt zelf (EyeLink - All PPs) toe
                                                     dot_id_order=dot_id_order_for_x_axis)
                else:
                    print(f"Geen data om {metric_label} per dot te plotten na filteren op verwachte dot_ids.")
        else:
            print("Geen data beschikbaar voor 'per dot across participants' plots.")
    else:
        print("Geen gecombineerde data beschikbaar voor het genereren van plots.")
