import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm 
import matplotlib.patches as patches 
import os
import astropy.convolution as krn # Geïmporteerd in webcam notebook, mogeli<PERSON> niet direct gebruikt hier
import scipy.stats as stats      # Geïmporteerd in webcam notebook
import seaborn as sns
import statsmodels.api as sm     # Geïmporteerd in webcam notebook
from statsmodels.stats.anova import AnovaRM # Geïmporteerd in webcam notebook

# De import 'from deepeye_analysis_package.plotting import makeHeat' uit het webcam notebook
# wordt hier niet gebruikt omdat de checkerboard heatmaps met een lokale 'plot_matrix' worden gemaakt.

import openpyxl 
import xlrd     

# compute_rms zoals gedefinieerd in de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb)
def compute_rms(samples):
    # Assuming `samples` is a list of tuples, each containing (x, y) coordinates
    # Of een numpy array (N,2)
    samples_arr = np.array(samples)
    if samples_arr.ndim != 2 or samples_arr.shape[1] != 2:
        if samples_arr.ndim == 1 and len(samples_arr) % 2 == 0 and len(samples_arr) >= 4:
            samples_arr = samples_arr.reshape(-1, 2)
        else:
            return np.nan
            
    samples_cleaned_for_diff = samples_arr[~np.isnan(samples_arr).any(axis=1)]

    if samples_cleaned_for_diff.shape[0] < 2: 
        return np.nan
        
    deltas = np.diff(samples_cleaned_for_diff, axis=0)
    if deltas.shape[0] == 0: 
        return np.nan
        
    distances_sq = np.sum(deltas**2, axis=1)
    distances_sq_cleaned = distances_sq[~np.isnan(distances_sq)]
    if distances_sq_cleaned.size == 0:
        return np.nan
        
    rms = np.sqrt(np.mean(distances_sq_cleaned))
    return rms

# pixels_to_cm zoals gedefinieerd in de webcam notebook
def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):
    """Converts pixel values to centimeters."""
    pixels_numeric = pd.to_numeric(pixels, errors='coerce')
    if screen_dimension_pixels == 0 or physical_screen_dimension_cm == 0:
        return np.nan
    return (pixels_numeric / screen_dimension_pixels) * physical_screen_dimension_cm

# calculate_summary_stats zoals gedefinieerd in de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb, cell 8)
# Input df_dot verwacht kolommen 'gaze_x_cm' en 'gaze_y_cm'
def calculate_summary_stats(dot_id, df_dot, target_coords_cm):
    """
    Calculates median offset, precision (mean Euclidean distance to median gaze),
    and RMS error for a given target dot. From webcam notebook.
    """
    precision_col_name = 'eucl_dist_gaze_to_median_cm'

    gaze_points_for_dot = list(zip(df_dot['gaze_x_cm'], df_dot['gaze_y_cm']))

    if not gaze_points_for_dot:
        return pd.Series({
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            precision_col_name: np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': 0
        })

    gaze_array_cm = np.array(gaze_points_for_dot)
    gaze_array_cm_cleaned = gaze_array_cm[~np.isnan(gaze_array_cm).any(axis=1)]

    if gaze_array_cm_cleaned.shape[0] == 0:
        return pd.Series({
            'dot_id': dot_id,
            'median_offset_cm': np.nan,
            precision_col_name: np.nan,
            'rms_error_cm': np.nan,
            'num_fixations': len(gaze_points_for_dot)
        })

    median_gaze_x_cm = np.median(gaze_array_cm_cleaned[:, 0])
    median_gaze_y_cm = np.median(gaze_array_cm_cleaned[:, 1])
    median_gaze_coords_cm = np.array([median_gaze_x_cm, median_gaze_y_cm])

    target_coords_cm_arr = np.array(target_coords_cm)
    if np.isnan(target_coords_cm_arr).any() or np.isnan(median_gaze_coords_cm).any():
        offset_cm = np.nan
    else:
        offset_cm = np.linalg.norm(median_gaze_coords_cm - target_coords_cm_arr)

    if np.isnan(median_gaze_coords_cm).any():
        distances_to_median_cm = []
    else:
        distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm_cleaned]

    precision_val = np.mean(distances_to_median_cm) if distances_to_median_cm else np.nan
    rms_cm = compute_rms(gaze_array_cm_cleaned)

    return pd.Series({
        'dot_id': dot_id,
        'median_offset_cm': offset_cm,
        precision_col_name: precision_val,
        'rms_error_cm': rms_cm,
        'num_fixations': len(gaze_points_for_dot)
    })

# plot_matrix functie, exact overgenomen uit de webcam notebook (deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb, cell 8)
def plot_matrix(average_per_dot, measurement, title, positions_to_fill, cmap_str="YlGnBu"):
    """Plots a 5x5 checkerboard heatmap using the exact logic from the webcam notebook."""
    
    # Bepaal de cbar label op basis van de titel (zoals in webcam notebook)
    # De webcam notebook gaf 'measurement' mee aan cbar_kws, wat resulteert in de kolomnaam.
    # We passen dit aan om consistent te zijn met de gewenste output labels.
    if 'Offset (cm)' in title:
        cbar_label_text = 'Offset (cm)'
    elif 'SD (cm)' in title:
        cbar_label_text = 'SD (cm)'
    elif 'RMS error (cm)' in title:
        cbar_label_text = 'RMS error (cm)'
    else:
        cbar_label_text = measurement # Fallback

    dot_matrix = pd.DataFrame(np.full((5, 5), np.nan)) # Maak een 5x5 matrix met NaNs
    
    # average_per_dot is de DataFrame met metrieken per dot_id (0-12 voor 13 punten)
    # Zorg dat het gesorteerd is op dot_id voor correcte mapping met positions_to_fill
    df_sorted = average_per_dot.sort_values(by='dot_id').reset_index(drop=True)

    for idx, (row_idx, col_idx) in enumerate(positions_to_fill):
        if idx < len(df_sorted):
            # De originele notebook gebruikte dot_matrix.iloc[col, row] = value
            # Voor standaard DataFrame.iloc en sns.heatmap is (row_idx, col_idx) correct.
            # Ik neem aan dat (row, col) in 'positions_to_fill' (visual_row, visual_col) betekent.
            value = df_sorted.loc[df_sorted['dot_id'] == idx, measurement].values
            if len(value) > 0:
                 dot_matrix.iloc[row_idx, col_idx] = value[0]
            # Oude logica uit webcam notebook: dot_matrix.iloc[col_idx, row_idx] = df_sorted[measurement].values[idx]
            # Dit lijkt een vergissing in de transcriptie van de webcam snippet, 
            # .iloc[row, col] is standaard. Ik houd mijn standaard implementatie aan.

    plt.figure(figsize=(8, 8))
    heatmap_ax = sns.heatmap(dot_matrix, annot=True, fmt=".2f", cmap=cmap_str,
                             square=True, annot_kws={"size": 12}, linewidths=0.5,
                             linecolor='gray', cbar_kws={'label': cbar_label_text})
    
    plt.title(title, fontsize=16, weight='bold', pad=12)
    plt.xlabel('Column', fontsize=14, labelpad=10)
    plt.ylabel('Row', fontsize=14, labelpad=10)
    heatmap_ax.set_xticklabels(range(1, 6), fontsize=12)
    heatmap_ax.set_yticklabels(range(1, 6), fontsize=12, rotation=0)
    if heatmap_ax.collections: 
        heatmap_ax.collections[0].colorbar.ax.tick_params(labelsize=12)
    plt.tight_layout()
    plt.show()

# plot_metric_per_dot_webcam_style, om de stijl van image_73b5a0.png (webcam) te repliceren
def plot_metric_per_dot_webcam_style(data_df, metric_col, y_label, title_str, dot_id_order):
    """Genereert een boxplot per dot_id met individuele rode punten en alternerende achtergrondkleur."""
    
    plt.style.use('seaborn-v0_8-whitegrid') 
    fig, ax = plt.subplots(figsize=(14, 7)) 
    
    plot_data = data_df[data_df['dot_id'].isin(dot_id_order)]
    
    for i, _ in enumerate(dot_id_order):
        if i % 2 == 1: 
            ax.axvspan(i - 0.5, i + 0.5, facecolor='whitesmoke', alpha=1, zorder=0) 
            
    sns.boxplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order,
                color='white', width=0.7, showfliers=False, 
                medianprops={'color': 'black', 'linewidth': 2},
                boxprops={'edgecolor': 'black', 'linewidth': 1.5, 'facecolor':'white'},
                whiskerprops={'color': 'black', 'linewidth': 1.5},
                capprops={'color': 'black', 'linewidth': 1.5},
                ax=ax, zorder=2)
    
    sns.stripplot(x='dot_id', y=metric_col, data=plot_data, order=dot_id_order, 
                  color='red', jitter=0.15, alpha=0.8, size=6, ax=ax, zorder=1)
    
    ax.set_title(title_str, fontsize=16, weight='bold', pad=15)
    ax.set_xlabel('Dot ID', fontsize=14, labelpad=10)
    ax.set_ylabel(y_label, fontsize=14, labelpad=10)
    
    ax.set_xticks(ticks=range(len(dot_id_order)))
    ax.set_xticklabels(dot_id_order, fontsize=12)
    ax.tick_params(axis='y', labelsize=12)
    
    ax.grid(True, axis='y', linestyle=':', color='lightgray', zorder=-2) 
    ax.set_facecolor('white') 
    fig.patch.set_facecolor('white') 
    
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    plt.tight_layout()
    plt.show()

PHYSICAL_SCREEN_WIDTH_CM = 34.4
PHYSICAL_SCREEN_HEIGHT_CM = 19.3
EYELINK_SCREEN_WIDTH_PX = 1920 
EYELINK_SCREEN_HEIGHT_PX = 1080 

print(f"EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm")
print(f"EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm")
print(f"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px")

base_data_path = './CollectedData/eyelink_data/' 
print(f"Base data path for EyeLink data: {base_data_path}")

try:
    folder_names = [
        name for name in os.listdir(base_data_path)
        if os.path.isdir(os.path.join(base_data_path, name)) and not name.startswith('.')
    ]
    if not folder_names:
        print(f"WAARSCHUWING: Geen participant mappen gevonden in '{base_data_path}'.")
    else:
        print(f"Gevonden EyeLink participant mappen: {sorted(folder_names)}")
except FileNotFoundError:
    print(f"FOUT: Basis data pad niet gevonden: '{base_data_path}'.")
    folder_names = []

positions_to_fill_13_dots = [
    (0,0), (0,2), (0,4),  
    (1,1), (1,3),          
    (2,0), (2,2), (2,4),  
    (3,1), (3,3),          
    (4,0), (4,2), (4,4)   
]

all_participants_dot_summaries_list = []

if not folder_names:
    print("Geen participant mappen om te verwerken. Script stopt.")
else:
    for participant_id in sorted(folder_names): 
        print(f"\n--- Processing Participant: {participant_id} ---")
        
        file_path = os.path.join(base_data_path, participant_id, "Output", f"{participant_id}.xls")
        print(f"Pogen data te laden van: {file_path}")

        df_eyelink_raw = None
        loaded_successfully = False
        try:
            print(f"  Poging 1: Laden als TSV... {file_path}")
            df_eyelink_raw = pd.read_csv(file_path, sep='\t', low_memory=False)
            print(f"    Succesvol geladen als TSV. Vorm: {df_eyelink_raw.shape}")
            loaded_successfully = True
        except Exception:
            try:
                print(f"  Poging 2: Laden als CSV... {file_path}")
                df_eyelink_raw = pd.read_csv(file_path, low_memory=False)
                print(f"    Succesvol geladen als CSV. Vorm: {df_eyelink_raw.shape}")
                loaded_successfully = True
            except Exception:
                try:
                    print(f"  Poging 3: Laden als Excel (.xls) met xlrd... {file_path}")
                    df_eyelink_raw = pd.read_excel(file_path, engine='xlrd')
                    print(f"    Succesvol geladen als Excel (.xls) met xlrd. Vorm: {df_eyelink_raw.shape}")
                    loaded_successfully = True
                except Exception:
                    try:
                        print(f"  Poging 4: Laden als Excel (.xlsx style) met openpyxl... {file_path}")
                        df_eyelink_raw = pd.read_excel(file_path, engine='openpyxl')
                        print(f"    Succesvol geladen als Excel (.xlsx style) met openpyxl. Vorm: {df_eyelink_raw.shape}")
                        loaded_successfully = True
                    except Exception as e_final:
                        print(f"  KRITIEKE FOUT: Kon databestand niet laden voor {participant_id} ({e_final}). Sla over.")
                        continue 

        if not loaded_successfully or df_eyelink_raw is None:
            continue

        if 'arrowLength' not in df_eyelink_raw.columns:
            print(f"FOUT: Kolom 'arrowLength' niet gevonden voor {participant_id}. Sla over.")
            continue
        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)
        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith("UNDEFINED")].copy()

        if df_accprec.empty:
            print(f"Geen accuracy/precision data gevonden voor {participant_id}. Sla over.")
            continue
        
        required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']
        missing_cols = [col for col in required_cols if col not in df_accprec.columns]
        if missing_cols:
            print(f"FOUT: Vereiste kolommen {missing_cols} niet gevonden voor {participant_id}. Sla over.")
            continue
        for col in required_cols:
            df_accprec[col] = pd.to_numeric(df_accprec[col], errors='coerce')
        df_accprec.dropna(subset=required_cols, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na numerieke conversie voor {participant_id}. Sla over.")
            continue

        # Data voorbereiden voor calculate_summary_stats: 'gaze_x_cm' en 'gaze_y_cm'
        df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)
        df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)
        cm_cols_to_check = ['gaze_x_cm', 'gaze_y_cm', 'target_x_cm', 'target_y_cm']
        df_accprec.dropna(subset=cm_cols_to_check, inplace=True)
        if df_accprec.empty:
            print(f"Geen valide data na cm-conversie voor {participant_id}. Sla over.")
            continue

        df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)
        df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)
        unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]
        unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])
        unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)
        unique_targets_cm['dot_id'] = unique_targets_cm.index 
        num_unique_dots_found = len(unique_targets_cm)
        print(f"Aantal unieke doelposities gevonden voor {participant_id}: {num_unique_dots_found}")
        if num_unique_dots_found == 0:
            print(f"FOUT: Geen unieke doelposities gevonden voor {participant_id}. Sla over.")
            continue
        # if num_unique_dots_found != 13: # Waarschuwing is optioneel, de code gaat door met gevonden dots
        #      print(f"WAARSCHUWING voor {participant_id}: Verwachtte 13 unieke doelposities, gevonden {num_unique_dots_found}.")
        
        df_accprec_merged = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], 
                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')
        
        # Data per dot voorbereiden voor calculate_summary_stats van webcam notebook
        # Deze verwacht een df_dot met 'gaze_x_cm' en 'gaze_y_cm'
        results_per_dot_current_pp = []
        for dot_id_val in sorted(df_accprec_merged['dot_id'].unique()):
            if dot_id_val >= len(positions_to_fill_13_dots):
                continue # Zorg dat we binnen de 13 verwachte dots blijven voor consistentie

            df_dot_for_function = df_accprec_merged[df_accprec_merged['dot_id'] == dot_id_val]
            if df_dot_for_function.empty:
                # Maak een lege Series met NaNs als er geen data is voor deze dot_id, maar wel verwacht
                # Dit zorgt ervoor dat elke dot_id (0-12) een entry heeft voor aggregatie
                summary = pd.Series({
                    'dot_id': dot_id_val, 'median_offset_cm': np.nan,
                    'eucl_dist_gaze_to_median_cm': np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0
                })
            else:
                target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]
                if target_info.empty: 
                    # print(f"  WAARSCHUWING: Doelinformatie niet gevonden voor dot_id {dot_id_val}.")
                    summary = pd.Series({
                        'dot_id': dot_id_val, 'median_offset_cm': np.nan,
                        'eucl_dist_gaze_to_median_cm': np.nan, 'rms_error_cm': np.nan, 'num_fixations': 0
                    })
                else:
                    target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])
                    summary = calculate_summary_stats(dot_id_val, df_dot_for_function, target_coords_cm_actual)
            results_per_dot_current_pp.append(summary)
        
        if results_per_dot_current_pp:
            summary_df_per_dot_current_pp = pd.DataFrame(results_per_dot_current_pp)
            if not summary_df_per_dot_current_pp.empty and not summary_df_per_dot_current_pp[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].isnull().all().all():
                summary_df_per_dot_current_pp['participant_id'] = participant_id 
                all_participants_dot_summaries_list.append(summary_df_per_dot_current_pp)
                print(f"Statistieken per doelpunt berekend voor {participant_id}.")
            else:
                print(f"Kon geen (valide) statistieken per doelpunt berekenen voor {participant_id} (allemaal NaN of leeg)." )
        else:
            print(f"Geen data om statistieken per doelpunt te berekenen voor {participant_id}.")

print("\nAlle participanten verwerkt.")

if not all_participants_dot_summaries_list:
    print("Geen data van participanten succesvol verwerkt. Kan geen geaggregeerde plots genereren.")
else:
    final_dot_summaries_all_pps_df = pd.concat(all_participants_dot_summaries_list, ignore_index=True)
    print(f"\nGecombineerde per-dot data van alle participanten. Totaal aantal observaties (dots x PPs): {len(final_dot_summaries_all_pps_df)}")

    # --- A. Gemiddelde Checkerboard Heatmaps (over alle participanten) ---
    # Gebruik de 'plot_matrix' functie zoals gedefinieerd in de webcam notebook.
    print("\nGenereren van gemiddelde checkerboard heatmaps over alle participanten...")
    if not final_dot_summaries_all_pps_df.empty:
        final_dot_summaries_all_pps_df['dot_id'] = pd.to_numeric(final_dot_summaries_all_pps_df['dot_id'], errors='coerce')
        final_dot_summaries_all_pps_df.dropna(subset=['dot_id'], inplace=True)
        if not final_dot_summaries_all_pps_df.empty:
             final_dot_summaries_all_pps_df['dot_id'] = final_dot_summaries_all_pps_df['dot_id'].astype(int)
        
        # 'average_per_dot' is de naam die de plot_matrix functie in de webcam notebook verwachtte
        average_per_dot = final_dot_summaries_all_pps_df.groupby('dot_id')[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()

        if not average_per_dot.empty:
            # Zorg dat 'average_per_dot' alle dot_ids (0-12) heeft voor het plotten, vul met NaN indien nodig
            # Dit is belangrijk als sommige dots geen data hadden over alle participanten heen.
            expected_dot_ids_df = pd.DataFrame({'dot_id': range(len(positions_to_fill_13_dots))})
            average_per_dot_for_plot = pd.merge(expected_dot_ids_df, average_per_dot, on='dot_id', how='left')
            
            # Titels exact zoals in de webcam notebook (output_10_X.png)
            # De 'plot_matrix' functie uit de webcam notebook wordt hier aangeroepen.
            plot_matrix(average_per_dot_for_plot, measurement='median_offset_cm', title='Offset (cm) Across Positions', positions_to_fill=positions_to_fill_13_dots, cmap_str="YlGnBu")
            plot_matrix(average_per_dot_for_plot, measurement='eucl_dist_gaze_to_median_cm', title='SD (cm) Across Positions', positions_to_fill=positions_to_fill_13_dots, cmap_str="YlGnBu")
            plot_matrix(average_per_dot_for_plot, measurement='rms_error_cm', title='RMS error (cm) Across Positions', positions_to_fill=positions_to_fill_13_dots, cmap_str="YlGnBu")
        else:
            print("Geen data om gemiddelde checkerboard heatmaps te plotten na groeperen.")
    else:
        print("Geen gecombineerde data beschikbaar voor checkerboard heatmaps.")

    # --- B. Overige Gecombineerde Plots (stijl identiek aan webcam output_14_X en output_16_X) ---
    # Deze plots waren NIET in de deepeye_analyze_MullerLyer_EyeLink_AccPrec13.ipynb file zelf,
    # dus we repliceren hun stijl op basis van de voorbeeldafbeeldingen.
    print("\nGenereren van gecombineerde beschrijvende statistieken en andere plots...")
    if not final_dot_summaries_all_pps_df.empty:
        # B.1. Tabel met Beschrijvende Statistieken
        desc_stats_data_all = {
            'Metric': ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)'],
            'Mean': [
                final_dot_summaries_all_pps_df['median_offset_cm'].mean(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].mean(),
                final_dot_summaries_all_pps_df['rms_error_cm'].mean()
            ],
            'Std Dev': [
                final_dot_summaries_all_pps_df['median_offset_cm'].std(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].std(),
                final_dot_summaries_all_pps_df['rms_error_cm'].std()
            ],
            'Min': [
                final_dot_summaries_all_pps_df['median_offset_cm'].min(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].min(),
                final_dot_summaries_all_pps_df['rms_error_cm'].min()
            ],
            'Median': [
                final_dot_summaries_all_pps_df['median_offset_cm'].median(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].median(),
                final_dot_summaries_all_pps_df['rms_error_cm'].median()
            ],
            'Max': [
                final_dot_summaries_all_pps_df['median_offset_cm'].max(),
                final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'].max(),
                final_dot_summaries_all_pps_df['rms_error_cm'].max()
            ]
        }
        overall_desc_stats_df = pd.DataFrame(desc_stats_data_all)
        print(f"\nDescriptive Statistics for Offset, Precision (SD), and RMS Error (EyeLink - All PPs):")
        print(overall_desc_stats_df.to_string(index=False))

        # B.2. Staafdiagram van Gemiddelde Prestaties
        plt.figure(figsize=(8, 6))
        barplot_data_overall = overall_desc_stats_df.set_index('Metric')
        metric_order = ['Offset (cm)', 'Precision (SD, cm)', 'RMS Error (cm)']
        barplot_data_overall = barplot_data_overall.reindex(metric_order)
        sns.barplot(x=barplot_data_overall.index, y='Mean', data=barplot_data_overall, palette=['#1f77b4', '#ff7f0e', '#2ca02c'])
        plt.title('Average System Performance Metrics (EyeLink - All PPs)', fontsize=16)
        plt.ylabel('Value (cm)', fontsize=12)
        plt.xlabel('') 
        plt.xticks(ticks=range(len(barplot_data_overall.index)), labels=barplot_data_overall.index, rotation=0, ha='center', fontsize=10)
        plt.tight_layout()
        plt.show()

        # B.3. Overall Boxplot van Distributies (Offset, SD, RMS)
        metrics_for_overall_boxplot = {
            'median_offset_cm': ('Offset (cm)', 'skyblue', 'Distribution of Offset Values Across Calibration Points'),
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'lightcoral', 'Distribution of Precision (SD) Values Across Calibration Points'),
            'rms_error_cm': ('RMS Error (cm)', 'lightgreen', 'Distribution of RMS Error Values Across Calibration Points')
        }
        for metric_col, (ylabel_text, color, plot_title_text_base) in metrics_for_overall_boxplot.items():
            plt.figure(figsize=(5, 6)) 
            sns.boxplot(y=final_dot_summaries_all_pps_df[metric_col], color=color, width=0.5)
            plt.title(plot_title_text_base + ' (EyeLink - All PPs)', fontsize=14)
            plt.ylabel(ylabel_text, fontsize=12)
            plt.xlabel(f'{len(positions_to_fill_13_dots)} Calibration Points', fontsize=10) 
            plt.xticks([]) 
            plt.tight_layout()
            plt.show()

        # B.4. Scatterplot van Offset vs. Precisie (SD) (overall)
        plt.figure(figsize=(8, 6))
        sns.scatterplot(x=final_dot_summaries_all_pps_df['median_offset_cm'], y=final_dot_summaries_all_pps_df['eucl_dist_gaze_to_median_cm'], alpha=0.5)
        plt.title('Accuracy (Offset) vs. Precision (SD) per Point (EyeLink - All PPs)', fontsize=16)
        plt.xlabel('Offset (cm)', fontsize=12)
        plt.ylabel('Precision (SD, cm)', fontsize=12)
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        # --- B.5. Plots: Metriek per dot across participants (Custom Stijl) ---
        print("\nGenereren van plots: metriek per doelpunt over participanten (custom stijl)...")
        metrics_titles_per_dot = {
            'median_offset_cm': ('Offset (cm)', 'Distribution of Offset Values per Dot ID'), 
            'eucl_dist_gaze_to_median_cm': ('Precision (SD, cm)', 'Distribution of Precision (SD) Values per Dot ID'),
            'rms_error_cm': ('RMS Error (cm)', 'Distribution of RMS Error Values per Dot ID')
        }
        
        # Gebruik dot_ids 0 tot 12 (of het max aanwezige dot_id als < 12) voor de x-as
        # dot_id_order_for_plot = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())
        # dot_id_order_for_plot = [did for did in dot_id_order_for_plot if did < len(positions_to_fill_13_dots)]
        # Gebruik de dot_ids waarvoor we daadwerkelijk data hebben in de geaggregeerde set
        unique_dot_ids_in_final_summary = sorted(final_dot_summaries_all_pps_df['dot_id'].unique())
        # Filter deze verder om zeker te zijn dat ze binnen de 0-12 range vallen (voor 13 punten)
        dot_id_order_for_plot = [did for did in unique_dot_ids_in_final_summary if did < len(positions_to_fill_13_dots)]

        plot_data_for_dots = final_dot_summaries_all_pps_df[final_dot_summaries_all_pps_df['dot_id'].isin(dot_id_order_for_plot)]

        if not plot_data_for_dots.empty and dot_id_order_for_plot:
            for metric_col, (ylabel_text, plot_title_base) in metrics_titles_per_dot.items():
                plot_metric_per_dot_webcam_style(plot_data_for_dots, 
                                                 metric_col=metric_col, 
                                                 y_label=ylabel_text, 
                                                 title_str=plot_title_base, # De functie voegt (EyeLink - All PPs) zelf toe
                                                 dot_id_order=dot_id_order_for_plot)
        else:
            print("Geen data beschikbaar voor 'per dot across participants' plots.")
    else:
        print("Geen gecombineerde data beschikbaar voor het genereren van plots.")
