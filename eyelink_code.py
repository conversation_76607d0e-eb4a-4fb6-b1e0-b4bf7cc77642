import os
import ast
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.image as mpimg
import astropy.convolution as krn
import pandas as pd
import numpy as np
from matplotlib import cm
import scipy.stats as stats
import seaborn as sns
import statsmodels.api as sm
from statsmodels.stats.anova import AnovaRM
import csv
from deepeye_analysis_package.plotting import makeHeat

# Stap 1: Lees het juiste CSV-bestand in
try:
    df_raw = pd.read_csv('/Users/<USER>/Documents/Bewegingswetenschappen/BOP/MullerLyer_eyelink/CollectedData/complete/data/MLPP7/MLPP7_test_all.csv')
except FileNotFoundError:
    print("Zorg ervoor dat het bestand 'MLPP7_test_all.csv' in dezelfde map staat.")
    # Stop executie als het bestand niet gevonden is
    exit()

# Stap 2: Definieer de CORRECTE kolom-mapping
column_mapping = {
    'dotNr': 'unique_dot',
    'x': 'accprecX',
    'y': 'accprecY',
    'user_pred_px_x': 'user_pred_px_x',
    'user_pred_px_y': 'user_pred_px_y',
    'pp_id': 'pp_id'
}

# Selecteer en hernoem de basiskolommen
all_df = df_raw[list(column_mapping.keys())].rename(columns=column_mapping)

# Stap 3: Bereken de schaalfactor en voeg deze toe aan het DataFrame
# schaal = schermbreedte in cm / schermbreedte in pixels
try:
    scale_factor = df_raw['scrW_cm'].iloc[0] / df_raw['resX'].iloc[0]
    all_df['scale_cm_in_px'] = scale_factor
except (KeyError, IndexError) as e:
    print(f"Kon schaalfactor niet berekenen uit 'scrW_cm' en 'resX'. Fout: {e}")
    # Stel een standaardwaarde in als de kolommen ontbreken
    all_df['scale_cm_in_px'] = 0.026 # Standaardwaarde als fallback
    print("Standaard schaalfactor ingesteld.")


# Stap 4: Bepaal de schermresolutie uit de correcte kolommen
try:
    target_resX = df_raw['resX'].iloc[0]
    target_resY = df_raw['resY'].iloc[0]
except (KeyError, IndexError) as e:
    print(f"Kon schermresolutie niet bepalen uit 'resX' en 'resY'. Fout: {e}")
    # Stel een standaardwaarde in als de kolommen ontbreken
    target_resX = 1920
    target_resY = 1080
    print(f"Standaardresolutie ingesteld op {target_resX}x{target_resY}.")


def plot_heatmap(df, target_resX, target_resY):
    # Generate heatmap for all gaze points
    heatmap = makeHeat([target_resX, target_resY], df['user_pred_px_x'].values, df['user_pred_px_y'].values)
    
    # Calculate median gaze predictions and ground truth per dot
    median_pred = df.groupby('unique_dot')[['user_pred_px_x', 'user_pred_px_y']].median()
    median_pred.columns = ['median_pred_x', 'median_pred_y']
    
    true_pos = df.groupby('unique_dot')[['accprecX', 'accprecY']].mean()
    
    # Calculate offset between median gaze predictions and ground truth, then convert to cm
    offset_px = np.linalg.norm(median_pred.values - true_pos.values, axis=1)
    # new scaling factor is used to convert px to cm for scaled data
    offset_cm = offset_px * df['scale_cm_in_px'].iloc[0]
    
    # Calculate Euclidean distance from each gaze point to the median prediction for each dot
    df = df.merge(median_pred, on='unique_dot', how='left')

    df['eucl_dist_gaze_to_median_px'] = np.linalg.norm(
        df[['user_pred_px_x', 'user_pred_px_y']].values - df[['median_pred_x', 'median_pred_y']].values,
        axis=1
    )    
    
    # Calculate the mean Euclidean distance per dot and convert to cm for standard deviation (SD)
    SD_px = df.groupby('unique_dot')['eucl_dist_gaze_to_median_px'].mean()
    # new scaling factor is used to convert px to cm for scaled data
    SD_cm = SD_px * df['scale_cm_in_px'].iloc[0]

    # Plot heatmap and ground truth vs. predicted gaze points
    plt.figure()
    plt.imshow(heatmap, cmap=cm.hot, extent=[0, target_resX, target_resY, 0], alpha=0.5, aspect='equal')
    
    # Plot ground truth positions (green) and median predicted gaze (blue)
    plt.scatter(true_pos['accprecX'], true_pos['accprecY'], c='g', s=40, alpha=0.5, label='Ground Truth')
    plt.scatter(median_pred['median_pred_x'], median_pred['median_pred_y'], c='b', s=40, alpha=0.5, label='Predicted Gaze')
    
    # Draw lines between ground truth and predictions for visualizing offsets
    plt.plot([median_pred['median_pred_x'], true_pos['accprecX']], [median_pred['median_pred_y'], true_pos['accprecY']], c='black')
            
    # Title and statistics in the legend
    plt.title(f'N={df["pp_id"].nunique()}\n Offset: {offset_cm.mean():.1f}cm | SD: {SD_cm.mean():.1f}cm', fontsize=12)
    
    # Annotate offset values near each ground truth dot position
    for (x, y, e) in zip(true_pos['accprecX'], true_pos['accprecY'], offset_cm.round(1)):
        plt.text(x, y, str(e), fontsize=10, color='black')
    
    # Add legend and final touches
    plt.suptitle('Calibration (All Participants, EyeLink)', fontsize=14)
    plt.subplots_adjust(top=0.97)
    plt.legend(loc='upper right', bbox_to_anchor=(1.1, 1.2))
    
    # Show the plot
    plt.show()

# Call the function with the dataframe
plot_heatmap(all_df, target_resX, target_resY)